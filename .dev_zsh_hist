: 1747184826:0;ls
: 1747184828:0;pwd
: 1747184829:0;ls
: 1747184830:0;cd
: 1747184830:0;ls
: 1747184832:0;cd /hfr
: 1747184833:0;clear
: 1747184833:0;ls
: 1747184835:0;clear
: 1747184857:0;z modules/HAF
: 1747184865:0;git clone https://github.com/zsh-users/zsh-autosuggestions ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions
: 1747184869:0;cd /root/
: 1747184869:0;ls
: 1747184873:0;cd .oh-my-zsh
: 1747184873:0;ls
: 1747184875:0;cd plugins
: 1747184875:0;ls
: 1747184890:0;pwd
: 1747184891:0;cd ..
: 1747184891:0;ls
: 1747184894:0;cd custom/plugins/
: 1747184894:0;ls
: 1747184895:0;cd 
: 1747184896:0;ls
: 1747184902:0;git clone https://github.com/zsh-users/zsh-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-syntax-highlighting
: 1747185358:0;echo $ZSH_CUSTOM
: 1747185365:0;git clone https://github.com/agkozak/zsh-z $ZSH_CUSTOM/plugins/zsh-z
: 1747185373:0;cd $ZSH_CUSTOM
: 1747185374:0;ls
: 1747185376:0;cd plugins
: 1747185377:0;ls
: 1747185379:0;cd
: 1747185379:0;ls
: 1747185390:0;git clone https://github.com/romkatv/powerlevel10k.git $ZSH_CUSTOM/themes/powerlevel10k
: 1747185403:0;clear
: 1747185404:0;vi 
: 1747185413:0;pwd
: 1747185416:0;vi .zshrc
: 1747185485:0;source .zshrc
: 1747185578:0;clear
: 1747185584:0;ls
: 1747185586:0;cd /hfr
: 1747185599:0;z modules/HAF
: 1747185600:0;ls
: 1747185659:0;exit
: 1747185701:0;clear
: 1747185704:0;z /hfr/modules/HAF
: 1747185705:0;ls
: 1747185707:0;cd /hfr
: 1747185708:0;ls
: 1747185710:0;clear
: 1747185711:0;ls
: 1747185717:0;c
: 1747185725:0;vi ~/.zshrc
: 1747185767:0;source ~/.zshrc
: 1747185768:0;c
: 1747186907:0;exit
: 1747187328:0;ls
: 1747187332:0;cd modules/HAF/road_boundary_perception_track
: 1747187332:0;ll
: 1747187337:0;cd core/p
: 1747187340:0;cd core/private
: 1747187340:0;ll
: 1747187342:0;cd d
: 1747187344:0;cd data/
: 1747187345:0;ll
: 1747187347:0;cd ..
: 1747187348:0;ll
: 1747202082:0;cd ..
: 1747202085:0;cd /hfr
: 1747202086:0;ls
: 1747202088:0;cd build
: 1747202089:0;ls
: 1747202090:0;cd ..
: 1747202090:0;ls
: 1747202102:0;cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=Debug
: 1747202110:0;uname -m
: 1747202142:0;rm -rf build
: 1747202143:0;cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=Debug
: 1747202159:0;vi script/cmake/hfrPlatformSettings.cmake
: 1747202182:0;pwd
: 1747202211:0;Based on the CMake error, I need to check the content of hfrPlatformSettings.cmake to fix the if statement syntax issue. Let me examine the file:
: 1747202211:0;vi script/cmake/hfrPlatformSettings.cmake
: 1747202224:0;/^wq
: 1747202224:0;cat script/cmake/hfrPlatformSettings.cmake
: 1747202243:0;clear
: 1747202247:0;cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=Debug
: 1747202249:0;ls
: 1747202254:0;cd script
: 1747202254:0;ls
: 1747202257:0;pwd
: 1747202280:0;cd ..
: 1747202284:0;cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=Debug
: 1747202299:0;vi /hfr/build/CMakeFiles/CMakeOutput.log
: 1747202387:0;ls
: 1747202390:0;cd modules/HAF/road_boundary_perception_track
: 1747202391:0;ls
: 1747202392:0;cd ..
: 1747202394:0;ls
: 1747202558:0;cd script/cmake/
: 1747202558:0;ls
: 1747202565:0;vi hfrPlatformSettings.cmake
: 1747202928:0;exit
: 1747202955:0;echo $HASCO_ENV
: 1747202960:0;exit
