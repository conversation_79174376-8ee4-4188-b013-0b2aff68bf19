<!-- Generated by SpecStory -->

# 修改检测数据读取方式 (2025-05-21 01:54Z)

_**User**_

去除 @rbt_sim.cpp 中的从 bin 文件读取detect数据的代码，改为 参考 @hist_test.cpp 中的道路边界点云仿真生成的方式来获取检测数据

---

_**Assistant**_

<think><details><summary>Thought Process</summary>我需要修改 rbt_sim.cpp 文件，将从 bin 文件读取检测数据的部分替换为使用 hist_test.cpp 中的仿真生成方式。

具体来说，我需要：

1. 找到 rbt_sim.cpp 中从 bin 文件读取数据的代码部分
2. 研究 hist_test.cpp 中的 GenerateTestData 函数，了解如何仿真生成道路边界点云
3. 在 rbt_sim.cpp 中添加 GenerateTestData 函数，并替换原来的数据读取逻辑

首先，让我找到 rbt_sim.cpp 中从 bin 文件读取数据的代码：

```cpp
string bin_path = bin_folder + std::string("/") + bin_name_list[frm_cnt];
if (!PBI::ReadDebugInfo(bin_path, &detected_roads)) {
    LOG(ERROR) << "ReadDebugInfo error!";
    std::cout << "\033[1;31mReadDebugInfo error!\033[0m" << std::endl;
    continue;
}
```

我需要将这段代码替换为使用 GenerateTestData 函数生成仿真数据。

接下来，我会研究 hist_test.cpp 中的 GenerateTestData 函数，了解它的参数和功能：

```cpp
void GenerateTestData(PBI::RoadInfo *road_info, int num_points, float view_start, float view_end, int pattern = 0) {
    // ... 生成仿真数据的代码 ...
}
```

GenerateTestData 函数接受以下参数：
- road_info: 用于填充的道路信息结构体
- num_points: 生成的点数量
- view_start: 视野起始位置
- view_end: 视野结束位置
- pattern: 点分布模式，0表示直线路沿，1表示弯道路沿

我还注意到一个问题：rbt_sim.cpp 中有 linter 错误，提示缺少 `perception` 命名空间的声明或包含。我将确保修改后的代码解决这个问题。

现在，我将进行以下修改：

1. 将 GenerateTestData 函数从 hist_test.cpp 复制到 rbt_sim.cpp
2. 在 rbt_sim.cpp 的 main 函数中，替换原来读取 bin 文件的代码，改为调用 GenerateTestData 函数
3. 确保代码中包含必要的头文件和命名空间

修改后，主循环将不再遍历 bin 文件，而是生成一定数量的帧，每帧都使用仿真数据。</details></think>

---

# 修改rbt_sim.cpp使用仿真数据

我将修改代码，用仿真生成的道路边界点替代原来从bin文件读取的部分。

---

Error calling tool 'edit_file'.

---

首先，我需要查看代码并理解流程：

---

Read file: modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp

---

现在让我从hist_test.cpp文件中获取GenerateTestData函数：

---

Read file: modules/HAF/road_boundary_perception_track/core/private/test/hist_test.cpp

---

现在我将修改rbt_sim.cpp文件，添加GenerateTestData函数，并修改主函数循环中的数据获取方式：

---

```diff
  #include <fstream>
  #include <memory>
+ #include <random>
  #include "opencv2/opencv.hpp"
  
```

```diff
  #include "io_util.h"
  #include "localization_type.h"
+ #include "road_info.h"
+ #include "sensor.h"
  
  using namespace std;
```

```diff
  
  int start_frame = 0;    //start at a middle frame for debug
- int end_frame = 10000;    //end at a middle frame for debug
+ int end_frame = 100;    //end at a middle frame for debug
+ 
+ // 生成仿真的帧数
+ const int simulation_frames = 100;
+ // 每帧生成的路沿点数量
+ const int simulation_num_points = 80;
+ // 视野范围
+ const float simulation_view_start = 0.0f;
+ const float simulation_view_end = 80.0f;
+ // 模拟道路边界的数量（左右路沿）
+ const int simulation_road_num = 2;
  
  bool PathExists(const std::string &path) {
```

```diff
      std::cout << "可视化图像已保存: " << file_path << std::endl;
  }
+ 
+ /**
+  * @brief 生成模拟的道路边界数据用于测试
+  * 
+  * @param road_info 用于填充的道路信息结构体
+  * @param num_points 生成的点数量
+  * @param view_start 视野起始位置
+  * @param view_end 视野结束位置
+  * @param pattern 点分布模式：0-直线路沿，1-弯道路沿
+  * @param road_index 路沿的索引，用于区分左右路沿
+  */
+ void GenerateTestData(PBI::RoadInfo *road_info, int num_points, float view_start, float view_end, int pattern = 0, int road_index = 0) {
+     std::random_device rd;
+     std::mt19937 gen(rd());
+     
+     road_info->view_range_start = view_start;
+     road_info->view_range_end = view_end;
+     road_info->valid = true;
+     
+     // 限制点数不超过数组大小
+     const int max_points = 100; // PBI::Guardrail::point 数组大小
+     road_info->guardrail.num = std::min(num_points, max_points);
+     
+     // 根据不同模式生成道路边界点
+     switch (pattern) {
+         case 0: {
+             // 直线路沿
+             // 设置路沿的基准线 y = k*x + b
+             float k = 0.05f;  // 直线斜率
+             float b = (road_index == 0) ? 2.0f : -2.0f;  // 直线截距，区分左右路沿
+             float width = 0.5f; // 路沿宽度（法向分布的标准差）
+             
+             // 在x轴上均匀分布
+             std::uniform_real_distribution<float> dist_x(view_start, view_end);
+             
+             for (int i = 0; i < road_info->guardrail.num; ++i) {
+                 // 生成x坐标（沿道路方向均匀分布）
+                 float x = dist_x(gen);
+                 
+                 // 计算基准y坐标
+                 float base_y = k * x + b;
+                 
+                 // 在垂直于道路方向上添加高斯噪声，模拟路沿宽度
+                 std::normal_distribution<float> dist_y(base_y, width);
+                 float y = dist_y(gen);
+                 
+                 // 设置点坐标
+                 road_info->guardrail.point[i].x = x;
+                 road_info->guardrail.point[i].y = y;
+                 road_info->guardrail.point[i].z = 0;
+                 road_info->guardrail.point[i].intensity = 100;
+                 road_info->guardrail.point[i].timestamp_us = 0;
+             }
+             break;
+         }
+         case 1: {
+             // 弯道路沿（使用二次函数模拟）
+             // 设置路沿的基准曲线 y = a*x^2 + b*x + c
+             float range = view_end - view_start;
+             float mid_x = (view_start + view_end) / 2.0f;
+             float a = 0.005f;  // 曲线参数（控制弯曲程度）
+             float b = -2 * a * mid_x;  // 使曲线在中点处的切线水平
+             float c = (road_index == 0) ? 2.0f : -2.0f;  // 控制曲线整体高度，区分左右路沿
+             c = c - a * mid_x * mid_x - b * mid_x;  // 调整以使曲线在中点处通过目标高度
+             float width = 0.5f; // 路沿宽度
+             
+             // x坐标分布（稍微集中在弯道处）
+             std::piecewise_linear_distribution<float> dist_x({view_start, mid_x, view_end}, 
+                                                             [mid_x](double x) { return x > mid_x ? 1.2 : 1.0; });
+             
+             for (int i = 0; i < road_info->guardrail.num; ++i) {
+                 // 生成x坐标
+                 float x = dist_x(gen);
+                 
+                 // 计算基准y坐标（弯曲的路沿）
+                 float base_y = a * x * x + b * x + c;
+                 
+                 // 在法向方向上添加噪声模拟路沿宽度
+                 // 计算曲线在点x处的切线方向
+                 float tangent_x = 1.0f;
+                 float tangent_y = 2 * a * x + b;
+                 float norm = std::sqrt(tangent_x * tangent_x + tangent_y * tangent_y);
+                 
+                 // 法向方向
+                 float normal_x = -tangent_y / norm;
+                 float normal_y = tangent_x / norm;
+                 
+                 // 沿法向方向添加随机偏移
+                 std::normal_distribution<float> dist_offset(0, width);
+                 float offset = dist_offset(gen);
+                 
+                 float y = base_y + offset * normal_y;
+                 
+                 // 设置点坐标
+                 road_info->guardrail.point[i].x = x;
+                 road_info->guardrail.point[i].y = y;
+                 road_info->guardrail.point[i].z = 0;
+                 road_info->guardrail.point[i].intensity = 100;
+                 road_info->guardrail.point[i].timestamp_us = 0;
+             }
+             break;
+         }
+         default: {
+             // 默认生成均匀分布的点
+             std::uniform_real_distribution<float> dist_x(view_start, view_end);
+             std::uniform_real_distribution<float> dist_y(-3.0, 3.0);
+             
+             for (int i = 0; i < road_info->guardrail.num; ++i) {
+                 road_info->guardrail.point[i].x = dist_x(gen);
+                 road_info->guardrail.point[i].y = dist_y(gen);
+                 road_info->guardrail.point[i].z = 0;
+                 road_info->guardrail.point[i].intensity = 100;
+                 road_info->guardrail.point[i].timestamp_us = 0;
+             }
+             break;
+         }
+     }
+ }
  
  int main(int argc, char *argv[]) {
      hLogRegister();
  
-     // load data
-     std::vector<string> bin_name_list;
-     GetFilenames(bin_folder, "bin", bin_name_list); //以bin文件为准
-     std::sort(bin_name_list.begin(), bin_name_list.end());
-     std::vector<string> pose_name_list;
-     GetFilenames(pose_folder, "txt", pose_name_list);
-     std::sort(pose_name_list.begin(), pose_name_list.end());
- 
-     //track
-     unique_ptr<perception::rbt::RoadTracker> p_road_tracker;
+     // 创建输出目录
+     mkdir(result_folder, 00755);
+     mkdir(visual_folder, 00755);  // 创建可视化结果文件夹
+ 
+     // 初始化跟踪器
+     std::unique_ptr<perception::rbt::RoadTracker> p_road_tracker;
      perception::rbt::RoadTrackerInitOptions options;
      options.enable_predict_road = true;
```

```diff
      p_road_tracker = std::make_unique<perception::rbt::RoadTracker>(options);
  
+     // 初始化定位信息
      HBI::LocalizationEstimate localization_message{};
      std::string location_frame_id = "odom";
      strcpy(localization_message.hbi_header.frame_id, location_frame_id.c_str());
  
+     // 初始化道路信息
      PBI::RoadInfoArray detected_roads{};
      PBI::RoadInfoArray tracked_roads{};
  
-     mkdir(result_folder, 00755);
-     mkdir(visual_folder, 00755);  // 创建可视化结果文件夹
+     // 选择道路模式：0-直线，1-弯道
+     int road_pattern = 1;
      
-     int end_num = std::min(end_frame, (int) bin_name_list.size());
-     for (int frm_cnt = start_frame; frm_cnt < end_num; frm_cnt++) {
-         LOG(INFO) << "Index:" << frm_cnt << " File:" << bin_name_list[frm_cnt].data();
-         std::cout << std::endl << "\033[1;32mIndex\033[0m:" << frm_cnt << " File:" << bin_name_list[frm_cnt].data()
-                   << std::endl;
+     // 在模拟帧范围内执行跟踪
+     for (int frm_cnt = 0; frm_cnt < simulation_frames; frm_cnt++) {
+         LOG(INFO) << "Simulation frame: " << frm_cnt;
+         std::cout << std::endl << "\033[1;32mSimulation frame\033[0m:" << frm_cnt << std::endl;
  
-         // 读取det结果
-         string bin_path = bin_folder + std::string("/") + bin_name_list[frm_cnt];
-         if (!PBI::ReadDebugInfo(bin_path, &detected_roads)) {
-             LOG(ERROR) << "ReadDebugInfo error!";
-             std::cout << "\033[1;31mReadDebugInfo error!\033[0m" << std::endl;
-             continue;
-         }
- 
-         std::string bin_name;
-         GetFilename(bin_path, bin_name);
-         double lidar_timestamp_s = GetTimestampByFilename(bin_name);
-         auto lidar_timestamp_us = static_cast<uint64_t>(lidar_timestamp_s * 1e6);
- 
-         // 读取pose文件
-         std::string pose_name = bin_name.replace(bin_name.find("bin"), -1, "txt");
-         std::string pose_path = pose_folder + std::string("/") + pose_name;
-         if (!ReadPoseFromHascoTXT(pose_path, &localization_message)) {
-             LOG(ERROR) << "Failed to load pose, disable tracking pipeline.";
-             std::cout << "\033[1;31mFailed to load pose, disable tracking pipeline.\033[0m" << std::endl;
-             continue;
-         }
+         // 生成模拟的检测数据
+         for (int i = 0; i < simulation_road_num && i < 4; ++i) {  // 最多生成4条路沿
+             GenerateTestData(&detected_roads.road[i], simulation_num_points, 
+                            simulation_view_start, simulation_view_end, 
+                            road_pattern, i);
+         }
+ 
+         // 设置时间戳
+         uint64_t timestamp_us = static_cast<uint64_t>(frm_cnt * 100000);  // 假设每帧间隔100ms
+         
+         // 设置模拟的定位信息
          localization_message.hbi_header.seq = static_cast<uint32_t>(frm_cnt);
-         localization_message.hbi_header.timestamp_us = lidar_timestamp_us;
- 
-         // track
+         localization_message.hbi_header.timestamp_us = timestamp_us;
+         localization_message.pose.position.x = 0.0;  // 模拟车辆位置
+         localization_message.pose.position.y = 0.0;
+         localization_message.pose.position.z = 0.0;
+         localization_message.pose.orientation.qx = 0.0;  // 四元数表示无旋转
+         localization_message.pose.orientation.qy = 0.0;
+         localization_message.pose.orientation.qz = 0.0;
+         localization_message.pose.orientation.qw = 1.0;
+         localization_message.pose.heading = 0.0;  // 朝向前方
+ 
+         // 执行跟踪
          p_road_tracker->Track(&localization_message, &detected_roads, &tracked_roads);
  
-         // save track result
+         // 保存结果
          char save_path[256];
  
-         std::string result_file_name;
-         GetFilename(bin_path, result_file_name);
-         result_file_name.replace(result_file_name.find("bin"), -1, "pntbin");
-         sprintf(save_path, "%s/%s", result_folder, result_file_name.c_str());
+         // 保存护栏点
+         sprintf(save_path, "%s/sim_%06d.pntbin", result_folder, frm_cnt);
          SaveGuardrail(save_path, &detected_roads, &tracked_roads);
  
-         GetFilename(bin_path, result_file_name);
-         result_file_name.replace(result_file_name.find("bin"), -1, "trk");
-         sprintf(save_path, "%s/%s", result_folder, result_file_name.c_str());
+         // 保存跟踪结果
+         sprintf(save_path, "%s/sim_%06d.trk", result_folder, frm_cnt);
          PBI::SaveResult(save_path, &tracked_roads);
          
-         // 可视化护栏点
-         GetFilename(bin_path, result_file_name);
-         result_file_name.replace(result_file_name.find("bin"), -1, "png");
-         sprintf(save_path, "%s/%s", visual_folder, result_file_name.c_str());
+         // 可视化结果
+         sprintf(save_path, "%s/sim_%06d.png", visual_folder, frm_cnt);
          VisualizeGuardrail(save_path, &detected_roads, &tracked_roads, &localization_message);
      }
  
-     printf("Done!\n");
+     printf("Simulation Done!\n");
      hLogUnRegister();
      return 0;
```

---

让我检查一下修改后的代码是否存在linter错误：

---

```bash
cd modules/HAF/road_boundary_perception_track/core/private/test && g++ -Wall -c rbt_sim.cpp
```

```
root@in-x86-dev-docker:/hfr# cd modules/HAF/road_boundary_perception_track/core/private/test &&
 g++ -Wall -c rbt_sim.cpp
rbt_sim.cpp:11:10: fatal error: opencv2/opencv.hpp: No such file or directory
 #include "opencv2/opencv.hpp"
          ^~~~~~~~~~~~~~~~~~~~
compilation terminated.
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/test#
```

---

看起来是路径问题，我们需要修正OpenCV的包含路径：

---

```diff
  #include <memory>
  #include <random>
- #include "opencv2/opencv.hpp"
+ #include <opencv2/opencv.hpp>
  
  #include "hlog.hpp"
```

---

让我们尝试添加适当的编译选项，包括OpenCV和其他必要的库：

---

```bash
cd modules/HAF/road_boundary_perception_track/core/private/test && g++ -Wall -c rbt_sim.cpp -I/hfr/3rdparty/opencv/x86_64/include
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/test# cd mo
dules/HAF/road_boundary_perception_track/core/private/test && g++ -Wall -c rbt_sim.cpp -I/hfr/3
rdparty/opencv/x86_64/include
bash: cd: modules/HAF/road_boundary_perception_track/core/private/test: No such file or directo
ry
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/test#
```

---

我们已经在正确的目录中了，修正命令：

---

```bash
g++ -Wall -c rbt_sim.cpp -I/hfr/3rdparty/opencv/x86_64/include
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/test# g++ -
Wall -c rbt_sim.cpp -I/hfr/3rdparty/opencv/x86_64/include
rbt_sim.cpp:13:10: fatal error: hlog.hpp: No such file or directory
 #include "hlog.hpp"
          ^~~~~~~~~~
compilation terminated.
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/test#
```

---

看起来还需要添加其他包含路径。我们先看看是否有相关的编译脚本可以参考：

---

<details>
            <summary>Listed directory modules/HAF/road_boundary_perception_track/core/private/test • **7** results</summary>
        
| Name |
|-------|
| 📄 `rbt_sim.cpp` |
| 📁 `data` |
| 📁 `build` |
| 📄 `hist_test.cpp` |
| 📄 `CMakeLists.txt` |
| 📄 `rbt_test.cpp` |
| 📄 `io_util_test.cpp` |

</details>

---

我们看一下CMakeLists.txt文件以了解项目的编译设置：

---

Read file: modules/HAF/road_boundary_perception_track/core/private/test/CMakeLists.txt

---

现在我们了解了需要添加的包含路径，让我们更新rbt_sim.cpp文件，确保它使用正确的包含路径，并编译成功。首先我们需要了解完整的命名空间和头文件问题：

---

Read file: modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp

---

现在让我们看一下RoadTracker的头文件和namespace情况：

---

<details>
            <summary>Listed directory modules/HAF/road_boundary_perception_track/core • **2** results</summary>
        
| Name |
|-------|
| 📁 `private` |
| 📁 `public` |

</details>

---

<details>
            <summary>Listed directory modules/HAF/road_boundary_perception_track/core/public • **1** results</summary>
        
| Name |
|-------|
| 📄 `.gitkeep` |

</details>

---

<details>
            <summary>Listed directory modules/HAF/road_boundary_perception_track/core/private • **12** results</summary>
        
| Name |
|-------|
| 📁 `test` |
| 📁 `.git` |
| 📁 `build` |
| 📄 `CMakeLists.txt` |
| 📁 `common` |
| 📁 `data` |
| 📁 `tools` |
| 📁 `RBT` |
| 📁 `docs` |
| 📁 `resource` |
| 📄 `.gitignore` |
| 📄 `.gitkeep` |

</details>

---

<details>
            <summary>Listed directory modules/HAF/road_boundary_perception_track/core/private/RBT • **13** results</summary>
        
| Name |
|-------|
| 📄 `road_tracker.cpp` |
| 📄 `tracked_road.cpp` |
| 📄 `tracked_road.h` |
| 📄 `target.cpp` |
| 📄 `target.h` |
| 📄 `road_type.h` |
| 📄 `road_frame.h` |
| 📄 `qr.h` |
| 📄 `qr.cpp` |
| 📄 `kalman_filter.cpp` |
| 📄 `kalman_filter.h` |
| 📄 `road_tracker.h` |
| 📄 `CMakeLists.txt` |

</details>

---

让我们查看road_tracker.h文件来理解其命名空间：

---

Read file: modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.h

---

让我们查看common目录下的文件，找到有用的头文件：

---

<details>
            <summary>Listed directory modules/HAF/road_boundary_perception_track/core/private/common • **4** results</summary>
        
| Name |
|-------|
| 📄 `sensor.h` |
| 📄 `CMakeLists.txt` |
| 📄 `io_util.h` |
| 📄 `road_info.h` |

</details>

---

Read file: modules/HAF/road_boundary_perception_track/core/private/common/io_util.h

---

基于以上信息，让我对rbt_sim.cpp进行最终修改，添加正确的命名空间和头文件包含路径：

---

```diff
  
  #include "hlog.hpp"
- #include "road_tracker.h"
- #include "io_util.h"
+ #include "../RBT/road_tracker.h"
+ #include "../common/io_util.h"
+ #include "../common/road_info.h"
+ #include "../common/sensor.h"
  #include "localization_type.h"
- #include "road_info.h"
- #include "sensor.h"
  
  using namespace std;
+ // 使用命名空间
+ using namespace perception::rbt;
  
  const char bin_folder[] = "../data/1708844416_1708844532/output";
```

```diff
  
      // 初始化跟踪器
-     std::unique_ptr<perception::rbt::RoadTracker> p_road_tracker;
-     perception::rbt::RoadTrackerInitOptions options;
+     std::unique_ptr<RoadTracker> p_road_tracker;
+     RoadTrackerInitOptions options;
      options.enable_predict_road = true;
      auto &target_param = options.target_param;
```

```diff
  //    target_param.process_variance = 1.0;
  //    target_param.curvature_rate_diff_threshold = 0.0;
-     p_road_tracker = std::make_unique<perception::rbt::RoadTracker>(options);
+     p_road_tracker = std::make_unique<RoadTracker>(options);
  
      // 初始化定位信息
```

---

这些linter错误看起来很奇怪，因为它们说参数类型已经匹配了。我们可能需要继续尝试编译来看看实际情况：

---

```bash
cd ../.. && cmake . && make rbt_sim
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/test# cd ..
/.. && cmake . && make rbt_sim
CMake Error: The source directory "/hfr/modules/HAF/road_boundary_perception_track/core" does n
ot appear to contain CMakeLists.txt.
Specify --help for usage, or press the help button on the CMake GUI.
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core#
```

---

让我们在正确的目录中尝试编译测试文件：

---

```bash
cd private && cmake . && make
```

```
-- The C compiler identification is GNU 7.5.0
-- The CXX compiler identification is GNU 7.5.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
HBI_VERSION=1.39

Start glob configure cmake
Search Done: /hfr/lib

Start glob library
    ==> find library: adolc, And create variable: ADOLC_DIR , ADOLC_INCLUDE_DIR , ADOLC_LIBRARY
_DIR , ADOLC_LIBRARIES
    ==> find library: boost, And create variable: BOOST_DIR , BOOST_INCLUDE_DIR , BOOST_LIBRARY
_DIR , BOOST_LIBRARIES
    ==> find library: cjson, And create variable: CJSON_DIR , CJSON_INCLUDE_DIR , CJSON_LIBRARY
_DIR , CJSON_LIBRARIES
    ==> find library: colpack, And create variable: COLPACK_DIR , COLPACK_INCLUDE_DIR , COLPACK
_LIBRARY_DIR , COLPACK_LIBRARIES
    ==> find library: dlt, And create variable: DLT_DIR , DLT_INCLUDE_DIR , DLT_LIBRARY_DIR , D
LT_LIBRARIES
    ==> find library: eigen, And create variable: EIGEN_DIR , EIGEN_INCLUDE_DIR , EIGEN_FOUND 
    ==> find library: flann, And create variable: FLANN_DIR , FLANN_INCLUDE_DIR , FLANN_LIBRARY
_DIR , FLANN_LIBRARIES
    ==> find library: gflags, And create variable: GFLAGS_DIR , GFLAGS_INCLUDE_DIR , GFLAGS_LIB
RARY_DIR , GFLAGS_LIBRARIES
    ==> find library: gfortran, And create variable: GFORTRAN_DIR , GFORTRAN_INCLUDE_DIR , GFOR
TRAN_LIBRARY_DIR , GFORTRAN_LIBRARIES
    ==> find library: glog, And create variable: GLOG_DIR , GLOG_INCLUDE_DIR , GLOG_LIBRARY_DIR
 , GLOG_LIBRARIES
    ==> find library: ipopt, And create variable: IPOPT_DIR , IPOPT_INCLUDE_DIR , IPOPT_LIBRARY
_DIR , IPOPT_LIBRARIES
    ==> find library: lapack, And create variable: LAPACK_DIR , LAPACK_INCLUDE_DIR , LAPACK_LIB
RARY_DIR , LAPACK_LIBRARIES
    ==> find library: mosquitto, And create variable: MOSQUITTO_DIR , MOSQUITTO_INCLUDE_DIR , M
OSQUITTO_LIBRARY_DIR , MOSQUITTO_LIBRARIES
    ==> find library: opencv, And create variable: OPENCV_DIR , OPENCV_INCLUDE_DIR , OPENCV_LIB
RARY_DIR , OPENCV_LIBRARIES
    ==> find library: openssl, And create variable: OPENSSL_DIR , OPENSSL_INCLUDE_DIR , OPENSSL
_LIBRARY_DIR , OPENSSL_LIBRARIES
    ==> find library: osqp, And create variable: OSQP_DIR , OSQP_INCLUDE_DIR , OSQP_LIBRARY_DIR
 , OSQP_LIBRARIES
    ==> find library: pcl, And create variable: PCL_DIR , PCL_INCLUDE_DIR , PCL_LIBRARY_DIR , P
CL_LIBRARIES
    ==> find library: protobuf, And create variable: PROTOBUF_DIR , PROTOBUF_INCLUDE_DIR , PROT
OBUF_LIBRARY_DIR , PROTOBUF_LIBRARIES
    ==> find library: rs_driver, And create variable: RS_DRIVER_DIR , RS_DRIVER_INCLUDE_DIR , R
S_DRIVER_LIBRARY_DIR , RS_DRIVER_LIBRARIES
    ==> find library: yaml, And create variable: YAML_DIR , YAML_INCLUDE_DIR , YAML_LIBRARY_DIR
 , YAML_LIBRARIES
    ==> find library: autocalib, And create variable: AUTOCALIB_DIR , AUTOCALIB_INCLUDE_DIR , A
UTOCALIB_LIBRARY_DIR , AUTOCALIB_LIBRARIES
    ==> find library: components_and_data_center, And create variable: COMPONENTS_AND_DATA_CENT
ER_DIR , COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR , COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR , COMP
ONENTS_AND_DATA_CENTER_LIBRARIES
    ==> find library: control, And create variable: CONTROL_DIR , CONTROL_INCLUDE_DIR , CONTROL
_LIBRARY_DIR , CONTROL_LIBRARIES
    ==> find library: execution_manifest, And create variable: EXECUTION_MANIFEST_DIR , EXECUTI
ON_MANIFEST_INCLUDE_DIR , EXECUTION_MANIFEST_LIBRARY_DIR , EXECUTION_MANIFEST_LIBRARIES
    ==> find library: fusion_localization_cargovehicle, And create variable: FUSION_LOCALIZATIO
N_CARGOVEHICLE_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_INCLUDE_DIR , FUSION_LOCALIZATION_CARGOVE
HICLE_LIBRARY_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_LIBRARIES
    ==> find library: hlog, And create variable: HLOG_DIR , HLOG_INCLUDE_DIR , HLOG_LIBRARY_DIR
 , HLOG_LIBRARIES
    ==> find library: iceoryx, And create variable: ICEORYX_DIR , ICEORYX_INCLUDE_DIR , ICEORYX
_LIBRARY_DIR , ICEORYX_LIBRARIES
    ==> find library: lidar_obstacle_perception_detect, And create variable: LIDAR_OBSTACLE_PER
CEPTION_DETECT_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_D
ETECT_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_LIBRARIES
    ==> find library: lidar_obstacle_perception_fusion, And create variable: LIDAR_OBSTACLE_PER
CEPTION_FUSION_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_F
USION_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_LIBRARIES
    ==> find library: lidar_obstacle_perception_segment, And create variable: LIDAR_OBSTACLE_PE
RCEPTION_SEGMENT_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTIO
N_SEGMENT_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_LIBRARIES
    ==> find library: lidar_obstacle_perception_track, And create variable: LIDAR_OBSTACLE_PERC
EPTION_TRACK_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_TRAC
K_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_LIBRARIES
    ==> find library: map, And create variable: MAP_DIR , MAP_INCLUDE_DIR , MAP_LIBRARY_DIR , M
AP_LIBRARIES
    ==> find library: planning, And create variable: PLANNING_DIR , PLANNING_INCLUDE_DIR , PLAN
NING_LIBRARY_DIR , PLANNING_LIBRARIES
    ==> find library: road_boundary_perception_track, And create variable: ROAD_BOUNDARY_PERCEP
TION_TRACK_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LI
BRARY_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARIES
    ==> find library: vsomeip, And create variable: VSOMEIP_DIR , VSOMEIP_INCLUDE_DIR , VSOMEIP
_LIBRARY_DIR , VSOMEIP_LIBRARIES
==> copy from: /hfr/3rdparty/dlt/x86_64/lib
==> copy to: /usr/lib/libdlt.so
==> copy to: /usr/lib/libdlt.so.2
==> copy to: /usr/lib/libdlt.so.2.18.9
-- SRC_DIR_LIST = /hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./kalman_fil
ter.cpp;/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./qr.cpp;/hfr/modules/
HAF/road_boundary_perception_track/core/private/RBT/./road_tracker.cpp;/hfr/modules/HAF/road_bo
undary_perception_track/core/private/RBT/./target.cpp;/hfr/modules/HAF/road_boundary_perception
_track/core/private/RBT/./tracked_road.cpp
-- Configuring done
-- Generating done
-- Build files have been written to: /hfr/modules/HAF/road_boundary_perception_track/core/priva
te
Scanning dependencies of target io_util_test
[ 10%] Building CXX object CMakeFiles/io_util_test.dir/test/io_util_test.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/io_util_test.cpp: In function
 ‘double GetTimestampByFilename(const string&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/io_util_test.cpp:80:23: warni
ng: comparison between signed and unsigned integer expressions [-Wsign-compare]
     if (underline_pos != file_name_without_extension.npos) {
[ 20%] Linking CXX executable io_util_test
[ 20%] Built target io_util_test
Scanning dependencies of target core_rbt
[ 30%] Building CXX object RBT/CMakeFiles/core_rbt.dir/kalman_filter.cpp.o
[ 40%] Building CXX object RBT/CMakeFiles/core_rbt.dir/qr.cpp.o
[ 50%] Building CXX object RBT/CMakeFiles/core_rbt.dir/road_tracker.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp: In static me
mber function ‘static void perception::rbt::RoadTracker::ComputeHistogram(const PBI::RoadInfo*,
 double, std::map<double, int>&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp:24:23: warnin
g: comparison between signed and unsigned integer expressions [-Wsign-compare]
     for (int i = 0; i < det->guardrail.num; ++i) {
                     ~~^~~~~~~~~~~~~~~~~~~~
[ 60%] Building CXX object RBT/CMakeFiles/core_rbt.dir/target.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp: In member function
 ‘bool perception::rbt::Target::SortPoint(PBI::Guardrail&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp:278:1: warning: no 
return statement in function returning non-void [-Wreturn-type]
 }
 ^
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp: In member function
 ‘bool perception::rbt::Target::InitKalmanFilter(const std::vector<perception::rbt::Point2D>&)’
:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp:296:1: warning: no 
return statement in function returning non-void [-Wreturn-type]
 }
 ^
[ 70%] Building CXX object RBT/CMakeFiles/core_rbt.dir/tracked_road.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/tracked_road.cpp: In member fu
nction ‘void perception::rbt::TrackedRoad::ComputeShapeFeatures(int, double, double)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/tracked_road.cpp:102:23: warni
ng: comparison between signed and unsigned integer expressions [-Wsign-compare]
     for (int i = 0; i < road_info.guardrail.num; ++i) {
                     ~~^~~~~~~~~~~~~~~~~~~~~~~~~
[ 80%] Linking CXX shared library libcore_rbt.so
[ 80%] Built target core_rbt
Scanning dependencies of target rbt_test_node
[ 90%] Building CXX object CMakeFiles/rbt_test_node.dir/test/rbt_test.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘do
uble GetTimestampByFilename(const string&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:110:23: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
     if (underline_pos != file_name_without_extension.npos) {
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘vo
id SaveGuardrail(const string&, const PBI::RoadInfoArray*, const PBI::RoadInfoArray*)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:127:31: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:141:31: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘vo
id VisualizeGuardrail(const string&, const PBI::RoadInfoArray*, const PBI::RoadInfoArray*, cons
t HBI::LocalizationEstimate*)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:335:31: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:365:31: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
[100%] Linking CXX executable rbt_test_node
[100%] Built target rbt_test_node

```

---

现在让我们修改CMakeLists.txt，添加rbt_sim测试程序的编译：

---

Read file: modules/HAF/road_boundary_perception_track/core/private/CMakeLists.txt

---

现在让我们修改CMakeLists.txt，添加rbt_sim.cpp的编译：

---

```diff
          ${OpenCV_LIBS}  # 添加OpenCV库链接
          )
+ 
+ ## test scope: RBT Simulation
+ add_executable(rbt_sim ./test/rbt_sim.cpp)
+ ## Specify libraries to link a library or executable target against
+ target_link_libraries(rbt_sim
+         core_rbt
+         ${DLT_LIBRARIES}
+         ${HLOG_LIBRARIES}
+         ${OpenCV_LIBS}  # 添加OpenCV库链接
+         )
  
```

---

现在让我们编译并运行新的测试程序：

---

```bash
cd /hfr/modules/HAF/road_boundary_perception_track/core/private && mkdir -p build && cd build && cmake .. && make
```

```
HBI_VERSION=1.39

Start glob configure cmake
Search Done: /hfr/lib

Start glob library
    ==> find library: adolc, And create variable: ADOLC_DIR , ADOLC_INCLUDE_DIR , ADOLC_LIBRARY
_DIR , ADOLC_LIBRARIES
    ==> find library: boost, And create variable: BOOST_DIR , BOOST_INCLUDE_DIR , BOOST_LIBRARY
_DIR , BOOST_LIBRARIES
    ==> find library: cjson, And create variable: CJSON_DIR , CJSON_INCLUDE_DIR , CJSON_LIBRARY
_DIR , CJSON_LIBRARIES
    ==> find library: colpack, And create variable: COLPACK_DIR , COLPACK_INCLUDE_DIR , COLPACK
_LIBRARY_DIR , COLPACK_LIBRARIES
    ==> find library: dlt, And create variable: DLT_DIR , DLT_INCLUDE_DIR , DLT_LIBRARY_DIR , D
LT_LIBRARIES
    ==> find library: eigen, And create variable: EIGEN_DIR , EIGEN_INCLUDE_DIR , EIGEN_FOUND 
    ==> find library: flann, And create variable: FLANN_DIR , FLANN_INCLUDE_DIR , FLANN_LIBRARY
_DIR , FLANN_LIBRARIES
    ==> find library: gflags, And create variable: GFLAGS_DIR , GFLAGS_INCLUDE_DIR , GFLAGS_LIB
RARY_DIR , GFLAGS_LIBRARIES
    ==> find library: gfortran, And create variable: GFORTRAN_DIR , GFORTRAN_INCLUDE_DIR , GFOR
TRAN_LIBRARY_DIR , GFORTRAN_LIBRARIES
    ==> find library: glog, And create variable: GLOG_DIR , GLOG_INCLUDE_DIR , GLOG_LIBRARY_DIR
 , GLOG_LIBRARIES
    ==> find library: ipopt, And create variable: IPOPT_DIR , IPOPT_INCLUDE_DIR , IPOPT_LIBRARY
_DIR , IPOPT_LIBRARIES
    ==> find library: lapack, And create variable: LAPACK_DIR , LAPACK_INCLUDE_DIR , LAPACK_LIB
RARY_DIR , LAPACK_LIBRARIES
    ==> find library: mosquitto, And create variable: MOSQUITTO_DIR , MOSQUITTO_INCLUDE_DIR , M
OSQUITTO_LIBRARY_DIR , MOSQUITTO_LIBRARIES
    ==> find library: opencv, And create variable: OPENCV_DIR , OPENCV_INCLUDE_DIR , OPENCV_LIB
RARY_DIR , OPENCV_LIBRARIES
    ==> find library: openssl, And create variable: OPENSSL_DIR , OPENSSL_INCLUDE_DIR , OPENSSL
_LIBRARY_DIR , OPENSSL_LIBRARIES
    ==> find library: osqp, And create variable: OSQP_DIR , OSQP_INCLUDE_DIR , OSQP_LIBRARY_DIR
 , OSQP_LIBRARIES
    ==> find library: pcl, And create variable: PCL_DIR , PCL_INCLUDE_DIR , PCL_LIBRARY_DIR , P
CL_LIBRARIES
    ==> find library: protobuf, And create variable: PROTOBUF_DIR , PROTOBUF_INCLUDE_DIR , PROT
OBUF_LIBRARY_DIR , PROTOBUF_LIBRARIES
    ==> find library: rs_driver, And create variable: RS_DRIVER_DIR , RS_DRIVER_INCLUDE_DIR , R
S_DRIVER_LIBRARY_DIR , RS_DRIVER_LIBRARIES
    ==> find library: yaml, And create variable: YAML_DIR , YAML_INCLUDE_DIR , YAML_LIBRARY_DIR
 , YAML_LIBRARIES
    ==> find library: autocalib, And create variable: AUTOCALIB_DIR , AUTOCALIB_INCLUDE_DIR , A
UTOCALIB_LIBRARY_DIR , AUTOCALIB_LIBRARIES
    ==> find library: components_and_data_center, And create variable: COMPONENTS_AND_DATA_CENT
ER_DIR , COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR , COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR , COMP
ONENTS_AND_DATA_CENTER_LIBRARIES
    ==> find library: control, And create variable: CONTROL_DIR , CONTROL_INCLUDE_DIR , CONTROL
_LIBRARY_DIR , CONTROL_LIBRARIES
    ==> find library: execution_manifest, And create variable: EXECUTION_MANIFEST_DIR , EXECUTI
ON_MANIFEST_INCLUDE_DIR , EXECUTION_MANIFEST_LIBRARY_DIR , EXECUTION_MANIFEST_LIBRARIES
    ==> find library: fusion_localization_cargovehicle, And create variable: FUSION_LOCALIZATIO
N_CARGOVEHICLE_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_INCLUDE_DIR , FUSION_LOCALIZATION_CARGOVE
HICLE_LIBRARY_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_LIBRARIES
    ==> find library: hlog, And create variable: HLOG_DIR , HLOG_INCLUDE_DIR , HLOG_LIBRARY_DIR
 , HLOG_LIBRARIES
    ==> find library: iceoryx, And create variable: ICEORYX_DIR , ICEORYX_INCLUDE_DIR , ICEORYX
_LIBRARY_DIR , ICEORYX_LIBRARIES
    ==> find library: lidar_obstacle_perception_detect, And create variable: LIDAR_OBSTACLE_PER
CEPTION_DETECT_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_D
ETECT_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_LIBRARIES
    ==> find library: lidar_obstacle_perception_fusion, And create variable: LIDAR_OBSTACLE_PER
CEPTION_FUSION_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_F
USION_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_LIBRARIES
    ==> find library: lidar_obstacle_perception_segment, And create variable: LIDAR_OBSTACLE_PE
RCEPTION_SEGMENT_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTIO
N_SEGMENT_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_LIBRARIES
    ==> find library: lidar_obstacle_perception_track, And create variable: LIDAR_OBSTACLE_PERC
EPTION_TRACK_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_TRAC
K_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_LIBRARIES
    ==> find library: map, And create variable: MAP_DIR , MAP_INCLUDE_DIR , MAP_LIBRARY_DIR , M
AP_LIBRARIES
    ==> find library: planning, And create variable: PLANNING_DIR , PLANNING_INCLUDE_DIR , PLAN
NING_LIBRARY_DIR , PLANNING_LIBRARIES
    ==> find library: road_boundary_perception_track, And create variable: ROAD_BOUNDARY_PERCEP
TION_TRACK_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LI
BRARY_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARIES
    ==> find library: vsomeip, And create variable: VSOMEIP_DIR , VSOMEIP_INCLUDE_DIR , VSOMEIP
_LIBRARY_DIR , VSOMEIP_LIBRARIES
==> copy from: /hfr/3rdparty/dlt/x86_64/lib
==> copy to: /usr/lib/libdlt.so
==> copy to: /usr/lib/libdlt.so.2
==> copy to: /usr/lib/libdlt.so.2.18.9
-- SRC_DIR_LIST = /hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./kalman_fil
ter.cpp;/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./qr.cpp;/hfr/modules/
HAF/road_boundary_perception_track/core/private/RBT/./road_tracker.cpp;/hfr/modules/HAF/road_bo
undary_perception_track/core/private/RBT/./target.cpp;/hfr/modules/HAF/road_boundary_perception
_track/core/private/RBT/./tracked_road.cpp
-- Configuring done
-- Generating done
-- Build files have been written to: /hfr/modules/HAF/road_boundary_perception_track/core/priva
te
HBI_VERSION=1.39

Start glob configure cmake
Search Done: /hfr/lib

Start glob library
    ==> find library: adolc, And create variable: ADOLC_DIR , ADOLC_INCLUDE_DIR , ADOLC_LIBRARY
_DIR , ADOLC_LIBRARIES
    ==> find library: boost, And create variable: BOOST_DIR , BOOST_INCLUDE_DIR , BOOST_LIBRARY
_DIR , BOOST_LIBRARIES
    ==> find library: cjson, And create variable: CJSON_DIR , CJSON_INCLUDE_DIR , CJSON_LIBRARY
_DIR , CJSON_LIBRARIES
    ==> find library: colpack, And create variable: COLPACK_DIR , COLPACK_INCLUDE_DIR , COLPACK
_LIBRARY_DIR , COLPACK_LIBRARIES
    ==> find library: dlt, And create variable: DLT_DIR , DLT_INCLUDE_DIR , DLT_LIBRARY_DIR , D
LT_LIBRARIES
    ==> find library: eigen, And create variable: EIGEN_DIR , EIGEN_INCLUDE_DIR , EIGEN_FOUND 
    ==> find library: flann, And create variable: FLANN_DIR , FLANN_INCLUDE_DIR , FLANN_LIBRARY
_DIR , FLANN_LIBRARIES
    ==> find library: gflags, And create variable: GFLAGS_DIR , GFLAGS_INCLUDE_DIR , GFLAGS_LIB
RARY_DIR , GFLAGS_LIBRARIES
    ==> find library: gfortran, And create variable: GFORTRAN_DIR , GFORTRAN_INCLUDE_DIR , GFOR
TRAN_LIBRARY_DIR , GFORTRAN_LIBRARIES
    ==> find library: glog, And create variable: GLOG_DIR , GLOG_INCLUDE_DIR , GLOG_LIBRARY_DIR
 , GLOG_LIBRARIES
    ==> find library: ipopt, And create variable: IPOPT_DIR , IPOPT_INCLUDE_DIR , IPOPT_LIBRARY
_DIR , IPOPT_LIBRARIES
    ==> find library: lapack, And create variable: LAPACK_DIR , LAPACK_INCLUDE_DIR , LAPACK_LIB
RARY_DIR , LAPACK_LIBRARIES
    ==> find library: mosquitto, And create variable: MOSQUITTO_DIR , MOSQUITTO_INCLUDE_DIR , M
OSQUITTO_LIBRARY_DIR , MOSQUITTO_LIBRARIES
    ==> find library: opencv, And create variable: OPENCV_DIR , OPENCV_INCLUDE_DIR , OPENCV_LIB
RARY_DIR , OPENCV_LIBRARIES
    ==> find library: openssl, And create variable: OPENSSL_DIR , OPENSSL_INCLUDE_DIR , OPENSSL
_LIBRARY_DIR , OPENSSL_LIBRARIES
    ==> find library: osqp, And create variable: OSQP_DIR , OSQP_INCLUDE_DIR , OSQP_LIBRARY_DIR
 , OSQP_LIBRARIES
    ==> find library: pcl, And create variable: PCL_DIR , PCL_INCLUDE_DIR , PCL_LIBRARY_DIR , P
CL_LIBRARIES
    ==> find library: protobuf, And create variable: PROTOBUF_DIR , PROTOBUF_INCLUDE_DIR , PROT
OBUF_LIBRARY_DIR , PROTOBUF_LIBRARIES
    ==> find library: rs_driver, And create variable: RS_DRIVER_DIR , RS_DRIVER_INCLUDE_DIR , R
S_DRIVER_LIBRARY_DIR , RS_DRIVER_LIBRARIES
    ==> find library: yaml, And create variable: YAML_DIR , YAML_INCLUDE_DIR , YAML_LIBRARY_DIR
 , YAML_LIBRARIES
    ==> find library: autocalib, And create variable: AUTOCALIB_DIR , AUTOCALIB_INCLUDE_DIR , A
UTOCALIB_LIBRARY_DIR , AUTOCALIB_LIBRARIES
    ==> find library: components_and_data_center, And create variable: COMPONENTS_AND_DATA_CENT
ER_DIR , COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR , COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR , COMP
ONENTS_AND_DATA_CENTER_LIBRARIES
    ==> find library: control, And create variable: CONTROL_DIR , CONTROL_INCLUDE_DIR , CONTROL
_LIBRARY_DIR , CONTROL_LIBRARIES
    ==> find library: execution_manifest, And create variable: EXECUTION_MANIFEST_DIR , EXECUTI
ON_MANIFEST_INCLUDE_DIR , EXECUTION_MANIFEST_LIBRARY_DIR , EXECUTION_MANIFEST_LIBRARIES
    ==> find library: fusion_localization_cargovehicle, And create variable: FUSION_LOCALIZATIO
N_CARGOVEHICLE_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_INCLUDE_DIR , FUSION_LOCALIZATION_CARGOVE
HICLE_LIBRARY_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_LIBRARIES
    ==> find library: hlog, And create variable: HLOG_DIR , HLOG_INCLUDE_DIR , HLOG_LIBRARY_DIR
 , HLOG_LIBRARIES
    ==> find library: iceoryx, And create variable: ICEORYX_DIR , ICEORYX_INCLUDE_DIR , ICEORYX
_LIBRARY_DIR , ICEORYX_LIBRARIES
    ==> find library: lidar_obstacle_perception_detect, And create variable: LIDAR_OBSTACLE_PER
CEPTION_DETECT_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_D
ETECT_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_LIBRARIES
    ==> find library: lidar_obstacle_perception_fusion, And create variable: LIDAR_OBSTACLE_PER
CEPTION_FUSION_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_F
USION_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_LIBRARIES
    ==> find library: lidar_obstacle_perception_segment, And create variable: LIDAR_OBSTACLE_PE
RCEPTION_SEGMENT_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTIO
N_SEGMENT_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_LIBRARIES
    ==> find library: lidar_obstacle_perception_track, And create variable: LIDAR_OBSTACLE_PERC
EPTION_TRACK_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_TRAC
K_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_LIBRARIES
    ==> find library: map, And create variable: MAP_DIR , MAP_INCLUDE_DIR , MAP_LIBRARY_DIR , M
AP_LIBRARIES
    ==> find library: planning, And create variable: PLANNING_DIR , PLANNING_INCLUDE_DIR , PLAN
NING_LIBRARY_DIR , PLANNING_LIBRARIES
    ==> find library: road_boundary_perception_track, And create variable: ROAD_BOUNDARY_PERCEP
TION_TRACK_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LI
BRARY_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARIES
    ==> find library: vsomeip, And create variable: VSOMEIP_DIR , VSOMEIP_INCLUDE_DIR , VSOMEIP
_LIBRARY_DIR , VSOMEIP_LIBRARIES
==> copy from: /hfr/3rdparty/dlt/x86_64/lib
==> copy to: /usr/lib/libdlt.so
==> copy to: /usr/lib/libdlt.so.2
==> copy to: /usr/lib/libdlt.so.2.18.9
-- SRC_DIR_LIST = /hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./kalman_fil
ter.cpp;/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./qr.cpp;/hfr/modules/
HAF/road_boundary_perception_track/core/private/RBT/./road_tracker.cpp;/hfr/modules/HAF/road_bo
undary_perception_track/core/private/RBT/./target.cpp;/hfr/modules/HAF/road_boundary_perception
_track/core/private/RBT/./tracked_road.cpp
-- Configuring done
-- Generating done
-- Build files have been written to: /hfr/modules/HAF/road_boundary_perception_track/core/priva
te/build
[ 16%] Built target io_util_test
Scanning dependencies of target core_rbt
[ 25%] Building CXX object RBT/CMakeFiles/core_rbt.dir/road_tracker.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp: In static me
mber function ‘static void perception::rbt::RoadTracker::ComputeHistogram(const PBI::RoadInfo*,
 double, std::map<double, int>&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp:24:23: warnin
g: comparison between signed and unsigned integer expressions [-Wsign-compare]
     for (int i = 0; i < det->guardrail.num; ++i) {
                     ~~^~~~~~~~~~~~~~~~~~~~
[ 33%] Building CXX object RBT/CMakeFiles/core_rbt.dir/target.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp: In member function
 ‘bool perception::rbt::Target::SortPoint(PBI::Guardrail&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp:278:1: warning: no 
return statement in function returning non-void [-Wreturn-type]
 }
 ^
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp: In member function
 ‘bool perception::rbt::Target::InitKalmanFilter(const std::vector<perception::rbt::Point2D>&)’
:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp:296:1: warning: no 
return statement in function returning non-void [-Wreturn-type]
 }
 ^
[ 41%] Building CXX object RBT/CMakeFiles/core_rbt.dir/tracked_road.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/tracked_road.cpp: In member fu
nction ‘void perception::rbt::TrackedRoad::ComputeShapeFeatures(int, double, double)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/tracked_road.cpp:102:23: warni
ng: comparison between signed and unsigned integer expressions [-Wsign-compare]
     for (int i = 0; i < road_info.guardrail.num; ++i) {
                     ~~^~~~~~~~~~~~~~~~~~~~~~~~~
[ 50%] Linking CXX shared library libcore_rbt.so
[ 66%] Built target core_rbt
Scanning dependencies of target rbt_test_node
[ 75%] Building CXX object CMakeFiles/rbt_test_node.dir/test/rbt_test.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘do
uble GetTimestampByFilename(const string&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:110:23: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
     if (underline_pos != file_name_without_extension.npos) {
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘vo
id SaveGuardrail(const string&, const PBI::RoadInfoArray*, const PBI::RoadInfoArray*)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:127:31: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:141:31: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘vo
id VisualizeGuardrail(const string&, const PBI::RoadInfoArray*, const PBI::RoadInfoArray*, cons
t HBI::LocalizationEstimate*)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:335:31: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:365:31: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
[ 83%] Linking CXX executable rbt_test_node
[ 83%] Built target rbt_test_node
Scanning dependencies of target rbt_sim
[ 91%] Building CXX object CMakeFiles/rbt_sim.dir/test/rbt_sim.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp: In function ‘dou
ble GetTimestampByFilename(const string&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp:125:23: warning: 
comparison between signed and unsigned integer expressions [-Wsign-compare]
     if (underline_pos != file_name_without_extension.npos) {
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp: In function ‘voi
d SaveGuardrail(const string&, const PBI::RoadInfoArray*, const PBI::RoadInfoArray*)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp:142:31: warning: 
comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp:156:31: warning: 
comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp: In function ‘voi
d VisualizeGuardrail(const string&, const PBI::RoadInfoArray*, const PBI::RoadInfoArray*, const
 HBI::LocalizationEstimate*)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp:350:31: warning: 
comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp:380:31: warning: 
comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp: In function ‘voi
d GenerateTestData(PBI::RoadInfo*, int, float, float, int, int)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp:464:31: warning: 
comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int i = 0; i < road_info->guardrail.num; ++i) {
                             ~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp:499:31: warning: 
comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int i = 0; i < road_info->guardrail.num; ++i) {
                             ~~^~~~~~~~~~~~~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp:513:23: warning: 
unused variable ‘normal_x’ [-Wunused-variable]
                 float normal_x = -tangent_y / norm;
                       ^~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp:487:19: warning: 
unused variable ‘range’ [-Wunused-variable]
             float range = view_end - view_start;
                   ^~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_sim.cpp:536:31: warning: 
comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int i = 0; i < road_info->guardrail.num; ++i) {
                             ~~^~~~~~~~~~~~~~~~~~~~~~~~~~
[100%] Linking CXX executable rbt_sim
[100%] Built target rbt_sim

```

---

现在我们来运行仿真代码：

---

```bash
mkdir -p ../data && ./rbt_sim
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# mkdi
r -p ../data && ./rbt_sim
[ 6238.213627]~DLT~55922~INFO     ~FIFO /tmp/dlt cannot be opened. Retrying later...

Simulation frame:0
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000000.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000000.png

Simulation frame:1
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000001.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000001.png

Simulation frame:2
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000002.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000002.png

Simulation frame:3
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000003.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000003.png

Simulation frame:4
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000004.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000004.png

Simulation frame:5
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000005.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000005.png

Simulation frame:6
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000006.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000006.png

Simulation frame:7
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000007.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000007.png

Simulation frame:8
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000008.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000008.png

Simulation frame:9
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000009.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000009.png

Simulation frame:10
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000010.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000010.png

Simulation frame:11
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000011.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000011.png

Simulation frame:12
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000012.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000012.png

Simulation frame:13
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000013.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000013.png

Simulation frame:14
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000014.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000014.png

Simulation frame:15
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000015.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000015.png

Simulation frame:16
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000016.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000016.png

Simulation frame:17
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000017.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000017.png

Simulation frame:18
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000018.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000018.png

Simulation frame:19
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000019.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000019.png

Simulation frame:20
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000020.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000020.png

Simulation frame:21
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000021.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000021.png

Simulation frame:22
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000022.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000022.png

Simulation frame:23
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000023.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000023.png

Simulation frame:24
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000024.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000024.png

Simulation frame:25
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000025.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000025.png

Simulation frame:26
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000026.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000026.png

Simulation frame:27
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000027.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000027.png

Simulation frame:28
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000028.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000028.png

Simulation frame:29
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000029.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000029.png

Simulation frame:30
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000030.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000030.png

Simulation frame:31
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000031.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000031.png

Simulation frame:32
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000032.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000032.png

Simulation frame:33
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000033.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000033.png

Simulation frame:34
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000034.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000034.png

Simulation frame:35
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000035.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000035.png

Simulation frame:36
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000036.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000036.png

Simulation frame:37
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000037.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000037.png

Simulation frame:38
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000038.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000038.png

Simulation frame:39
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000039.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000039.png

Simulation frame:40
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000040.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000040.png

Simulation frame:41
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000041.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000041.png

Simulation frame:42
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000042.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000042.png

Simulation frame:43
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000043.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000043.png

Simulation frame:44
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000044.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000044.png

Simulation frame:45
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000045.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000045.png

Simulation frame:46
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000046.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000046.png

Simulation frame:47
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000047.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000047.png

Simulation frame:48
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000048.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000048.png

Simulation frame:49
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000049.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000049.png

Simulation frame:50
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000050.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000050.png

Simulation frame:51
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000051.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000051.png

Simulation frame:52
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000052.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000052.png

Simulation frame:53
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000053.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000053.png

Simulation frame:54
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000054.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000054.png

Simulation frame:55
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000055.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000055.png

Simulation frame:56
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000056.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000056.png

Simulation frame:57
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000057.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000057.png

Simulation frame:58
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000058.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000058.png

Simulation frame:59
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000059.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000059.png

Simulation frame:60
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000060.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000060.png

Simulation frame:61
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000061.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000061.png

Simulation frame:62
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000062.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000062.png

Simulation frame:63
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000063.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000063.png

Simulation frame:64
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000064.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000064.png

Simulation frame:65
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000065.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000065.png

Simulation frame:66
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000066.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000066.png

Simulation frame:67
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000067.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000067.png

Simulation frame:68
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000068.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000068.png

Simulation frame:69
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000069.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000069.png

Simulation frame:70
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000070.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000070.png

Simulation frame:71
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000071.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000071.png

Simulation frame:72
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000072.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000072.png

Simulation frame:73
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000073.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000073.png

Simulation frame:74
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000074.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000074.png

Simulation frame:75
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000075.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000075.png

Simulation frame:76
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000076.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000076.png

Simulation frame:77
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000077.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000077.png

Simulation frame:78
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000078.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000078.png

Simulation frame:79
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000079.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000079.png

Simulation frame:80
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000080.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000080.png

Simulation frame:81
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000081.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000081.png

Simulation frame:82
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000082.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000082.png

Simulation frame:83
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000083.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000083.png

Simulation frame:84
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000084.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000084.png

Simulation frame:85
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000085.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000085.png

Simulation frame:86
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000086.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000086.png

Simulation frame:87
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000087.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000087.png

Simulation frame:88
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000088.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000088.png

Simulation frame:89
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000089.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000089.png

Simulation frame:90
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000090.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000090.png

Simulation frame:91
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000091.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000091.png

Simulation frame:92
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000092.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000092.png

Simulation frame:93
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000093.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000093.png

Simulation frame:94
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000094.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000094.png

Simulation frame:95
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000095.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000095.png

Simulation frame:96
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000096.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000096.png

Simulation frame:97
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000097.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000097.png

Simulation frame:98
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000098.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000098.png

Simulation frame:99
护栏点已保存: ../data/1708844416_1708844532/target_management/sim_000099.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/sim_000099.png
Simulation Done!
[ 6248.883717]~DLT~55922~WARNING  ~Lost log messages in user buffer when exiting: 1201
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build#
```

---

让我们查看生成的可视化图像：

---

```bash
ls -l ../data/1708844416_1708844532/visualization/
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# 
-rw-r--r-- 1 <USER> <GROUP> 52784 May 15 11:08 1708844466_519377.png
-rw-r--r-- 1 <USER> <GROUP> 54790 May 15 11:08 1708844466_590805.png
-rw-r--r-- 1 <USER> <GROUP> 54463 May 15 11:08 1708844466_662205.png
-rw-r--r-- 1 <USER> <GROUP> 54666 May 15 11:08 1708844466_735633.png
-rw-r--r-- 1 <USER> <GROUP> 54483 May 15 11:08 1708844466_806817.png
-rw-r--r-- 1 <USER> <GROUP> 53475 May 15 11:08 1708844466_879487.png
-rw-r--r-- 1 <USER> <GROUP> 54434 May 15 11:08 1708844466_950948.png
-rw-r--r-- 1 <USER> <GROUP> 54011 May 15 11:08 1708844467_024891.png
-rw-r--r-- 1 <USER> <GROUP> 54153 May 15 11:08 1708844467_095240.png
-rw-r--r-- 1 <USER> <GROUP> 53853 May 15 11:08 1708844467_168382.png
-rw-r--r-- 1 <USER> <GROUP> 54326 May 15 11:08 1708844467_242226.png
-rw-r--r-- 1 <USER> <GROUP> 54948 May 15 11:08 1708844467_312984.png
-rw-r--r-- 1 <USER> <GROUP> 54557 May 15 11:08 1708844467_385166.png
-rw-r--r-- 1 <USER> <GROUP> 54599 May 15 11:08 1708844467_457551.png
-rw-r--r-- 1 <USER> <GROUP> 53666 May 15 11:08 1708844467_527923.png
-rw-r--r-- 1 <USER> <GROUP> 53885 May 15 11:08 1708844467_601757.png
-rw-r--r-- 1 <USER> <GROUP> 54415 May 15 11:08 1708844467_674091.png
-rw-r--r-- 1 <USER> <GROUP> 53808 May 15 11:08 1708844467_747057.png
-rw-r--r-- 1 <USER> <GROUP> 54042 May 15 11:08 1708844467_818303.png
-rw-r--r-- 1 <USER> <GROUP> 54297 May 15 11:08 1708844467_891506.png
-rw-r--r-- 1 <USER> <GROUP> 54244 May 15 11:08 1708844467_962368.png
-rw-r--r-- 1 <USER> <GROUP> 54264 May 15 11:08 1708844468_033538.png
-rw-r--r-- 1 <USER> <GROUP> 54326 May 15 11:08 1708844468_106505.png
-rw-r--r-- 1 <USER> <GROUP> 53886 May 15 11:08 1708844468_177704.png
-rw-r--r-- 1 <USER> <GROUP> 54000 May 15 11:08 1708844468_250212.png
-rw-r--r-- 1 <USER> <GROUP> 53892 May 15 11:08 1708844468_322626.png
-rw-r--r-- 1 <USER> <GROUP> 52809 May 15 11:08 1708844468_396715.png
-rw-r--r-- 1 <USER> <GROUP> 52808 May 15 11:08 1708844468_467516.png
-rw-r--r-- 1 <USER> <GROUP> 52822 May 15 11:08 1708844468_539335.png
-rw-r--r-- 1 <USER> <GROUP> 52851 May 15 11:08 1708844468_610420.png
-rw-r--r-- 1 <USER> <GROUP> 52862 May 15 11:08 1708844468_683852.png
-rw-r--r-- 1 <USER> <GROUP> 54269 May 15 11:08 1708844468_756320.png
-rw-r--r-- 1 <USER> <GROUP> 54352 May 15 11:08 1708844468_828133.png
-rw-r--r-- 1 <USER> <GROUP> 52849 May 15 11:08 1708844468_901249.png
-rw-r--r-- 1 <USER> <GROUP> 52783 May 15 11:08 1708844468_973112.png
-rw-r--r-- 1 <USER> <GROUP> 52838 May 15 11:08 1708844469_044478.png
-rw-r--r-- 1 <USER> <GROUP> 52746 May 15 11:08 1708844469_117142.png
-rw-r--r-- 1 <USER> <GROUP> 54250 May 15 11:08 1708844469_189871.png
-rw-r--r-- 1 <USER> <GROUP> 53900 May 15 11:08 1708844469_262141.png
-rw-r--r-- 1 <USER> <GROUP> 54214 May 15 11:08 1708844469_333183.png
-rw-r--r-- 1 <USER> <GROUP> 54194 May 15 11:08 1708844469_406761.png
-rw-r--r-- 1 <USER> <GROUP> 53806 May 15 11:08 1708844469_478510.png
-rw-r--r-- 1 <USER> <GROUP> 54031 May 15 11:08 1708844469_551034.png
-rw-r--r-- 1 <USER> <GROUP> 54166 May 15 11:08 1708844469_623452.png
-rw-r--r-- 1 <USER> <GROUP> 54543 May 15 11:08 1708844469_694599.png
-rw-r--r-- 1 <USER> <GROUP> 54450 May 15 11:08 1708844469_766659.png
-rw-r--r-- 1 <USER> <GROUP> 54553 May 15 11:08 1708844469_838456.png
-rw-r--r-- 1 <USER> <GROUP> 54543 May 15 11:08 1708844469_910829.png
-rw-r--r-- 1 <USER> <GROUP> 54661 May 15 11:08 1708844469_984396.png
-rw-r--r-- 1 <USER> <GROUP> 54492 May 15 11:08 1708844470_055786.png
-rw-r--r-- 1 <USER> <GROUP> 54795 May 15 11:08 1708844470_126795.png
-rw-r--r-- 1 <USER> <GROUP> 55044 May 15 11:08 1708844470_199694.png
-rw-r--r-- 1 <USER> <GROUP> 54769 May 15 11:08 1708844470_272467.png
-rw-r--r-- 1 <USER> <GROUP> 54279 May 15 11:08 1708844470_344159.png
-rw-r--r-- 1 <USER> <GROUP> 54789 May 15 11:08 1708844470_417449.png
-rw-r--r-- 1 <USER> <GROUP> 53676 May 15 11:08 1708844470_489017.png
-rw-r--r-- 1 <USER> <GROUP> 55243 May 15 11:08 1708844470_559287.png
-rw-r--r-- 1 <USER> <GROUP> 55090 May 15 11:08 1708844470_632863.png
-rw-r--r-- 1 <USER> <GROUP> 54507 May 15 11:08 1708844470_704897.png
-rw-r--r-- 1 <USER> <GROUP> 54989 May 15 11:08 1708844470_777899.png
-rw-r--r-- 1 <USER> <GROUP> 54938 May 15 11:08 1708844470_849162.png
-rw-r--r-- 1 <USER> <GROUP> 54843 May 15 11:08 1708844470_921638.png
-rw-r--r-- 1 <USER> <GROUP> 55239 May 15 11:08 1708844470_993596.png
-rw-r--r-- 1 <USER> <GROUP> 55067 May 15 11:08 1708844471_064426.png
-rw-r--r-- 1 <USER> <GROUP> 54704 May 15 11:08 1708844471_138992.png
-rw-r--r-- 1 <USER> <GROUP> 54825 May 15 11:08 1708844471_212192.png
-rw-r--r-- 1 <USER> <GROUP> 54357 May 15 11:08 1708844471_283195.png
-rw-r--r-- 1 <USER> <GROUP> 54671 May 15 11:08 1708844471_355068.png
-rw-r--r-- 1 <USER> <GROUP> 54543 May 15 11:08 1708844471_427135.png
-rw-r--r-- 1 <USER> <GROUP> 54775 May 15 11:08 1708844471_499615.png
-rw-r--r-- 1 <USER> <GROUP> 54541 May 15 11:08 1708844471_572020.png
-rw-r--r-- 1 <USER> <GROUP> 55038 May 15 11:08 1708844471_643614.png
-rw-r--r-- 1 <USER> <GROUP> 54247 May 15 11:08 1708844471_716460.png
-rw-r--r-- 1 <USER> <GROUP> 54518 May 15 11:08 1708844471_787870.png
-rw-r--r-- 1 <USER> <GROUP> 54498 May 15 11:08 1708844471_860232.png
-rw-r--r-- 1 <USER> <GROUP> 54438 May 15 11:08 1708844471_932710.png
-rw-r--r-- 1 <USER> <GROUP> 54400 May 15 11:08 1708844472_003412.png
-rw-r--r-- 1 <USER> <GROUP> 54379 May 15 11:08 1708844472_076319.png
-rw-r--r-- 1 <USER> <GROUP> 54459 May 15 11:08 1708844472_148507.png
-rw-r--r-- 1 <USER> <GROUP> 54810 May 15 11:08 1708844472_220757.png
-rw-r--r-- 1 <USER> <GROUP> 54073 May 15 11:08 1708844472_293558.png
-rw-r--r-- 1 <USER> <GROUP> 54101 May 15 11:08 1708844472_363562.png
-rw-r--r-- 1 <USER> <GROUP> 54004 May 15 11:08 1708844472_437365.png
-rw-r--r-- 1 <USER> <GROUP> 53944 May 15 11:08 1708844472_510433.png
-rw-r--r-- 1 <USER> <GROUP> 54444 May 15 11:08 1708844472_580667.png
-rw-r--r-- 1 <USER> <GROUP> 54249 May 15 11:08 1708844472_653923.png
-rw-r--r-- 1 <USER> <GROUP> 54345 May 15 11:08 1708844472_726459.png
-rw-r--r-- 1 <USER> <GROUP> 54582 May 15 11:08 1708844472_799428.png
-rw-r--r-- 1 <USER> <GROUP> 54354 May 15 11:08 1708844472_869817.png
-rw-r--r-- 1 <USER> <GROUP> 54218 May 15 11:08 1708844472_941067.png
-rw-r--r-- 1 <USER> <GROUP> 54415 May 15 11:08 1708844473_014608.png
-rw-r--r-- 1 <USER> <GROUP> 54289 May 15 11:08 1708844473_087266.png
-rw-r--r-- 1 <USER> <GROUP> 53731 May 15 11:08 1708844473_159445.png
-rw-r--r-- 1 <USER> <GROUP> 54331 May 15 11:08 1708844473_231090.png
-rw-r--r-- 1 <USER> <GROUP> 54004 May 15 11:08 1708844473_303412.png
-rw-r--r-- 1 <USER> <GROUP> 52783 May 15 11:08 1708844473_374901.png
-rw-r--r-- 1 <USER> <GROUP> 54078 May 15 11:08 1708844473_448020.png
-rw-r--r-- 1 <USER> <GROUP> 54214 May 15 11:08 1708844473_520098.png
-rw-r--r-- 1 <USER> <GROUP> 53842 May 15 11:08 1708844473_591777.png
-rw-r--r-- 1 <USER> <GROUP> 54620 May 15 11:08 1708844473_664984.png
-rw-r--r-- 1 <USER> <GROUP> 53743 May 15 11:08 1708844473_736823.png
-rw-r--r-- 1 <USER> <GROUP> 53924 May 15 11:08 1708844473_808703.png
-rw-r--r-- 1 <USER> <GROUP> 53917 May 15 11:08 1708844473_880639.png
-rw-r--r-- 1 <USER> <GROUP> 53926 May 15 11:08 1708844473_953874.png
-rw-r--r-- 1 <USER> <GROUP> 53770 May 15 11:08 1708844474_025420.png
-rw-r--r-- 1 <USER> <GROUP> 53967 May 15 11:08 1708844474_098327.png
-rw-r--r-- 1 <USER> <GROUP> 53692 May 15 11:08 1708844474_168764.png
-rw-r--r-- 1 <USER> <GROUP> 52775 May 15 11:08 1708844474_241319.png
-rw-r--r-- 1 <USER> <GROUP> 52792 May 15 11:08 1708844474_314395.png
-rw-r--r-- 1 <USER> <GROUP> 52825 May 15 11:08 1708844474_385446.png
-rw-r--r-- 1 <USER> <GROUP> 52794 May 15 11:08 1708844474_457979.png
-rw-r--r-- 1 <USER> <GROUP> 52779 May 15 11:08 1708844474_530217.png
-rw-r--r-- 1 <USER> <GROUP> 52841 May 15 11:08 1708844474_603106.png
-rw-r--r-- 1 <USER> <GROUP> 52819 May 15 11:08 1708844474_674628.png
-rw-r--r-- 1 <USER> <GROUP> 52809 May 15 11:08 1708844474_746582.png
-rw-r--r-- 1 <USER> <GROUP> 52830 May 15 11:08 1708844474_819997.png
-rw-r--r-- 1 <USER> <GROUP> 52827 May 15 11:08 1708844474_892201.png
-rw-r--r-- 1 <USER> <GROUP> 52856 May 15 11:08 1708844474_963969.png
-rw-r--r-- 1 <USER> <GROUP> 52829 May 15 11:08 1708844475_035460.png
-rw-r--r-- 1 <USER> <GROUP> 54153 May 15 11:08 1708844475_107519.png
-rw-r--r-- 1 <USER> <GROUP> 52779 May 15 11:08 1708844475_179538.png
-rw-r--r-- 1 <USER> <GROUP> 52742 May 15 11:08 1708844475_252141.png
-rw-r--r-- 1 <USER> <GROUP> 52796 May 15 11:08 1708844475_324469.png
-rw-r--r-- 1 <USER> <GROUP> 52837 May 15 11:08 1708844475_396859.png
-rw-r--r-- 1 <USER> <GROUP> 52798 May 15 11:08 1708844475_468547.png
-rw-r--r-- 1 <USER> <GROUP> 52763 May 15 11:08 1708844475_541435.png
-rw-r--r-- 1 <USER> <GROUP> 54290 May 15 11:08 1708844475_612612.png
-rw-r--r-- 1 <USER> <GROUP> 53949 May 15 11:08 1708844475_686270.png
-rw-r--r-- 1 <USER> <GROUP> 53870 May 15 11:08 1708844475_756061.png
-rw-r--r-- 1 <USER> <GROUP> 54012 May 15 11:08 1708844475_827894.png
-rw-r--r-- 1 <USER> <GROUP> 54170 May 15 11:08 1708844475_902250.png
-rw-r--r-- 1 <USER> <GROUP> 54243 May 15 11:08 1708844475_974126.png
-rw-r--r-- 1 <USER> <GROUP> 53959 May 15 11:08 1708844476_044712.png
-rw-r--r-- 1 <USER> <GROUP> 54272 May 15 11:08 1708844476_117009.png
-rw-r--r-- 1 <USER> <GROUP> 53728 May 15 11:08 1708844476_189578.png
-rw-r--r-- 1 <USER> <GROUP> 54301 May 15 11:08 1708844476_262322.png
-rw-r--r-- 1 <USER> <GROUP> 53607 May 15 11:08 1708844476_335566.png
-rw-r--r-- 1 <USER> <GROUP> 54477 May 15 11:08 1708844476_406769.png
-rw-r--r-- 1 <USER> <GROUP> 52826 May 15 11:08 1708844476_479828.png
-rw-r--r-- 1 <USER> <GROUP> 54756 May 15 11:08 1708844476_549741.png
-rw-r--r-- 1 <USER> <GROUP> 54732 May 15 11:08 1708844476_623016.png
-rw-r--r-- 1 <USER> <GROUP> 52835 May 15 11:08 1708844476_696295.png
-rw-r--r-- 1 <USER> <GROUP> 53632 May 15 11:08 1708844476_767513.png
-rw-r--r-- 1 <USER> <GROUP> 55040 May 15 11:08 1708844476_838823.png
-rw-r--r-- 1 <USER> <GROUP> 54618 May 15 11:08 1708844476_911212.png
-rw-r--r-- 1 <USER> <GROUP> 52796 May 15 11:08 1708844476_984173.png
-rw-r--r-- 1 <USER> <GROUP> 52847 May 15 11:08 1708844477_056858.png
-rw-r--r-- 1 <USER> <GROUP> 54677 May 15 11:08 1708844477_126727.png
-rw-r--r-- 1 <USER> <GROUP> 54909 May 15 11:08 1708844477_198720.png
-rw-r--r-- 1 <USER> <GROUP> 52721 May 15 11:08 1708844477_274471.png
-rw-r--r-- 1 <USER> <GROUP> 54659 May 15 11:08 1708844477_345654.png
-rw-r--r-- 1 <USER> <GROUP> 54514 May 15 11:08 1708844477_417508.png
-rw-r--r-- 1 <USER> <GROUP> 54539 May 15 11:08 1708844477_490081.png
-rw-r--r-- 1 <USER> <GROUP> 54798 May 15 11:08 1708844477_561827.png
-rw-r--r-- 1 <USER> <GROUP> 54558 May 15 11:08 1708844477_633534.png
-rw-r--r-- 1 <USER> <GROUP> 55408 May 15 11:08 1708844477_708183.png
-rw-r--r-- 1 <USER> <GROUP> 54719 May 15 11:08 1708844477_777253.png
-rw-r--r-- 1 <USER> <GROUP> 54776 May 15 11:08 1708844477_849911.png
-rw-r--r-- 1 <USER> <GROUP> 54906 May 15 11:08 1708844477_922450.png
-rw-r--r-- 1 <USER> <GROUP> 54633 May 15 11:08 1708844477_996019.png
-rw-r--r-- 1 <USER> <GROUP> 54783 May 15 11:08 1708844478_066988.png
-rw-r--r-- 1 <USER> <GROUP> 54710 May 15 11:08 1708844478_139509.png
-rw-r--r-- 1 <USER> <GROUP> 54702 May 15 11:08 1708844478_211843.png
-rw-r--r-- 1 <USER> <GROUP> 54832 May 15 11:08 1708844478_283272.png
-rw-r--r-- 1 <USER> <GROUP> 54730 May 15 11:08 1708844478_355588.png
-rw-r--r-- 1 <USER> <GROUP> 54758 May 15 11:08 1708844478_428923.png
-rw-r--r-- 1 <USER> <GROUP> 54277 May 15 11:08 1708844478_499159.png
-rw-r--r-- 1 <USER> <GROUP> 54594 May 15 11:08 1708844478_572672.png
-rw-r--r-- 1 <USER> <GROUP> 54936 May 15 11:08 1708844478_644630.png
-rw-r--r-- 1 <USER> <GROUP> 54851 May 15 11:08 1708844478_716457.png
-rw-r--r-- 1 <USER> <GROUP> 54481 May 15 11:08 1708844478_789656.png
-rw-r--r-- 1 <USER> <GROUP> 54589 May 15 11:08 1708844478_862357.png
-rw-r--r-- 1 <USER> <GROUP> 55028 May 15 11:08 1708844478_932730.png
-rw-r--r-- 1 <USER> <GROUP> 54257 May 15 11:08 1708844479_005837.png
-rw-r--r-- 1 <USER> <GROUP> 55100 May 15 11:08 1708844479_077670.png
-rw-r--r-- 1 <USER> <GROUP> 55067 May 15 11:08 1708844479_150149.png
-rw-r--r-- 1 <USER> <GROUP> 55095 May 15 11:08 1708844479_223429.png
-rw-r--r-- 1 <USER> <GROUP> 55126 May 15 11:08 1708844479_293580.png
-rw-r--r-- 1 <USER> <GROUP> 54370 May 15 11:08 1708844479_367014.png
-rw-r--r-- 1 <USER> <GROUP> 54614 May 15 11:08 1708844479_438093.png
-rw-r--r-- 1 <USER> <GROUP> 54868 May 15 11:08 1708844479_511417.png
-rw-r--r-- 1 <USER> <GROUP> 54939 May 15 11:08 1708844479_582498.png
-rw-r--r-- 1 <USER> <GROUP> 54768 May 15 11:08 1708844479_656328.png
-rw-r--r-- 1 <USER> <GROUP> 54396 May 15 11:08 1708844479_728353.png
-rw-r--r-- 1 <USER> <GROUP> 54006 May 15 11:08 1708844479_799456.png
-rw-r--r-- 1 <USER> <GROUP> 54563 May 15 11:08 1708844479_870963.png
-rw-r--r-- 1 <USER> <GROUP> 54630 May 15 11:08 1708844479_944101.png
-rw-r--r-- 1 <USER> <GROUP> 54451 May 15 11:08 1708844480_014973.png
-rw-r--r-- 1 <USER> <GROUP> 54428 May 15 11:08 1708844480_087403.png
-rw-r--r-- 1 <USER> <GROUP> 54384 May 15 11:08 1708844480_159563.png
-rw-r--r-- 1 <USER> <GROUP> 54365 May 15 11:08 1708844480_232691.png
-rw-r--r-- 1 <USER> <GROUP> 54460 May 15 11:08 1708844480_304639.png
-rw-r--r-- 1 <USER> <GROUP> 54492 May 15 11:08 1708844480_377863.png
-rw-r--r-- 1 <USER> <GROUP> 54310 May 15 11:08 1708844480_448243.png
-rw-r--r-- 1 <USER> <GROUP> 54430 May 15 11:08 1708844480_520619.png
-rw-r--r-- 1 <USER> <GROUP> 54424 May 15 11:08 1708844480_593503.png
-rw-r--r-- 1 <USER> <GROUP> 54690 May 15 11:08 1708844480_666282.png
-rw-r--r-- 1 <USER> <GROUP> 54046 May 15 11:08 1708844480_738081.png
-rw-r--r-- 1 <USER> <GROUP> 53963 May 15 11:08 1708844480_809820.png
-rw-r--r-- 1 <USER> <GROUP> 54569 May 15 11:08 1708844480_880222.png
-rw-r--r-- 1 <USER> <GROUP> 54021 May 15 11:08 1708844480_954236.png
-rw-r--r-- 1 <USER> <GROUP> 53838 May 15 11:08 1708844481_026657.png
-rw-r--r-- 1 <USER> <GROUP> 54384 May 15 11:08 1708844481_098518.png
-rw-r--r-- 1 <USER> <GROUP> 53495 May 15 11:08 1708844481_171321.png
-rw-r--r-- 1 <USER> <GROUP> 54088 May 15 11:08 1708844481_243243.png
-rw-r--r-- 1 <USER> <GROUP> 54753 May 15 11:08 1708844481_316389.png
-rw-r--r-- 1 <USER> <GROUP> 55285 May 15 11:08 1708844481_387670.png
-rw-r--r-- 1 <USER> <GROUP> 54811 May 15 11:08 1708844481_459791.png
-rw-r--r-- 1 <USER> <GROUP> 55259 May 15 11:08 1708844481_532076.png
-rw-r--r-- 1 <USER> <GROUP> 55215 May 15 11:08 1708844481_603881.png
-rw-r--r-- 1 <USER> <GROUP> 54177 May 15 11:08 1708844481_675382.png
-rw-r--r-- 1 <USER> <GROUP> 55105 May 15 11:08 1708844481_748103.png
-rw-r--r-- 1 <USER> <GROUP> 55099 May 15 11:08 1708844481_820697.png
-rw-r--r-- 1 <USER> <GROUP> 55383 May 15 11:08 1708844481_892849.png
-rw-r--r-- 1 <USER> <GROUP> 55547 May 15 11:08 1708844481_965291.png
-rw-r--r-- 1 <USER> <GROUP> 55423 May 15 11:08 1708844482_036625.png
-rw-r--r-- 1 <USER> <GROUP> 55318 May 15 11:08 1708844482_108234.png
-rw-r--r-- 1 <USER> <GROUP> 54366 May 15 11:08 1708844482_181841.png
-rw-r--r-- 1 <USER> <GROUP> 55743 May 15 11:08 1708844482_254248.png
-rw-r--r-- 1 <USER> <GROUP> 55989 May 15 11:08 1708844482_325742.png
-rw-r--r-- 1 <USER> <GROUP> 55685 May 15 11:08 1708844482_397806.png
-rw-r--r-- 1 <USER> <GROUP> 56127 May 15 11:08 1708844482_469603.png
-rw-r--r-- 1 <USER> <GROUP> 55776 May 15 11:08 1708844482_543063.png
-rw-r--r-- 1 <USER> <GROUP> 54720 May 15 11:08 1708844482_615401.png
-rw-r--r-- 1 <USER> <GROUP> 55267 May 15 11:08 1708844482_686181.png
-rw-r--r-- 1 <USER> <GROUP> 55312 May 15 11:08 1708844482_758903.png
-rw-r--r-- 1 <USER> <GROUP> 55184 May 15 11:08 1708844482_830623.png
-rw-r--r-- 1 <USER> <GROUP> 55628 May 15 11:08 1708844482_902772.png
-rw-r--r-- 1 <USER> <GROUP> 55731 May 15 11:08 1708844482_975540.png
-rw-r--r-- 1 <USER> <GROUP> 54792 May 15 11:08 1708844483_047979.png
-rw-r--r-- 1 <USER> <GROUP> 54722 May 15 11:08 1708844483_119717.png
-rw-r--r-- 1 <USER> <GROUP> 54812 May 15 11:08 1708844483_191518.png
-rw-r--r-- 1 <USER> <GROUP> 55696 May 15 11:08 1708844483_263243.png
-rw-r--r-- 1 <USER> <GROUP> 54675 May 15 11:08 1708844483_335971.png
-rw-r--r-- 1 <USER> <GROUP> 55618 May 15 11:08 1708844483_407361.png
-rw-r--r-- 1 <USER> <GROUP> 55835 May 15 11:08 1708844483_480662.png
-rw-r--r-- 1 <USER> <GROUP> 55542 May 15 11:08 1708844483_552300.png
-rw-r--r-- 1 <USER> <GROUP> 54472 May 15 11:08 1708844483_624153.png
-rw-r--r-- 1 <USER> <GROUP> 54204 May 15 11:08 1708844483_696158.png
-rw-r--r-- 1 <USER> <GROUP> 54986 May 15 11:08 1708844483_769444.png
-rw-r--r-- 1 <USER> <GROUP> 54610 May 15 11:08 1708844483_841345.png
-rw-r--r-- 1 <USER> <GROUP> 54548 May 15 11:08 1708844483_913329.png
-rw-r--r-- 1 <USER> <GROUP> 54893 May 15 11:08 1708844483_985591.png
-rw-r--r-- 1 <USER> <GROUP> 55754 May 15 11:08 1708844484_058132.png
-rw-r--r-- 1 <USER> <GROUP> 55129 May 15 11:08 1708844484_129353.png
-rw-r--r-- 1 <USER> <GROUP> 55758 May 15 11:08 1708844484_203122.png
-rw-r--r-- 1 <USER> <GROUP> 54932 May 15 11:08 1708844484_274302.png
-rw-r--r-- 1 <USER> <GROUP> 55532 May 15 11:08 1708844484_346080.png
-rw-r--r-- 1 <USER> <GROUP> 54905 May 15 11:08 1708844484_418619.png
-rw-r--r-- 1 <USER> <GROUP> 55288 May 15 11:08 1708844484_489492.png
-rw-r--r-- 1 <USER> <GROUP> 54045 May 15 11:08 1708844484_561827.png
-rw-r--r-- 1 <USER> <GROUP> 56127 May 15 11:08 1708844484_634806.png
-rw-r--r-- 1 <USER> <GROUP> 55693 May 15 11:08 1708844484_707630.png
-rw-r--r-- 1 <USER> <GROUP> 55528 May 15 11:08 1708844484_780348.png
-rw-r--r-- 1 <USER> <GROUP> 55919 May 15 11:08 1708844484_851203.png
-rw-r--r-- 1 <USER> <GROUP> 55399 May 15 11:08 1708844484_923138.png
-rw-r--r-- 1 <USER> <GROUP> 55672 May 15 11:08 1708844484_995506.png
-rw-r--r-- 1 <USER> <GROUP> 55623 May 15 11:08 1708844485_068050.png
-rw-r--r-- 1 <USER> <GROUP> 55013 May 15 11:08 1708844485_140377.png
-rw-r--r-- 1 <USER> <GROUP> 55158 May 15 11:08 1708844485_213619.png
-rw-r--r-- 1 <USER> <GROUP> 55142 May 15 11:08 1708844485_283935.png
-rw-r--r-- 1 <USER> <GROUP> 54127 May 15 11:08 1708844485_355856.png
-rw-r--r-- 1 <USER> <GROUP> 54878 May 15 11:08 1708844485_429405.png
-rw-r--r-- 1 <USER> <GROUP> 54871 May 15 11:08 1708844485_500900.png
-rw-r--r-- 1 <USER> <GROUP> 54130 May 15 11:08 1708844485_573731.png
-rw-r--r-- 1 <USER> <GROUP> 55159 May 15 11:08 1708844485_645838.png
-rw-r--r-- 1 <USER> <GROUP> 55201 May 15 11:08 1708844485_716779.png
-rw-r--r-- 1 <USER> <GROUP> 55313 May 15 11:08 1708844485_790402.png
-rw-r--r-- 1 <USER> <GROUP> 55345 May 15 11:08 1708844485_861399.png
-rw-r--r-- 1 <USER> <GROUP> 55258 May 15 11:08 1708844485_933898.png
-rw-r--r-- 1 <USER> <GROUP> 55250 May 15 11:08 1708844486_003255.png
-rw-r--r-- 1 <USER> <GROUP> 55737 May 15 11:08 1708844486_079339.png
-rw-r--r-- 1 <USER> <GROUP> 54724 May 15 11:08 1708844486_150420.png
-rw-r--r-- 1 <USER> <GROUP> 54987 May 15 11:08 1708844486_222806.png
-rw-r--r-- 1 <USER> <GROUP> 56033 May 15 11:08 1708844486_294647.png
-rw-r--r-- 1 <USER> <GROUP> 54472 May 15 11:08 1708844486_367955.png
-rw-r--r-- 1 <USER> <GROUP> 55007 May 15 11:08 1708844486_439397.png
-rw-r--r-- 1 <USER> <GROUP> 54408 May 15 11:08 1708844486_512746.png
-rw-r--r-- 1 <USER> <GROUP> 55337 May 15 11:08 1708844486_584441.png
-rw-r--r-- 1 <USER> <GROUP> 55563 May 15 11:08 1708844486_656629.png
-rw-r--r-- 1 <USER> <GROUP> 53908 May 15 11:08 1708844486_728062.png
-rw-r--r-- 1 <USER> <GROUP> 54524 May 15 11:08 1708844486_800147.png
-rw-r--r-- 1 <USER> <GROUP> 52883 May 15 11:08 1708844486_873889.png
-rw-r--r-- 1 <USER> <GROUP> 52851 May 15 11:08 1708844486_943997.png
-rw-r--r-- 1 <USER> <GROUP> 54338 May 15 11:08 1708844487_017116.png
-rw-r--r-- 1 <USER> <GROUP> 54010 May 15 11:08 1708844487_088551.png
-rw-r--r-- 1 <USER> <GROUP> 54653 May 15 11:08 1708844487_161244.png
-rw-r--r-- 1 <USER> <GROUP> 53899 May 15 11:08 1708844487_233260.png
-rw-r--r-- 1 <USER> <GROUP> 54392 May 15 11:08 1708844487_306454.png
-rw-r--r-- 1 <USER> <GROUP> 54347 May 15 11:08 1708844487_377558.png
-rw-r--r-- 1 <USER> <GROUP> 54757 May 15 11:08 1708844487_450222.png
-rw-r--r-- 1 <USER> <GROUP> 54706 May 15 11:08 1708844487_522585.png
-rw-r--r-- 1 <USER> <GROUP> 54837 May 15 11:08 1708844487_593569.png
-rw-r--r-- 1 <USER> <GROUP> 54533 May 15 11:08 1708844487_667179.png
-rw-r--r-- 1 <USER> <GROUP> 54533 May 15 11:08 1708844487_737816.png
-rw-r--r-- 1 <USER> <GROUP> 54609 May 15 11:08 1708844487_811521.png
-rw-r--r-- 1 <USER> <GROUP> 54007 May 15 11:08 1708844487_881804.png
-rw-r--r-- 1 <USER> <GROUP> 54187 May 15 11:08 1708844487_954487.png
-rw-r--r-- 1 <USER> <GROUP> 54702 May 15 11:08 1708844488_025328.png
-rw-r--r-- 1 <USER> <GROUP> 54611 May 15 11:08 1708844488_100754.png
-rw-r--r-- 1 <USER> <GROUP> 54631 May 15 11:08 1708844488_171554.png
-rw-r--r-- 1 <USER> <GROUP> 54403 May 15 11:08 1708844488_244028.png
-rw-r--r-- 1 <USER> <GROUP> 54396 May 15 11:08 1708844488_316748.png
-rw-r--r-- 1 <USER> <GROUP> 54239 May 15 11:08 1708844488_387426.png
-rw-r--r-- 1 <USER> <GROUP> 54304 May 15 11:08 1708844488_461476.png
-rw-r--r-- 1 <USER> <GROUP> 54184 May 15 11:08 1708844488_531457.png
-rw-r--r-- 1 <USER> <GROUP> 54292 May 15 11:08 1708844488_604071.png
-rw-r--r-- 1 <USER> <GROUP> 54266 May 15 11:08 1708844488_676734.png
-rw-r--r-- 1 <USER> <GROUP> 54233 May 15 11:08 1708844488_749143.png
-rw-r--r-- 1 <USER> <GROUP> 54296 May 15 11:08 1708844488_821465.png
-rw-r--r-- 1 <USER> <GROUP> 54362 May 15 11:08 1708844488_892629.png
-rw-r--r-- 1 <USER> <GROUP> 53882 May 15 11:08 1708844488_964093.png
-rw-r--r-- 1 <USER> <GROUP> 53808 May 15 11:08 1708844489_037207.png
-rw-r--r-- 1 <USER> <GROUP> 53828 May 15 11:08 1708844489_109947.png
-rw-r--r-- 1 <USER> <GROUP> 53733 May 15 11:08 1708844489_181195.png
-rw-r--r-- 1 <USER> <GROUP> 52780 May 15 11:08 1708844489_252732.png
-rw-r--r-- 1 <USER> <GROUP> 52802 May 15 11:08 1708844489_326314.png
-rw-r--r-- 1 <USER> <GROUP> 52855 May 15 11:08 1708844489_396768.png
-rw-r--r-- 1 <USER> <GROUP> 52889 May 15 11:08 1708844489_469948.png
-rw-r--r-- 1 <USER> <GROUP> 52817 May 15 11:08 1708844489_542378.png
-rw-r--r-- 1 <USER> <GROUP> 52836 May 15 11:08 1708844489_615504.png
-rw-r--r-- 1 <USER> <GROUP> 52899 May 15 11:08 1708844489_688458.png
-rw-r--r-- 1 <USER> <GROUP> 52823 May 15 11:08 1708844489_758072.png
-rw-r--r-- 1 <USER> <GROUP> 52788 May 15 11:08 1708844489_831727.png
-rw-r--r-- 1 <USER> <GROUP> 52829 May 15 11:08 1708844489_903735.png
-rw-r--r-- 1 <USER> <GROUP> 52827 May 15 11:08 1708844489_976641.png
-rw-r--r-- 1 <USER> <GROUP> 52863 May 15 11:08 1708844490_046749.png
-rw-r--r-- 1 <USER> <GROUP> 52866 May 15 11:08 1708844490_120500.png
-rw-r--r-- 1 <USER> <GROUP> 52885 May 15 11:08 1708844490_192000.png
-rw-r--r-- 1 <USER> <GROUP> 52865 May 15 11:08 1708844490_264839.png
-rw-r--r-- 1 <USER> <GROUP> 52814 May 15 11:08 1708844490_336551.png
-rw-r--r-- 1 <USER> <GROUP> 52836 May 15 11:08 1708844490_409114.png
-rw-r--r-- 1 <USER> <GROUP> 52851 May 15 11:08 1708844490_481945.png
-rw-r--r-- 1 <USER> <GROUP> 52826 May 15 11:08 1708844490_553307.png
-rw-r--r-- 1 <USER> <GROUP> 52848 May 15 11:08 1708844490_623585.png
-rw-r--r-- 1 <USER> <GROUP> 52909 May 15 11:08 1708844490_698070.png
-rw-r--r-- 1 <USER> <GROUP> 52839 May 15 11:08 1708844490_768957.png
-rw-r--r-- 1 <USER> <GROUP> 52857 May 15 11:08 1708844490_841824.png
-rw-r--r-- 1 <USER> <GROUP> 52847 May 15 11:08 1708844490_914825.png
-rw-r--r-- 1 <USER> <GROUP> 52871 May 15 11:08 1708844490_986255.png
-rw-r--r-- 1 <USER> <GROUP> 52842 May 15 11:08 1708844491_058519.png
-rw-r--r-- 1 <USER> <GROUP> 52806 May 15 11:08 1708844491_130591.png
-rw-r--r-- 1 <USER> <GROUP> 52782 May 15 11:08 1708844491_201521.png
-rw-r--r-- 1 <USER> <GROUP> 52768 May 15 11:08 1708844491_275441.png
-rw-r--r-- 1 <USER> <GROUP> 52785 May 15 11:08 1708844491_347159.png
-rw-r--r-- 1 <USER> <GROUP> 52863 May 15 11:08 1708844491_418868.png
-rw-r--r-- 1 <USER> <GROUP> 52835 May 15 11:08 1708844491_492036.png
-rw-r--r-- 1 <USER> <GROUP> 52825 May 15 11:08 1708844491_563440.png
-rw-r--r-- 1 <USER> <GROUP> 52842 May 15 11:08 1708844491_636666.png
-rw-r--r-- 1 <USER> <GROUP> 52799 May 15 11:08 1708844491_707933.png
-rw-r--r-- 1 <USER> <GROUP> 52770 May 15 11:08 1708844491_779977.png
-rw-r--r-- 1 <USER> <GROUP> 52843 May 15 11:08 1708844491_851969.png
-rw-r--r-- 1 <USER> <GROUP> 52848 May 15 11:08 1708844491_923588.png
-rw-r--r-- 1 <USER> <GROUP> 52830 May 15 11:08 1708844491_996787.png
-rw-r--r-- 1 <USER> <GROUP> 52907 May 15 11:08 1708844492_068996.png
-rw-r--r-- 1 <USER> <GROUP> 52822 May 15 11:08 1708844492_140446.png
-rw-r--r-- 1 <USER> <GROUP> 52756 May 15 11:08 1708844492_211677.png
-rw-r--r-- 1 <USER> <GROUP> 52848 May 15 11:08 1708844492_283881.png
-rw-r--r-- 1 <USER> <GROUP> 52780 May 15 11:08 1708844492_357533.png
-rw-r--r-- 1 <USER> <GROUP> 52808 May 15 11:08 1708844492_431926.png
-rw-r--r-- 1 <USER> <GROUP> 52820 May 15 11:08 1708844492_501705.png
-rw-r--r-- 1 <USER> <GROUP> 52807 May 15 11:08 1708844492_573483.png
-rw-r--r-- 1 <USER> <GROUP> 52823 May 15 11:08 1708844492_646567.png
-rw-r--r-- 1 <USER> <GROUP> 52828 May 15 11:08 1708844492_718360.png
-rw-r--r-- 1 <USER> <GROUP> 52845 May 15 11:08 1708844492_789945.png
-rw-r--r-- 1 <USER> <GROUP> 52826 May 15 11:08 1708844492_863343.png
-rw-r--r-- 1 <USER> <GROUP> 52828 May 15 11:08 1708844492_934041.png
-rw-r--r-- 1 <USER> <GROUP> 52842 May 15 11:08 1708844493_007183.png
-rw-r--r-- 1 <USER> <GROUP> 52855 May 15 11:08 1708844493_078768.png
-rw-r--r-- 1 <USER> <GROUP> 52808 May 15 11:08 1708844493_151081.png
-rw-r--r-- 1 <USER> <GROUP> 52778 May 15 11:08 1708844493_223314.png
-rw-r--r-- 1 <USER> <GROUP> 52855 May 15 11:08 1708844493_295958.png
-rw-r--r-- 1 <USER> <GROUP> 52817 May 15 11:08 1708844493_366770.png
-rw-r--r-- 1 <USER> <GROUP> 52845 May 15 11:08 1708844493_439682.png
-rw-r--r-- 1 <USER> <GROUP> 52804 May 15 11:08 1708844493_511538.png
-rw-r--r-- 1 <USER> <GROUP> 52815 May 15 11:08 1708844493_584447.png
-rw-r--r-- 1 <USER> <GROUP> 52795 May 15 11:08 1708844493_654751.png
-rw-r--r-- 1 <USER> <GROUP> 52835 May 15 11:08 1708844493_728260.png
-rw-r--r-- 1 <USER> <GROUP> 52896 May 15 11:08 1708844493_800790.png
-rw-r--r-- 1 <USER> <GROUP> 52814 May 15 11:08 1708844493_873597.png
-rw-r--r-- 1 <USER> <GROUP> 52833 May 15 11:08 1708844493_944661.png
-rw-r--r-- 1 <USER> <GROUP> 52859 May 15 11:08 1708844494_016042.png
-rw-r--r-- 1 <USER> <GROUP> 52892 May 15 11:08 1708844494_088526.png
-rw-r--r-- 1 <USER> <GROUP> 52881 May 15 11:08 1708844494_160598.png
-rw-r--r-- 1 <USER> <GROUP> 52849 May 15 11:08 1708844494_234020.png
-rw-r--r-- 1 <USER> <GROUP> 52884 May 15 11:08 1708844494_306586.png
-rw-r--r-- 1 <USER> <GROUP> 52804 May 15 11:08 1708844494_378221.png
-rw-r--r-- 1 <USER> <GROUP> 52845 May 15 11:08 1708844494_449463.png
-rw-r--r-- 1 <USER> <GROUP> 52841 May 15 11:08 1708844494_522087.png
-rw-r--r-- 1 <USER> <GROUP> 52857 May 15 11:08 1708844494_594799.png
-rw-r--r-- 1 <USER> <GROUP> 52898 May 15 11:08 1708844494_666588.png
-rw-r--r-- 1 <USER> <GROUP> 52821 May 15 11:08 1708844494_740107.png
-rw-r--r-- 1 <USER> <GROUP> 52867 May 15 11:08 1708844494_882536.png
-rw-r--r-- 1 <USER> <GROUP> 52841 May 15 11:08 1708844494_956126.png
-rw-r--r-- 1 <USER> <GROUP> 52842 May 15 11:08 1708844495_027895.png
-rw-r--r-- 1 <USER> <GROUP> 52816 May 15 11:08 1708844495_100744.png
-rw-r--r-- 1 <USER> <GROUP> 52744 May 15 11:08 1708844495_172115.png
-rw-r--r-- 1 <USER> <GROUP> 52829 May 15 11:08 1708844495_244638.png
-rw-r--r-- 1 <USER> <GROUP> 53717 May 15 11:08 1708844495_316011.png
-rw-r--r-- 1 <USER> <GROUP> 52853 May 15 11:08 1708844495_388150.png
-rw-r--r-- 1 <USER> <GROUP> 52809 May 15 11:08 1708844495_461130.png
-rw-r--r-- 1 <USER> <GROUP> 52820 May 15 11:08 1708844495_534240.png
-rw-r--r-- 1 <USER> <GROUP> 52852 May 15 11:08 1708844495_605033.png
-rw-r--r-- 1 <USER> <GROUP> 52816 May 15 11:08 1708844495_678423.png
-rw-r--r-- 1 <USER> <GROUP> 52800 May 15 11:08 1708844495_749675.png
-rw-r--r-- 1 <USER> <GROUP> 52852 May 15 11:08 1708844495_821858.png
-rw-r--r-- 1 <USER> <GROUP> 52838 May 15 11:08 1708844495_894244.png
-rw-r--r-- 1 <USER> <GROUP> 52906 May 15 11:08 1708844495_966889.png
-rw-r--r-- 1 <USER> <GROUP> 52852 May 15 11:08 1708844496_038729.png
-rw-r--r-- 1 <USER> <GROUP> 52872 May 15 11:08 1708844496_109309.png
-rw-r--r-- 1 <USER> <GROUP> 52820 May 15 11:08 1708844496_182759.png
-rw-r--r-- 1 <USER> <GROUP> 52794 May 15 11:08 1708844496_254257.png
-rw-r--r-- 1 <USER> <GROUP> 52789 May 15 11:08 1708844496_326773.png
-rw-r--r-- 1 <USER> <GROUP> 54608 May 15 11:08 1708844496_399917.png
-rw-r--r-- 1 <USER> <GROUP> 52856 May 15 11:08 1708844496_470920.png
-rw-r--r-- 1 <USER> <GROUP> 52827 May 15 11:08 1708844496_544439.png
-rw-r--r-- 1 <USER> <GROUP> 52807 May 15 11:08 1708844496_615541.png
-rw-r--r-- 1 <USER> <GROUP> 52869 May 15 11:08 1708844496_688181.png
-rw-r--r-- 1 <USER> <GROUP> 52834 May 15 11:08 1708844496_760193.png
-rw-r--r-- 1 <USER> <GROUP> 52843 May 15 11:08 1708844496_832565.png
-rw-r--r-- 1 <USER> <GROUP> 52895 May 15 11:08 1708844496_906368.png
-rw-r--r-- 1 <USER> <GROUP> 52843 May 15 11:08 1708844496_976320.png
-rw-r--r-- 1 <USER> <GROUP> 52852 May 15 11:08 1708844497_048655.png
-rw-r--r-- 1 <USER> <GROUP> 52758 May 15 11:08 1708844497_121367.png
-rw-r--r-- 1 <USER> <GROUP> 52780 May 15 11:08 1708844497_192574.png
-rw-r--r-- 1 <USER> <GROUP> 52806 May 15 11:08 1708844497_266119.png
-rw-r--r-- 1 <USER> <GROUP> 52781 May 15 11:08 1708844497_339751.png
-rw-r--r-- 1 <USER> <GROUP> 52822 May 15 11:08 1708844497_409631.png
-rw-r--r-- 1 <USER> <GROUP> 52800 May 15 11:08 1708844497_481674.png
-rw-r--r-- 1 <USER> <GROUP> 52782 May 15 11:08 1708844497_554123.png
-rw-r--r-- 1 <USER> <GROUP> 52832 May 15 11:08 1708844497_626867.png
-rw-r--r-- 1 <USER> <GROUP> 52842 May 15 11:08 1708844497_698516.png
-rw-r--r-- 1 <USER> <GROUP> 52805 May 15 11:08 1708844497_770626.png
-rw-r--r-- 1 <USER> <GROUP> 52818 May 15 11:08 1708844497_842748.png
-rw-r--r-- 1 <USER> <GROUP> 52830 May 15 11:08 1708844497_913460.png
-rw-r--r-- 1 <USER> <GROUP> 52903 May 15 11:08 1708844497_988028.png
-rw-r--r-- 1 <USER> <GROUP> 53717 May 15 11:08 1708844498_059739.png
-rw-r--r-- 1 <USER> <GROUP> 52803 May 15 11:08 1708844498_131440.png
-rw-r--r-- 1 <USER> <GROUP> 52820 May 15 11:08 1708844498_204179.png
-rw-r--r-- 1 <USER> <GROUP> 52799 May 15 11:08 1708844498_276243.png
-rw-r--r-- 1 <USER> <GROUP> 53621 May 15 11:08 1708844498_347696.png
-rw-r--r-- 1 <USER> <GROUP> 52835 May 15 11:08 1708844498_419979.png
-rw-r--r-- 1 <USER> <GROUP> 52859 May 15 11:08 1708844498_492881.png
-rw-r--r-- 1 <USER> <GROUP> 52841 May 15 11:08 1708844498_565346.png
-rw-r--r-- 1 <USER> <GROUP> 52831 May 15 11:08 1708844498_637269.png
-rw-r--r-- 1 <USER> <GROUP> 52819 May 15 11:08 1708844498_709512.png
-rw-r--r-- 1 <USER> <GROUP> 52844 May 15 11:08 1708844498_780445.png
-rw-r--r-- 1 <USER> <GROUP> 53795 May 15 11:08 1708844498_852072.png
-rw-r--r-- 1 <USER> <GROUP> 54013 May 15 11:08 1708844498_926419.png
-rw-r--r-- 1 <USER> <GROUP> 54060 May 15 11:08 1708844498_998417.png
-rw-r--r-- 1 <USER> <GROUP> 54028 May 15 11:08 1708844499_070655.png
-rw-r--r-- 1 <USER> <GROUP> 54080 May 15 11:08 1708844499_143114.png
-rw-r--r-- 1 <USER> <GROUP> 54045 May 15 11:08 1708844499_215051.png
-rw-r--r-- 1 <USER> <GROUP> 54317 May 15 11:08 1708844499_287832.png
-rw-r--r-- 1 <USER> <GROUP> 54423 May 15 11:08 1708844499_358836.png
-rw-r--r-- 1 <USER> <GROUP> 54435 May 15 11:08 1708844499_431094.png
-rw-r--r-- 1 <USER> <GROUP> 54400 May 15 11:08 1708844499_502775.png
-rw-r--r-- 1 <USER> <GROUP> 54972 May 15 11:08 1708844499_575834.png
-rw-r--r-- 1 <USER> <GROUP> 54817 May 15 11:08 1708844499_648231.png
-rw-r--r-- 1 <USER> <GROUP> 54524 May 15 11:08 1708844499_719744.png
-rw-r--r-- 1 <USER> <GROUP> 54254 May 15 11:08 1708844499_791162.png
-rw-r--r-- 1 <USER> <GROUP> 54722 May 15 11:08 1708844499_862728.png
-rw-r--r-- 1 <USER> <GROUP> 54607 May 15 11:08 1708844499_936744.png
-rw-r--r-- 1 <USER> <GROUP> 54758 May 15 11:08 1708844500_007390.png
-rw-r--r-- 1 <USER> <GROUP> 53691 May 15 11:08 1708844500_079915.png
-rw-r--r-- 1 <USER> <GROUP> 54689 May 15 11:08 1708844500_151713.png
-rw-r--r-- 1 <USER> <GROUP> 54873 May 15 11:08 1708844500_224992.png
-rw-r--r-- 1 <USER> <GROUP> 55112 May 15 11:08 1708844500_296841.png
-rw-r--r-- 1 <USER> <GROUP> 53908 May 15 11:08 1708844500_368278.png
-rw-r--r-- 1 <USER> <GROUP> 53928 May 15 11:08 1708844500_442386.png
-rw-r--r-- 1 <USER> <GROUP> 54599 May 15 11:08 1708844500_513517.png
-rw-r--r-- 1 <USER> <GROUP> 54440 May 15 11:08 1708844500_586729.png
-rw-r--r-- 1 <USER> <GROUP> 54639 May 15 11:08 1708844500_658882.png
-rw-r--r-- 1 <USER> <GROUP> 54399 May 15 11:08 1708844500_729114.png
-rw-r--r-- 1 <USER> <GROUP> 53494 May 15 11:08 1708844500_801614.png
-rw-r--r-- 1 <USER> <GROUP> 53408 May 15 11:08 1708844500_874971.png
-rw-r--r-- 1 <USER> <GROUP> 53785 May 15 11:08 1708844500_947074.png
-rw-r--r-- 1 <USER> <GROUP> 54398 May 15 11:08 1708844501_018674.png
-rw-r--r-- 1 <USER> <GROUP> 54626 May 15 11:08 1708844501_090189.png
-rw-r--r-- 1 <USER> <GROUP> 55218 May 15 11:08 1708844501_162345.png
-rw-r--r-- 1 <USER> <GROUP> 55013 May 15 11:08 1708844501_235727.png
-rw-r--r-- 1 <USER> <GROUP> 54962 May 15 11:08 1708844501_306275.png
-rw-r--r-- 1 <USER> <GROUP> 55443 May 15 11:08 1708844501_378510.png
-rw-r--r-- 1 <USER> <GROUP> 55419 May 15 11:08 1708844501_449401.png
-rw-r--r-- 1 <USER> <GROUP> 55114 May 15 11:08 1708844501_523870.png
-rw-r--r-- 1 <USER> <GROUP> 55432 May 15 11:08 1708844501_595749.png
-rw-r--r-- 1 <USER> <GROUP> 55532 May 15 11:08 1708844501_668421.png
-rw-r--r-- 1 <USER> <GROUP> 55303 May 15 11:08 1708844501_740742.png
-rw-r--r-- 1 <USER> <GROUP> 54783 May 15 11:08 1708844501_811313.png
-rw-r--r-- 1 <USER> <GROUP> 54849 May 15 11:08 1708844501_883730.png
-rw-r--r-- 1 <USER> <GROUP> 54798 May 15 11:08 1708844501_956946.png
-rw-r--r-- 1 <USER> <GROUP> 54751 May 15 11:08 1708844502_028286.png
-rw-r--r-- 1 <USER> <GROUP> 54648 May 15 11:08 1708844502_101495.png
-rw-r--r-- 1 <USER> <GROUP> 54890 May 15 11:08 1708844502_172437.png
-rw-r--r-- 1 <USER> <GROUP> 54907 May 15 11:08 1708844502_246358.png
-rw-r--r-- 1 <USER> <GROUP> 54902 May 15 11:08 1708844502_317366.png
-rw-r--r-- 1 <USER> <GROUP> 54333 May 15 11:08 1708844502_389651.png
-rw-r--r-- 1 <USER> <GROUP> 54746 May 15 11:08 1708844502_462719.png
-rw-r--r-- 1 <USER> <GROUP> 54607 May 15 11:08 1708844502_533490.png
-rw-r--r-- 1 <USER> <GROUP> 55146 May 15 11:08 1708844502_606163.png
-rw-r--r-- 1 <USER> <GROUP> 54841 May 15 11:08 1708844502_679788.png
-rw-r--r-- 1 <USER> <GROUP> 54433 May 15 11:08 1708844502_750074.png
-rw-r--r-- 1 <USER> <GROUP> 54308 May 15 11:08 1708844502_823039.png
-rw-r--r-- 1 <USER> <GROUP> 54905 May 15 11:08 1708844502_895518.png
-rw-r--r-- 1 <USER> <GROUP> 54844 May 15 11:08 1708844502_966727.png
-rw-r--r-- 1 <USER> <GROUP> 53378 May 15 11:08 1708844503_039171.png
-rw-r--r-- 1 <USER> <GROUP> 54569 May 15 11:08 1708844503_112472.png
-rw-r--r-- 1 <USER> <GROUP> 53407 May 15 11:08 1708844503_183287.png
-rw-r--r-- 1 <USER> <GROUP> 53783 May 15 11:08 1708844503_256545.png
-rw-r--r-- 1 <USER> <GROUP> 54433 May 15 11:08 1708844503_327393.png
-rw-r--r-- 1 <USER> <GROUP> 54017 May 15 11:08 1708844503_401577.png
-rw-r--r-- 1 <USER> <GROUP> 54369 May 15 11:08 1708844503_472603.png
-rw-r--r-- 1 <USER> <GROUP> 54895 May 15 11:08 1708844503_544294.png
-rw-r--r-- 1 <USER> <GROUP> 55195 May 15 11:08 1708844503_617127.png
-rw-r--r-- 1 <USER> <GROUP> 54656 May 15 11:08 1708844503_689037.png
-rw-r--r-- 1 <USER> <GROUP> 53937 May 15 11:08 1708844503_760877.png
-rw-r--r-- 1 <USER> <GROUP> 53914 May 15 11:08 1708844503_832977.png
-rw-r--r-- 1 <USER> <GROUP> 54422 May 15 11:08 1708844503_905950.png
-rw-r--r-- 1 <USER> <GROUP> 54797 May 15 11:08 1708844503_977732.png
-rw-r--r-- 1 <USER> <GROUP> 55056 May 15 11:08 1708844504_049344.png
-rw-r--r-- 1 <USER> <GROUP> 54910 May 15 11:08 1708844504_123323.png
-rw-r--r-- 1 <USER> <GROUP> 54782 May 15 11:08 1708844504_193759.png
-rw-r--r-- 1 <USER> <GROUP> 54885 May 15 11:08 1708844504_264731.png
-rw-r--r-- 1 <USER> <GROUP> 55324 May 15 11:08 1708844504_339164.png
-rw-r--r-- 1 <USER> <GROUP> 54335 May 15 11:08 1708844504_410087.png
-rw-r--r-- 1 <USER> <GROUP> 54781 May 15 11:08 1708844504_482741.png
-rw-r--r-- 1 <USER> <GROUP> 54910 May 15 11:08 1708844504_555909.png
-rw-r--r-- 1 <USER> <GROUP> 54583 May 15 11:08 1708844504_628159.png
-rw-r--r-- 1 <USER> <GROUP> 55304 May 15 11:08 1708844504_699851.png
-rw-r--r-- 1 <USER> <GROUP> 55007 May 15 11:08 1708844504_771676.png
-rw-r--r-- 1 <USER> <GROUP> 55139 May 15 11:08 1708844504_845035.png
-rw-r--r-- 1 <USER> <GROUP> 55046 May 15 11:08 1708844504_917022.png
-rw-r--r-- 1 <USER> <GROUP> 55297 May 15 11:08 1708844504_987984.png
-rw-r--r-- 1 <USER> <GROUP> 55051 May 15 11:08 1708844505_060451.png
-rw-r--r-- 1 <USER> <GROUP> 55301 May 15 11:08 1708844505_132063.png
-rw-r--r-- 1 <USER> <GROUP> 55108 May 15 11:08 1708844505_203928.png
-rw-r--r-- 1 <USER> <GROUP> 55509 May 15 11:08 1708844505_277067.png
-rw-r--r-- 1 <USER> <GROUP> 55113 May 15 11:08 1708844505_348545.png
-rw-r--r-- 1 <USER> <GROUP> 54988 May 15 11:08 1708844505_422047.png
-rw-r--r-- 1 <USER> <GROUP> 54823 May 15 11:08 1708844505_492814.png
-rw-r--r-- 1 <USER> <GROUP> 55032 May 15 11:08 1708844505_709440.png
-rw-r--r-- 1 <USER> <GROUP> 54911 May 15 11:08 1708844505_782485.png
-rw-r--r-- 1 <USER> <GROUP> 54777 May 15 11:08 1708844505_854558.png
-rw-r--r-- 1 <USER> <GROUP> 54830 May 15 11:08 1708844505_926500.png
-rw-r--r-- 1 <USER> <GROUP> 54890 May 15 11:08 1708844505_998897.png
-rw-r--r-- 1 <USER> <GROUP> 54533 May 15 11:08 1708844506_071388.png
-rw-r--r-- 1 <USER> <GROUP> 54406 May 15 11:08 1708844506_143099.png
-rw-r--r-- 1 <USER> <GROUP> 54759 May 15 11:08 1708844506_215210.png
-rw-r--r-- 1 <USER> <GROUP> 55165 May 15 11:08 1708844506_287872.png
-rw-r--r-- 1 <USER> <GROUP> 55191 May 15 11:08 1708844506_359205.png
-rw-r--r-- 1 <USER> <GROUP> 55082 May 15 11:08 1708844506_430480.png
-rw-r--r-- 1 <USER> <GROUP> 55014 May 15 11:08 1708844506_504385.png
-rw-r--r-- 1 <USER> <GROUP> 54728 May 15 11:08 1708844506_576891.png
-rw-r--r-- 1 <USER> <GROUP> 54883 May 15 11:08 1708844506_648984.png
-rw-r--r-- 1 <USER> <GROUP> 55189 May 15 11:08 1708844506_719365.png
-rw-r--r-- 1 <USER> <GROUP> 55282 May 15 11:08 1708844506_791373.png
-rw-r--r-- 1 <USER> <GROUP> 55265 May 15 11:08 1708844506_865777.png
-rw-r--r-- 1 <USER> <GROUP> 55016 May 15 11:08 1708844506_937799.png
-rw-r--r-- 1 <USER> <GROUP> 55060 May 15 11:08 1708844507_010204.png
-rw-r--r-- 1 <USER> <GROUP> 54727 May 15 11:08 1708844507_080783.png
-rw-r--r-- 1 <USER> <GROUP> 54785 May 15 11:08 1708844507_153035.png
-rw-r--r-- 1 <USER> <GROUP> 54532 May 15 11:08 1708844507_225248.png
-rw-r--r-- 1 <USER> <GROUP> 54452 May 15 11:08 1708844507_298235.png
-rw-r--r-- 1 <USER> <GROUP> 54821 May 15 11:08 1708844507_370406.png
-rw-r--r-- 1 <USER> <GROUP> 54653 May 15 11:08 1708844507_442104.png
-rw-r--r-- 1 <USER> <GROUP> 54630 May 15 11:08 1708844507_514324.png
-rw-r--r-- 1 <USER> <GROUP> 54208 May 15 11:08 1708844507_586134.png
-rw-r--r-- 1 <USER> <GROUP> 54583 May 15 11:08 1708844507_658541.png
-rw-r--r-- 1 <USER> <GROUP> 54203 May 15 11:08 1708844507_731841.png
-rw-r--r-- 1 <USER> <GROUP> 54544 May 15 11:08 1708844507_804250.png
-rw-r--r-- 1 <USER> <GROUP> 54258 May 15 11:08 1708844507_874555.png
-rw-r--r-- 1 <USER> <GROUP> 54435 May 15 11:08 1708844507_946823.png
-rw-r--r-- 1 <USER> <GROUP> 53897 May 15 11:08 1708844508_018773.png
-rw-r--r-- 1 <USER> <GROUP> 53813 May 15 11:08 1708844508_091864.png
-rw-r--r-- 1 <USER> <GROUP> 53582 May 15 11:08 1708844508_165220.png
-rw-r--r-- 1 <USER> <GROUP> 53624 May 15 11:08 1708844508_236330.png
-rw-r--r-- 1 <USER> <GROUP> 53644 May 15 11:08 1708844508_308957.png
-rw-r--r-- 1 <USER> <GROUP> 53624 May 15 11:08 1708844508_379635.png
-rw-r--r-- 1 <USER> <GROUP> 52818 May 15 11:08 1708844508_452262.png
-rw-r--r-- 1 <USER> <GROUP> 52858 May 15 11:08 1708844508_598219.png
-rw-r--r-- 1 <USER> <GROUP> 52883 May 15 11:08 1708844508_669664.png
-rw-r--r-- 1 <USER> <GROUP> 52808 May 15 11:08 1708844508_741644.png
-rw-r--r-- 1 <USER> <GROUP> 52836 May 15 11:08 1708844508_812874.png
-rw-r--r-- 1 <USER> <GROUP> 52882 May 15 11:08 1708844508_885520.png
-rw-r--r-- 1 <USER> <GROUP> 52810 May 15 11:08 1708844508_957251.png
-rw-r--r-- 1 <USER> <GROUP> 53805 May 15 11:08 1708844509_030753.png
-rw-r--r-- 1 <USER> <GROUP> 52884 May 15 11:08 1708844509_100669.png
-rw-r--r-- 1 <USER> <GROUP> 52802 May 15 11:08 1708844509_173816.png
-rw-r--r-- 1 <USER> <GROUP> 53806 May 15 11:08 1708844509_246845.png
-rw-r--r-- 1 <USER> <GROUP> 53480 May 15 11:08 1708844509_318616.png
-rw-r--r-- 1 <USER> <GROUP> 53590 May 15 11:08 1708844509_392061.png
-rw-r--r-- 1 <USER> <GROUP> 52827 May 15 11:08 1708844509_463545.png
-rw-r--r-- 1 <USER> <GROUP> 52825 May 15 11:08 1708844509_534365.png
-rw-r--r-- 1 <USER> <GROUP> 52909 May 15 11:08 1708844509_606848.png
-rw-r--r-- 1 <USER> <GROUP> 52805 May 15 11:08 1708844509_678173.png
-rw-r--r-- 1 <USER> <GROUP> 52781 May 15 11:08 1708844509_752547.png
-rw-r--r-- 1 <USER> <GROUP> 52830 May 15 11:08 1708844509_822957.png
-rw-r--r-- 1 <USER> <GROUP> 52859 May 15 11:08 1708844509_896342.png
-rw-r--r-- 1 <USER> <GROUP> 52842 May 15 11:08 1708844509_967832.png
-rw-r--r-- 1 <USER> <GROUP> 52810 May 15 11:08 1708844510_041156.png
-rw-r--r-- 1 <USER> <GROUP> 52786 May 15 11:08 1708844510_112992.png
-rw-r--r-- 1 <USER> <GROUP> 52810 May 15 11:08 1708844510_184570.png
-rw-r--r-- 1 <USER> <GROUP> 52812 May 15 11:08 1708844510_256667.png
-rw-r--r-- 1 <USER> <GROUP> 52783 May 15 11:08 1708844510_328311.png
-rw-r--r-- 1 <USER> <GROUP> 52900 May 15 11:08 1708844510_400890.png
-rw-r--r-- 1 <USER> <GROUP> 52809 May 15 11:08 1708844510_472289.png
-rw-r--r-- 1 <USER> <GROUP> 52797 May 15 11:08 1708844510_544525.png
-rw-r--r-- 1 <USER> <GROUP> 52862 May 15 11:08 1708844510_618660.png
-rw-r--r-- 1 <USER> <GROUP> 52841 May 15 11:08 1708844510_690379.png
-rw-r--r-- 1 <USER> <GROUP> 52824 May 15 11:08 1708844510_761488.png
-rw-r--r-- 1 <USER> <GROUP> 52843 May 15 11:08 1708844510_834299.png
-rw-r--r-- 1 <USER> <GROUP> 52811 May 15 11:08 1708844510_907541.png
-rw-r--r-- 1 <USER> <GROUP> 52844 May 15 11:08 1708844510_978484.png
-rw-r--r-- 1 <USER> <GROUP> 52761 May 15 11:08 1708844511_051437.png
-rw-r--r-- 1 <USER> <GROUP> 52746 May 15 11:08 1708844511_123525.png
-rw-r--r-- 1 <USER> <GROUP> 53632 May 15 11:08 1708844511_196467.png
-rw-r--r-- 1 <USER> <GROUP> 52749 May 15 11:08 1708844511_267575.png
-rw-r--r-- 1 <USER> <GROUP> 52805 May 15 11:08 1708844511_338799.png
-rw-r--r-- 1 <USER> <GROUP> 52810 May 15 11:08 1708844511_412806.png
-rw-r--r-- 1 <USER> <GROUP> 52746 May 15 11:08 1708844511_483177.png
-rw-r--r-- 1 <USER> <GROUP> 52824 May 15 11:08 1708844511_556598.png
-rw-r--r-- 1 <USER> <GROUP> 52762 May 15 11:08 1708844511_628137.png
-rw-r--r-- 1 <USER> <GROUP> 52797 May 15 11:08 1708844511_701063.png
-rw-r--r-- 1 <USER> <GROUP> 52777 May 15 11:08 1708844511_772094.png
-rw-r--r-- 1 <USER> <GROUP> 53458 May 15 11:08 1708844511_844683.png
-rw-r--r-- 1 <USER> <GROUP> 52765 May 15 11:08 1708844511_917318.png
-rw-r--r-- 1 <USER> <GROUP> 52827 May 15 11:08 1708844511_988764.png
-rw-r--r-- 1 <USER> <GROUP> 52804 May 15 11:08 1708844512_062476.png
-rw-r--r-- 1 <USER> <GROUP> 53279 May 15 11:08 1708844512_132426.png
-rw-r--r-- 1 <USER> <GROUP> 53451 May 15 11:08 1708844512_204909.png
-rw-r--r-- 1 <USER> <GROUP> 53346 May 15 11:08 1708844512_277813.png
-rw-r--r-- 1 <USER> <GROUP> 53371 May 15 11:08 1708844512_350219.png
-rw-r--r-- 1 <USER> <GROUP> 52783 May 15 11:08 1708844512_421987.png
-rw-r--r-- 1 <USER> <GROUP> 52783 May 15 11:08 1708844512_494415.png
-rw-r--r-- 1 <USER> <GROUP> 53854 May 15 11:08 1708844512_565892.png
-rw-r--r-- 1 <USER> <GROUP> 52771 May 15 11:08 1708844512_639137.png
-rw-r--r-- 1 <USER> <GROUP> 52805 May 15 11:08 1708844512_710836.png
-rw-r--r-- 1 <USER> <GROUP> 52785 May 15 11:08 1708844512_782454.png
-rw-r--r-- 1 <USER> <GROUP> 52842 May 15 11:08 1708844512_855996.png
-rw-r--r-- 1 <USER> <GROUP> 52793 May 15 11:08 1708844512_927304.png
-rw-r--r-- 1 <USER> <GROUP> 52867 May 15 11:08 1708844512_999859.png
-rw-r--r-- 1 <USER> <GROUP> 52765 May 15 11:08 1708844513_071352.png
-rw-r--r-- 1 <USER> <GROUP> 53983 May 15 11:08 1708844513_143733.png
-rw-r--r-- 1 <USER> <GROUP> 53429 May 15 11:08 1708844513_215440.png
-rw-r--r-- 1 <USER> <GROUP> 52796 May 15 11:08 1708844513_289214.png
-rw-r--r-- 1 <USER> <GROUP> 53563 May 15 11:08 1708844513_359755.png
-rw-r--r-- 1 <USER> <GROUP> 53403 May 15 11:08 1708844513_432639.png
-rw-r--r-- 1 <USER> <GROUP> 53563 May 15 11:08 1708844513_504711.png
-rw-r--r-- 1 <USER> <GROUP> 52777 May 15 11:08 1708844513_576535.png
-rw-r--r-- 1 <USER> <GROUP> 52840 May 15 11:08 1708844513_649486.png
-rw-r--r-- 1 <USER> <GROUP> 52793 May 15 11:08 1708844513_720661.png
-rw-r--r-- 1 <USER> <GROUP> 53971 May 15 11:08 1708844513_792673.png
-rw-r--r-- 1 <USER> <GROUP> 53532 May 15 11:08 1708844513_865997.png
-rw-r--r-- 1 <USER> <GROUP> 54156 May 15 11:08 1708844513_938798.png
-rw-r--r-- 1 <USER> <GROUP> 53557 May 15 11:08 1708844514_010417.png
-rw-r--r-- 1 <USER> <GROUP> 53603 May 15 11:08 1708844514_080721.png
-rw-r--r-- 1 <USER> <GROUP> 54428 May 15 11:08 1708844514_154315.png
-rw-r--r-- 1 <USER> <GROUP> 54618 May 15 11:08 1708844514_225366.png
-rw-r--r-- 1 <USER> <GROUP> 53712 May 15 11:08 1708844514_298683.png
-rw-r--r-- 1 <USER> <GROUP> 53574 May 15 11:08 1708844514_371918.png
-rw-r--r-- 1 <USER> <GROUP> 52796 May 15 11:08 1708844514_442783.png
-rw-r--r-- 1 <USER> <GROUP> 53734 May 15 11:08 1708844514_516262.png
-rw-r--r-- 1 <USER> <GROUP> 52864 May 15 11:08 1708844514_588585.png
-rw-r--r-- 1 <USER> <GROUP> 52845 May 15 11:08 1708844514_660074.png
-rw-r--r-- 1 <USER> <GROUP> 53956 May 15 11:08 1708844514_732340.png
-rw-r--r-- 1 <USER> <GROUP> 53866 May 15 11:08 1708844514_802987.png
-rw-r--r-- 1 <USER> <GROUP> 54137 May 15 11:08 1708844514_876952.png
-rw-r--r-- 1 <USER> <GROUP> 54466 May 15 11:08 1708844514_948911.png
-rw-r--r-- 1 <USER> <GROUP> 54303 May 15 11:08 1708844515_020123.png
-rw-r--r-- 1 <USER> <GROUP> 54269 May 15 11:08 1708844515_092130.png
-rw-r--r-- 1 <USER> <GROUP> 54210 May 15 11:08 1708844515_164398.png
-rw-r--r-- 1 <USER> <GROUP> 54299 May 15 11:08 1708844515_237471.png
-rw-r--r-- 1 <USER> <GROUP> 54483 May 15 11:08 1708844515_309115.png
-rw-r--r-- 1 <USER> <GROUP> 54679 May 15 11:08 1708844515_381566.png
-rw-r--r-- 1 <USER> <GROUP> 54854 May 15 11:08 1708844515_453049.png
-rw-r--r-- 1 <USER> <GROUP> 54783 May 15 11:08 1708844515_525053.png
-rw-r--r-- 1 <USER> <GROUP> 54866 May 15 11:08 1708844515_598079.png
-rw-r--r-- 1 <USER> <GROUP> 54925 May 15 11:08 1708844515_670964.png
-rw-r--r-- 1 <USER> <GROUP> 54755 May 15 11:08 1708844515_742513.png
-rw-r--r-- 1 <USER> <GROUP> 54647 May 15 11:08 1708844515_815433.png
-rw-r--r-- 1 <USER> <GROUP> 55039 May 15 11:08 1708844515_886800.png
-rw-r--r-- 1 <USER> <GROUP> 54884 May 15 11:08 1708844515_958506.png
-rw-r--r-- 1 <USER> <GROUP> 55000 May 15 11:08 1708844516_030510.png
-rw-r--r-- 1 <USER> <GROUP> 54959 May 15 11:08 1708844516_103624.png
-rw-r--r-- 1 <USER> <GROUP> 54822 May 15 11:08 1708844516_176163.png
-rw-r--r-- 1 <USER> <GROUP> 54874 May 15 11:08 1708844516_247749.png
-rw-r--r-- 1 <USER> <GROUP> 54281 May 15 11:08 1708844516_464539.png
-rw-r--r-- 1 <USER> <GROUP> 54002 May 15 11:08 1708844516_537350.png
-rw-r--r-- 1 <USER> <GROUP> 54188 May 15 11:08 1708844516_609355.png
-rw-r--r-- 1 <USER> <GROUP> 54600 May 15 11:08 1708844516_680549.png
-rw-r--r-- 1 <USER> <GROUP> 54443 May 15 11:08 1708844516_752553.png
-rw-r--r-- 1 <USER> <GROUP> 54250 May 15 11:08 1708844516_825726.png
-rw-r--r-- 1 <USER> <GROUP> 54369 May 15 11:08 1708844516_898013.png
-rw-r--r-- 1 <USER> <GROUP> 54246 May 15 11:08 1708844516_968396.png
-rw-r--r-- 1 <USER> <GROUP> 54084 May 15 11:08 1708844517_040849.png
-rw-r--r-- 1 <USER> <GROUP> 54003 May 15 11:08 1708844517_112919.png
-rw-r--r-- 1 <USER> <GROUP> 54043 May 15 11:08 1708844517_185637.png
-rw-r--r-- 1 <USER> <GROUP> 53830 May 15 11:08 1708844517_257347.png
-rw-r--r-- 1 <USER> <GROUP> 54061 May 15 11:08 1708844517_331113.png
-rw-r--r-- 1 <USER> <GROUP> 53705 May 15 11:08 1708844517_401483.png
-rw-r--r-- 1 <USER> <GROUP> 53742 May 15 11:08 1708844517_473139.png
-rw-r--r-- 1 <USER> <GROUP> 53514 May 15 11:08 1708844517_545928.png
-rw-r--r-- 1 <USER> <GROUP> 54035 May 15 11:08 1708844517_617912.png
-rw-r--r-- 1 <USER> <GROUP> 52832 May 15 11:08 1708844517_691084.png
-rw-r--r-- 1 <USER> <GROUP> 54461 May 15 11:08 1708844517_763965.png
-rw-r--r-- 1 <USER> <GROUP> 54733 May 15 11:08 1708844517_835345.png
-rw-r--r-- 1 <USER> <GROUP> 54595 May 15 11:08 1708844517_906742.png
-rw-r--r-- 1 <USER> <GROUP> 54466 May 15 11:08 1708844517_980129.png
-rw-r--r-- 1 <USER> <GROUP> 52784 May 15 11:08 1708844518_051572.png
-rw-r--r-- 1 <USER> <GROUP> 54175 May 15 11:08 1708844518_124446.png
-rw-r--r-- 1 <USER> <GROUP> 54173 May 15 11:08 1708844518_196327.png
-rw-r--r-- 1 <USER> <GROUP> 54768 May 15 11:08 1708844518_267683.png
-rw-r--r-- 1 <USER> <GROUP> 54684 May 15 11:08 1708844518_339825.png
-rw-r--r-- 1 <USER> <GROUP> 54574 May 15 11:08 1708844518_411807.png
-rw-r--r-- 1 <USER> <GROUP> 54532 May 15 11:08 1708844518_484711.png
-rw-r--r-- 1 <USER> <GROUP> 54573 May 15 11:08 1708844518_556690.png
-rw-r--r-- 1 <USER> <GROUP> 54613 May 15 11:08 1708844518_629810.png
-rw-r--r-- 1 <USER> <GROUP> 54543 May 15 11:08 1708844518_700595.png
-rw-r--r-- 1 <USER> <GROUP> 54444 May 15 11:08 1708844518_774150.png
-rw-r--r-- 1 <USER> <GROUP> 54317 May 15 11:08 1708844518_845211.png
-rw-r--r-- 1 <USER> <GROUP> 54325 May 15 11:08 1708844518_917981.png
-rw-r--r-- 1 <USER> <GROUP> 54456 May 15 11:08 1708844518_989110.png
-rw-r--r-- 1 <USER> <GROUP> 54305 May 15 11:08 1708844519_060264.png
-rw-r--r-- 1 <USER> <GROUP> 52749 May 15 11:08 1708844519_134125.png
-rw-r--r-- 1 <USER> <GROUP> 54254 May 15 11:08 1708844519_206419.png
-rw-r--r-- 1 <USER> <GROUP> 54121 May 15 11:08 1708844519_279008.png
-rw-r--r-- 1 <USER> <GROUP> 54661 May 15 11:08 1708844519_350868.png
-rw-r--r-- 1 <USER> <GROUP> 52724 May 15 11:08 1708844519_421717.png
-rw-r--r-- 1 <USER> <GROUP> 52807 May 15 11:08 1708844519_493667.png
-rw-r--r-- 1 <USER> <GROUP> 52785 May 15 11:08 1708844519_567749.png
-rw-r--r-- 1 <USER> <GROUP> 52818 May 15 11:08 1708844519_639924.png
-rw-r--r-- 1 <USER> <GROUP> 52804 May 15 11:08 1708844519_710956.png
-rw-r--r-- 1 <USER> <GROUP> 52784 May 15 11:08 1708844519_784523.png
-rw-r--r-- 1 <USER> <GROUP> 52832 May 15 11:08 1708844519_855198.png
-rw-r--r-- 1 <USER> <GROUP> 52846 May 15 11:08 1708844519_928062.png
-rw-r--r-- 1 <USER> <GROUP> 53657 May 15 11:08 1708844520_000795.png
-rw-r--r-- 1 <USER> <GROUP> 53788 May 15 11:08 1708844520_072008.png
-rw-r--r-- 1 <USER> <GROUP> 53726 May 15 11:08 1708844520_145078.png
-rw-r--r-- 1 <USER> <GROUP> 52835 May 15 11:08 1708844520_216208.png
-rw-r--r-- 1 <USER> <GROUP> 53618 May 15 11:08 1708844520_288008.png
-rw-r--r-- 1 <USER> <GROUP> 53837 May 15 11:08 1708844520_361002.png
-rw-r--r-- 1 <USER> <GROUP> 52810 May 15 11:08 1708844520_433328.png
-rw-r--r-- 1 <USER> <GROUP> 53756 May 15 11:08 1708844520_505464.png
-rw-r--r-- 1 <USER> <GROUP> 53529 May 15 11:08 1708844520_577180.png
-rw-r--r-- 1 <USER> <GROUP> 53597 May 15 11:08 1708844520_648081.png
-rw-r--r-- 1 <USER> <GROUP> 52796 May 15 11:08 1708844520_719923.png
-rw-r--r-- 1 <USER> <GROUP> 53775 May 15 11:08 1708844520_794088.png
-rw-r--r-- 1 <USER> <GROUP> 53691 May 15 11:08 1708844520_867114.png
-rw-r--r-- 1 <USER> <GROUP> 53633 May 15 11:08 1708844520_937942.png
-rw-r--r-- 1 <USER> <GROUP> 52786 May 15 11:08 1708844521_011395.png
-rw-r--r-- 1 <USER> <GROUP> 53918 May 15 11:08 1708844521_081314.png
-rw-r--r-- 1 <USER> <GROUP> 54029 May 15 11:08 1708844521_154897.png
-rw-r--r-- 1 <USER> <GROUP> 53849 May 15 11:08 1708844521_227302.png
-rw-r--r-- 1 <USER> <GROUP> 52800 May 15 11:08 1708844521_299534.png
-rw-r--r-- 1 <USER> <GROUP> 53433 May 15 11:08 1708844521_371237.png
-rw-r--r-- 1 <USER> <GROUP> 52771 May 15 11:08 1708844521_442255.png
-rw-r--r-- 1 <USER> <GROUP> 52748 May 15 11:08 1708844521_517516.png
-rw-r--r-- 1 <USER> <GROUP> 52850 May 15 11:08 1708844521_587808.png
-rw-r--r-- 1 <USER> <GROUP> 52784 May 15 11:08 1708844521_661391.png
-rw-r--r-- 1 <USER> <GROUP> 52746 May 15 11:08 1708844521_732911.png
-rw-r--r-- 1 <USER> <GROUP> 52810 May 15 11:08 1708844521_803729.png
-rw-r--r-- 1 <USER> <GROUP> 52751 May 15 11:08 1708844521_876777.png
-rw-r--r-- 1 <USER> <GROUP> 52806 May 15 11:08 1708844521_949244.png
-rw-r--r-- 1 <USER> <GROUP> 52823 May 15 11:08 1708844522_021839.png
-rw-r--r-- 1 <USER> <GROUP> 52868 May 15 11:08 1708844522_093960.png
-rw-r--r-- 1 <USER> <GROUP> 52833 May 15 11:08 1708844522_238560.png
-rw-r--r-- 1 <USER> <GROUP> 52827 May 15 11:08 1708844522_310200.png
-rw-r--r-- 1 <USER> <GROUP> 52830 May 15 11:08 1708844522_382648.png
-rw-r--r-- 1 <USER> <GROUP> 52790 May 15 11:08 1708844522_454292.png
-rw-r--r-- 1 <USER> <GROUP> 52818 May 15 11:08 1708844522_525468.png
-rw-r--r-- 1 <USER> <GROUP> 52863 May 15 11:08 1708844522_598049.png
-rw-r--r-- 1 <USER> <GROUP> 52815 May 15 11:08 1708844522_670943.png
-rw-r--r-- 1 <USER> <GROUP> 52765 May 15 11:08 1708844522_742543.png
-rw-r--r-- 1 <USER> <GROUP> 52782 May 15 11:08 1708844522_815332.png
-rw-r--r-- 1 <USER> <GROUP> 52817 May 15 11:08 1708844522_887207.png
-rw-r--r-- 1 <USER> <GROUP> 52832 May 15 11:08 1708844522_959691.png
-rw-r--r-- 1 <USER> <GROUP> 52827 May 15 11:08 1708844523_031389.png
-rw-r--r-- 1 <USER> <GROUP> 52807 May 15 11:08 1708844523_103530.png
-rw-r--r-- 1 <USER> <GROUP> 52761 May 15 11:08 1708844523_175102.png
-rw-r--r-- 1 <USER> <GROUP> 52779 May 15 11:08 1708844523_247510.png
-rw-r--r-- 1 <USER> <GROUP> 52771 May 15 11:08 1708844523_320721.png
-rw-r--r-- 1 <USER> <GROUP> 52764 May 15 11:08 1708844523_392127.png
-rw-r--r-- 1 <USER> <GROUP> 52805 May 15 11:08 1708844523_464378.png
-rw-r--r-- 1 <USER> <GROUP> 52793 May 15 11:08 1708844523_537061.png
-rw-r--r-- 1 <USER> <GROUP> 52873 May 15 11:08 1708844523_609028.png
-rw-r--r-- 1 <USER> <GROUP> 52830 May 15 11:08 1708844523_681678.png
-rw-r--r-- 1 <USER> <GROUP> 52782 May 15 11:08 1708844523_752833.png
-rw-r--r-- 1 <USER> <GROUP> 52859 May 15 11:08 1708844523_826850.png
-rw-r--r-- 1 <USER> <GROUP> 52795 May 15 11:08 1708844523_897767.png
-rw-r--r-- 1 <USER> <GROUP> 52855 May 15 11:08 1708844523_969558.png
-rw-r--r-- 1 <USER> <GROUP> 52841 May 15 11:08 1708844524_043440.png
-rw-r--r-- 1 <USER> <GROUP> 52773 May 15 11:08 1708844524_114701.png
-rw-r--r-- 1 <USER> <GROUP> 52786 May 15 11:08 1708844524_187161.png
-rw-r--r-- 1 <USER> <GROUP> 52831 May 15 11:08 1708844524_258249.png
-rw-r--r-- 1 <USER> <GROUP> 52853 May 15 11:08 1708844524_329906.png
-rw-r--r-- 1 <USER> <GROUP> 52829 May 15 11:08 1708844524_402259.png
-rw-r--r-- 1 <USER> <GROUP> 52791 May 15 11:08 1708844524_474491.png
-rw-r--r-- 1 <USER> <GROUP> 52851 May 15 11:08 1708844524_619948.png
-rw-r--r-- 1 <USER> <GROUP> 52835 May 15 11:08 1708844524_692581.png
-rw-r--r-- 1 <USER> <GROUP> 52826 May 15 11:08 1708844524_763995.png
-rw-r--r-- 1 <USER> <GROUP> 52837 May 15 11:08 1708844524_836633.png
-rw-r--r-- 1 <USER> <GROUP> 52851 May 15 11:08 1708844524_909192.png
-rw-r--r-- 1 <USER> <GROUP> 52846 May 15 11:08 1708844524_979780.png
-rw-r--r-- 1 <USER> <GROUP> 52789 May 15 11:08 1708844525_052314.png
-rw-r--r-- 1 <USER> <GROUP> 52771 May 15 11:08 1708844525_124914.png
-rw-r--r-- 1 <USER> <GROUP> 52803 May 15 11:08 1708844525_196748.png
-rw-r--r-- 1 <USER> <GROUP> 52848 May 15 11:08 1708844525_268094.png
-rw-r--r-- 1 <USER> <GROUP> 52800 May 15 11:08 1708844525_340353.png
-rw-r--r-- 1 <USER> <GROUP> 54282 May 15 11:08 1708844525_412601.png
-rw-r--r-- 1 <USER> <GROUP> 53842 May 15 11:08 1708844525_485456.png
-rw-r--r-- 1 <USER> <GROUP> 53499 May 15 11:08 1708844525_557483.png
-rw-r--r-- 1 <USER> <GROUP> 52826 May 15 11:08 1708844525_629629.png
-rw-r--r-- 1 <USER> <GROUP> 52818 May 15 11:08 1708844525_702289.png
-rw-r--r-- 1 <USER> <GROUP> 52771 May 15 11:08 1708844525_773867.png
-rw-r--r-- 1 <USER> <GROUP> 52853 May 15 11:08 1708844525_846684.png
-rw-r--r-- 1 <USER> <GROUP> 52785 May 15 11:08 1708844525_919141.png
-rw-r--r-- 1 <USER> <GROUP> 52860 May 15 11:08 1708844525_990844.png
-rw-r--r-- 1 <USER> <GROUP> 52845 May 15 11:08 1708844526_062678.png
-rw-r--r-- 1 <USER> <GROUP> 52796 May 15 11:08 1708844526_136318.png
-rw-r--r-- 1 <USER> <GROUP> 52857 May 15 11:08 1708844526_206056.png
-rw-r--r-- 1 <USER> <GROUP> 54355 May 15 11:08 1708844526_280626.png
-rw-r--r-- 1 <USER> <GROUP> 52846 May 15 11:08 1708844526_352009.png
-rw-r--r-- 1 <USER> <GROUP> 52798 May 15 11:08 1708844526_423339.png
-rw-r--r-- 1 <USER> <GROUP> 52824 May 15 11:08 1708844526_493832.png
-rw-r--r-- 1 <USER> <GROUP> 52830 May 15 11:08 1708844526_568163.png
-rw-r--r-- 1 <USER> <GROUP> 52814 May 15 11:08 1708844526_641240.png
-rw-r--r-- 1 <USER> <GROUP> 52774 May 15 11:08 1708844526_713105.png
-rw-r--r-- 1 <USER> <GROUP> 52810 May 15 11:08 1708844526_786243.png
-rw-r--r-- 1 <USER> <GROUP> 52867 May 15 11:08 1708844526_856940.png
-rw-r--r-- 1 <USER> <GROUP> 52837 May 15 11:08 1708844526_929395.png
-rw-r--r-- 1 <USER> <GROUP> 52865 May 15 11:08 1708844527_001900.png
-rw-r--r-- 1 <USER> <GROUP> 52795 May 15 11:08 1708844527_072841.png
-rw-r--r-- 1 <USER> <GROUP> 52775 May 15 11:08 1708844527_146927.png
-rw-r--r-- 1 <USER> <GROUP> 52797 May 15 11:08 1708844527_219055.png
-rw-r--r-- 1 <USER> <GROUP> 52789 May 15 11:08 1708844527_291022.png
-rw-r--r-- 1 <USER> <GROUP> 52793 May 15 11:08 1708844527_360142.png
-rw-r--r-- 1 <USER> <GROUP> 52784 May 15 11:08 1708844527_434439.png
-rw-r--r-- 1 <USER> <GROUP> 52808 May 15 11:08 1708844527_506071.png
-rw-r--r-- 1 <USER> <GROUP> 52802 May 15 11:08 1708844527_578395.png
-rw-r--r-- 1 <USER> <GROUP> 52849 May 15 11:08 1708844527_650308.png
-rw-r--r-- 1 <USER> <GROUP> 52738 May 15 11:08 1708844527_723347.png
-rw-r--r-- 1 <USER> <GROUP> 52779 May 15 11:08 1708844527_795553.png
-rw-r--r-- 1 <USER> <GROUP> 52873 May 15 11:08 1708844527_868050.png
-rw-r--r-- 1 <USER> <GROUP> 52795 May 15 11:08 1708844527_939341.png
-rw-r--r-- 1 <USER> <GROUP> 52862 May 15 11:08 1708844528_010856.png
-rw-r--r-- 1 <USER> <GROUP> 52851 May 15 11:08 1708844528_083394.png
-rw-r--r-- 1 <USER> <GROUP> 52837 May 15 11:08 1708844528_156048.png
-rw-r--r-- 1 <USER> <GROUP> 52818 May 15 11:08 1708844528_228766.png
-rw-r--r-- 1 <USER> <GROUP> 52847 May 15 11:08 1708844528_300619.png
-rw-r--r-- 1 <USER> <GROUP> 52776 May 15 11:08 1708844528_373554.png
-rw-r--r-- 1 <USER> <GROUP> 52781 May 15 11:08 1708844528_443971.png
-rw-r--r-- 1 <USER> <GROUP> 52828 May 15 11:08 1708844528_518035.png
-rw-r--r-- 1 <USER> <GROUP> 52845 May 15 11:08 1708844528_589051.png
-rw-r--r-- 1 <USER> <GROUP> 52833 May 15 11:08 1708844528_661399.png
-rw-r--r-- 1 <USER> <GROUP> 52744 May 15 11:08 1708844528_733747.png
-rw-r--r-- 1 <USER> <GROUP> 52868 May 15 11:08 1708844528_805260.png
-rw-r--r-- 1 <USER> <GROUP> 52772 May 15 11:08 1708844528_877742.png
-rw-r--r-- 1 <USER> <GROUP> 52828 May 15 11:08 1708844528_949467.png
-rw-r--r-- 1 <USER> <GROUP> 52822 May 15 11:08 1708844529_021386.png
-rw-r--r-- 1 <USER> <GROUP> 52823 May 15 11:08 1708844529_094162.png
-rw-r--r-- 1 <USER> <GROUP> 52750 May 15 11:08 1708844529_167227.png
-rw-r--r-- 1 <USER> <GROUP> 52819 May 15 11:08 1708844529_238626.png
-rw-r--r-- 1 <USER> <GROUP> 52834 May 15 11:08 1708844529_308031.png
-rw-r--r-- 1 <USER> <GROUP> 52799 May 15 11:08 1708844529_382129.png
-rw-r--r-- 1 <USER> <GROUP> 52812 May 15 11:08 1708844529_455428.png
-rw-r--r-- 1 <USER> <GROUP> 52792 May 15 11:08 1708844529_527034.png
-rw-r--r-- 1 <USER> <GROUP> 52860 May 15 11:08 1708844529_599029.png
-rw-r--r-- 1 <USER> <GROUP> 53683 May 15 11:08 1708844529_670925.png
-rw-r--r-- 1 <USER> <GROUP> 52775 May 15 11:08 1708844529_743555.png
-rw-r--r-- 1 <USER> <GROUP> 52802 May 15 11:08 1708844529_815516.png
-rw-r--r-- 1 <USER> <GROUP> 52840 May 15 11:08 1708844529_888125.png
-rw-r--r-- 1 <USER> <GROUP> 52830 May 15 11:08 1708844529_960017.png
-rw-r--r-- 1 <USER> <GROUP> 54255 May 15 11:08 1708844530_032107.png
-rw-r--r-- 1 <USER> <GROUP> 54105 May 15 11:08 1708844530_104092.png
-rw-r--r-- 1 <USER> <GROUP> 52770 May 15 11:08 1708844530_176322.png
-rw-r--r-- 1 <USER> <GROUP> 52815 May 15 11:08 1708844530_248017.png
-rw-r--r-- 1 <USER> <GROUP> 52772 May 15 11:08 1708844530_321415.png
-rw-r--r-- 1 <USER> <GROUP> 52825 May 15 11:08 1708844530_393320.png
-rw-r--r-- 1 <USER> <GROUP> 52815 May 15 11:08 1708844530_464912.png
-rw-r--r-- 1 <USER> <GROUP> 54210 May 15 11:08 1708844530_537056.png
-rw-r--r-- 1 <USER> <GROUP> 54175 May 15 11:08 1708844530_609066.png
-rw-r--r-- 1 <USER> <GROUP> 54679 May 15 11:08 1708844530_681334.png
-rw-r--r-- 1 <USER> <GROUP> 54399 May 15 11:08 1708844530_754383.png
-rw-r--r-- 1 <USER> <GROUP> 54342 May 15 11:08 1708844530_826224.png
-rw-r--r-- 1 <USER> <GROUP> 54243 May 15 11:08 1708844530_898230.png
-rw-r--r-- 1 <USER> <GROUP> 54762 May 15 11:08 1708844530_971541.png
-rw-r--r-- 1 <USER> <GROUP> 54322 May 15 11:08 1708844531_042026.png
-rw-r--r-- 1 <USER> <GROUP> 54297 May 15 11:08 1708844531_113956.png
-rw-r--r-- 1 <USER> <GROUP> 54241 May 15 11:08 1708844531_187739.png
-rw-r--r-- 1 <USER> <GROUP> 54340 May 15 11:08 1708844531_258693.png
-rw-r--r-- 1 <USER> <GROUP> 54530 May 15 11:08 1708844531_331643.png
-rw-r--r-- 1 <USER> <GROUP> 52788 May 15 11:08 1708844531_404153.png
-rw-r--r-- 1 <USER> <GROUP> 54224 May 15 11:08 1708844531_474652.png
-rw-r--r-- 1 <USER> <GROUP> 54293 May 15 11:08 1708844531_548187.png
-rw-r--r-- 1 <USER> <GROUP> 54483 May 15 11:08 1708844531_620011.png
-rw-r--r-- 1 <USER> <GROUP> 52833 May 15 11:08 1708844531_692002.png
-rw-r--r-- 1 <USER> <GROUP> 54305 May 15 11:08 1708844531_764706.png
-rw-r--r-- 1 <USER> <GROUP> 54654 May 15 11:08 1708844531_835053.png
-rw-r--r-- 1 <USER> <GROUP> 54727 May 15 11:08 1708844531_908043.png
-rw-r--r-- 1 <USER> <GROUP> 54269 May 15 11:08 1708844531_979767.png
-rw-r--r-- 1 <USER> <GROUP> 54502 May 15 11:08 1708844532_053659.png
-rw-r--r-- 1 <USER> <GROUP> 54443 May 15 11:08 1708844532_125156.png
-rw-r--r-- 1 <USER> <GROUP> 54526 May 15 11:08 1708844532_198313.png
-rw-r--r-- 1 <USER> <GROUP> 54161 May 15 11:08 1708844532_268924.png
-rw-r--r-- 1 <USER> <GROUP> 54778 May 15 11:08 1708844532_342230.png
-rw-r--r-- 1 <USER> <GROUP> 54223 May 15 11:08 1708844532_413986.png
-rw-r--r-- 1 <USER> <GROUP> 54607 May 15 11:08 1708844532_485988.png
-rw-r--r-- 1 <USER> <GROUP> 52828 May 15 11:08 1708844532_558052.png
-rw-r--r-- 1 <USER> <GROUP> 54502 May 15 11:08 1708844532_629857.png
-rw-r--r-- 1 <USER> <GROUP> 54206 May 15 11:08 1708844532_702908.png
-rw-r--r-- 1 <USER> <GROUP> 54711 May 15 11:08 1708844532_775203.png
-rw-r--r-- 1 <USER> <GROUP> 54629 May 15 11:08 1708844532_846254.png
-rw-r--r-- 1 <USER> <GROUP> 54258 May 15 11:08 1708844532_917751.png
-rw-r--r-- 1 <USER> <GROUP> 54659 May 15 11:08 1708844532_990917.png
-rw-r--r-- 1 <USER> <GROUP> 79934 May 21 10:03 sim_000000.png
-rw-r--r-- 1 <USER> <GROUP> 84069 May 21 10:03 sim_000001.png
-rw-r--r-- 1 <USER> <GROUP> 84970 May 21 10:03 sim_000002.png
-rw-r--r-- 1 <USER> <GROUP> 90260 May 21 10:03 sim_000003.png
-rw-r--r-- 1 <USER> <GROUP> 88136 May 21 10:03 sim_000004.png
-rw-r--r-- 1 <USER> <GROUP> 85600 May 21 10:03 sim_000005.png
-rw-r--r-- 1 <USER> <GROUP> 89286 May 21 10:03 sim_000006.png
-rw-r--r-- 1 <USER> <GROUP> 86353 May 21 10:03 sim_000007.png
-rw-r--r-- 1 <USER> <GROUP> 84469 May 21 10:03 sim_000008.png
-rw-r--r-- 1 <USER> <GROUP> 89827 May 21 10:03 sim_000009.png
-rw-r--r-- 1 <USER> <GROUP> 82575 May 21 10:03 sim_000010.png
-rw-r--r-- 1 <USER> <GROUP> 87099 May 21 10:03 sim_000011.png
-rw-r--r-- 1 <USER> <GROUP> 89302 May 21 10:03 sim_000012.png
-rw-r--r-- 1 <USER> <GROUP> 81857 May 21 10:03 sim_000013.png
-rw-r--r-- 1 <USER> <GROUP> 87124 May 21 10:03 sim_000014.png
-rw-r--r-- 1 <USER> <GROUP> 81211 May 21 10:03 sim_000015.png
-rw-r--r-- 1 <USER> <GROUP> 82324 May 21 10:03 sim_000016.png
-rw-r--r-- 1 <USER> <GROUP> 83975 May 21 10:03 sim_000017.png
-rw-r--r-- 1 <USER> <GROUP> 83710 May 21 10:03 sim_000018.png
-rw-r--r-- 1 <USER> <GROUP> 81963 May 21 10:03 sim_000019.png
-rw-r--r-- 1 <USER> <GROUP> 84619 May 21 10:03 sim_000020.png
-rw-r--r-- 1 <USER> <GROUP> 87313 May 21 10:03 sim_000021.png
-rw-r--r-- 1 <USER> <GROUP> 89324 May 21 10:03 sim_000022.png
-rw-r--r-- 1 <USER> <GROUP> 86265 May 21 10:03 sim_000023.png
-rw-r--r-- 1 <USER> <GROUP> 86208 May 21 10:03 sim_000024.png
-rw-r--r-- 1 <USER> <GROUP> 87583 May 21 10:03 sim_000025.png
-rw-r--r-- 1 <USER> <GROUP> 81616 May 21 10:03 sim_000026.png
-rw-r--r-- 1 <USER> <GROUP> 85683 May 21 10:03 sim_000027.png
-rw-r--r-- 1 <USER> <GROUP> 84228 May 21 10:03 sim_000028.png
-rw-r--r-- 1 <USER> <GROUP> 86576 May 21 10:03 sim_000029.png
-rw-r--r-- 1 <USER> <GROUP> 85600 May 21 10:03 sim_000030.png
-rw-r--r-- 1 <USER> <GROUP> 82918 May 21 10:03 sim_000031.png
-rw-r--r-- 1 <USER> <GROUP> 88312 May 21 10:03 sim_000032.png
-rw-r--r-- 1 <USER> <GROUP> 87951 May 21 10:03 sim_000033.png
-rw-r--r-- 1 <USER> <GROUP> 89930 May 21 10:03 sim_000034.png
-rw-r--r-- 1 <USER> <GROUP> 84799 May 21 10:03 sim_000035.png
-rw-r--r-- 1 <USER> <GROUP> 85300 May 21 10:03 sim_000036.png
-rw-r--r-- 1 <USER> <GROUP> 83391 May 21 10:03 sim_000037.png
-rw-r--r-- 1 <USER> <GROUP> 79668 May 21 10:03 sim_000038.png
-rw-r--r-- 1 <USER> <GROUP> 87405 May 21 10:03 sim_000039.png
-rw-r--r-- 1 <USER> <GROUP> 87146 May 21 10:03 sim_000040.png
-rw-r--r-- 1 <USER> <GROUP> 85647 May 21 10:03 sim_000041.png
-rw-r--r-- 1 <USER> <GROUP> 85074 May 21 10:03 sim_000042.png
-rw-r--r-- 1 <USER> <GROUP> 87709 May 21 10:03 sim_000043.png
-rw-r--r-- 1 <USER> <GROUP> 82608 May 21 10:03 sim_000044.png
-rw-r--r-- 1 <USER> <GROUP> 85183 May 21 10:03 sim_000045.png
-rw-r--r-- 1 <USER> <GROUP> 85372 May 21 10:03 sim_000046.png
-rw-r--r-- 1 <USER> <GROUP> 85544 May 21 10:03 sim_000047.png
-rw-r--r-- 1 <USER> <GROUP> 86742 May 21 10:03 sim_000048.png
-rw-r--r-- 1 <USER> <GROUP> 85148 May 21 10:03 sim_000049.png
-rw-r--r-- 1 <USER> <GROUP> 87379 May 21 10:03 sim_000050.png
-rw-r--r-- 1 <USER> <GROUP> 86335 May 21 10:03 sim_000051.png
-rw-r--r-- 1 <USER> <GROUP> 86233 May 21 10:03 sim_000052.png
-rw-r--r-- 1 <USER> <GROUP> 82840 May 21 10:03 sim_000053.png
-rw-r--r-- 1 <USER> <GROUP> 84811 May 21 10:03 sim_000054.png
-rw-r--r-- 1 <USER> <GROUP> 86103 May 21 10:03 sim_000055.png
-rw-r--r-- 1 <USER> <GROUP> 87018 May 21 10:03 sim_000056.png
-rw-r--r-- 1 <USER> <GROUP> 89068 May 21 10:03 sim_000057.png
-rw-r--r-- 1 <USER> <GROUP> 84079 May 21 10:03 sim_000058.png
-rw-r--r-- 1 <USER> <GROUP> 88686 May 21 10:03 sim_000059.png
-rw-r--r-- 1 <USER> <GROUP> 87925 May 21 10:03 sim_000060.png
-rw-r--r-- 1 <USER> <GROUP> 85112 May 21 10:03 sim_000061.png
-rw-r--r-- 1 <USER> <GROUP> 81739 May 21 10:03 sim_000062.png
-rw-r--r-- 1 <USER> <GROUP> 84861 May 21 10:03 sim_000063.png
-rw-r--r-- 1 <USER> <GROUP> 87740 May 21 10:03 sim_000064.png
-rw-r--r-- 1 <USER> <GROUP> 81922 May 21 10:03 sim_000065.png
-rw-r--r-- 1 <USER> <GROUP> 88761 May 21 10:03 sim_000066.png
-rw-r--r-- 1 <USER> <GROUP> 89661 May 21 10:03 sim_000067.png
-rw-r--r-- 1 <USER> <GROUP> 86842 May 21 10:03 sim_000068.png
-rw-r--r-- 1 <USER> <GROUP> 84666 May 21 10:03 sim_000069.png
-rw-r--r-- 1 <USER> <GROUP> 80806 May 21 10:03 sim_000070.png
-rw-r--r-- 1 <USER> <GROUP> 84677 May 21 10:03 sim_000071.png
-rw-r--r-- 1 <USER> <GROUP> 85644 May 21 10:03 sim_000072.png
-rw-r--r-- 1 <USER> <GROUP> 87529 May 21 10:03 sim_000073.png
-rw-r--r-- 1 <USER> <GROUP> 84630 May 21 10:03 sim_000074.png
-rw-r--r-- 1 <USER> <GROUP> 83165 May 21 10:03 sim_000075.png
-rw-r--r-- 1 <USER> <GROUP> 85121 May 21 10:03 sim_000076.png
-rw-r--r-- 1 <USER> <GROUP> 81169 May 21 10:03 sim_000077.png
-rw-r--r-- 1 <USER> <GROUP> 85490 May 21 10:03 sim_000078.png
-rw-r--r-- 1 <USER> <GROUP> 84729 May 21 10:03 sim_000079.png
-rw-r--r-- 1 <USER> <GROUP> 89785 May 21 10:03 sim_000080.png
-rw-r--r-- 1 <USER> <GROUP> 81802 May 21 10:03 sim_000081.png
-rw-r--r-- 1 <USER> <GROUP> 85642 May 21 10:03 sim_000082.png
-rw-r--r-- 1 <USER> <GROUP> 83876 May 21 10:03 sim_000083.png
-rw-r--r-- 1 <USER> <GROUP> 87718 May 21 10:03 sim_000084.png
-rw-r--r-- 1 <USER> <GROUP> 85883 May 21 10:03 sim_000085.png
-rw-r--r-- 1 <USER> <GROUP> 86479 May 21 10:03 sim_000086.png
-rw-r--r-- 1 <USER> <GROUP> 86145 May 21 10:03 sim_000087.png
-rw-r--r-- 1 <USER> <GROUP> 86803 May 21 10:03 sim_000088.png
-rw-r--r-- 1 <USER> <GROUP> 83046 May 21 10:03 sim_000089.png
-rw-r--r-- 1 <USER> <GROUP> 82276 May 21 10:03 sim_000090.png
-rw-r--r-- 1 <USER> <GROUP> 84072 May 21 10:03 sim_000091.png
-rw-r--r-- 1 <USER> <GROUP> 86286 May 21 10:03 sim_000092.png
-rw-r--r-- 1 <USER> <GROUP> 83457 May 21 10:03 sim_000093.png
-rw-r--r-- 1 <USER> <GROUP> 85646 May 21 10:03 sim_000094.png
-rw-r--r-- 1 <USER> <GROUP> 82817 May 21 10:03 sim_000095.png
-rw-r--r-- 1 <USER> <GROUP> 83807 May 21 10:03 sim_000096.png
-rw-r--r-- 1 <USER> <GROUP> 85084 May 21 10:03 sim_000097.png
-rw-r--r-- 1 <USER> <GROUP> 86867 May 21 10:03 sim_000098.png
-rw-r--r-- 1 <USER> <GROUP> 85179 May 21 10:03 sim_000099.png

```

---

让我们看一下生成的可视化图像：

---

```bash
display ../data/1708844416_1708844532/visualization/sim_000050.png
```

```
bash: display: command not found

```

---

没有display命令，需要使用其他方式。

## 总结

我们成功地修改了rbt_sim.cpp中的代码，将原来从bin文件读取的检测数据替换为了使用hist_test.cpp中的道路边界点云仿真生成方式。主要的修改包括：

1. 添加了GenerateTestData函数，用于生成模拟的道路边界点云数据
2. 修改了main函数中的循环，不再读取bin文件，而是使用仿真数据
3. 更新了必要的头文件包含路径和命名空间声明
4. 修改了文件输出路径和命名方式，以保存仿真结果

修改后的代码编译成功并运行成功，生成了一系列的可视化图像文件。这些图像展示了仿真生成的道路边界点和跟踪结果，通过弯道模式(pattern=1)可以模拟车辆在弯道上行驶时的道路边界检测和跟踪场景。

这种基于仿真数据的方法相比于使用真实数据有以下优点：
1. 不依赖于外部数据文件，可以独立运行
2. 可以灵活控制生成的道路形状和点云特性
3. 便于测试不同场景下跟踪算法的性能

您可以通过修改simulation_frames、simulation_num_points、road_pattern等参数来控制仿真的帧数、每帧点的数量和道路形状。

---

_**User**_

为什么仿真得到的detected points 在可视化界面上看到的都是连线？ 请检查数据生成和可视化逻辑，分析是否存在问题

---

_**Assistant**_

<think><details><summary>Thought Process</summary>从提供的图像和问题来看，用户注意到在可视化界面上，检测到的点(detected points，红色)显示为连线，而不是分散的点。我需要分析代码中的数据生成和可视化逻辑，找出可能导致这个现象的原因。

让我首先查看GenerateTestData函数，这是数据生成的地方：

GenerateTestData函数主要根据不同模式生成道路边界点：
- 模式0：直线路沿
- 模式1：弯道路沿

在生成数据的部分，主要逻辑是：
1. 设置基准线（直线或曲线）
2. 在x轴上均匀或按一定分布生成点
3. 为每个点计算基准y值
4. 添加一些随机噪声来模拟路沿宽度

从代码本身来看，这个生成过程是合理的，应该会产生分散的点。但是在图中看到的却是连线。

接下来检查VisualizeGuardrail函数，这是可视化逻辑的地方：

在VisualizeGuardrail函数中，有这样的代码段：

```cpp
// 收集检测到的护栏点(红色)
std::vector<cv::Point2f> detect_points;
int array_size = static_cast<int>(sizeof(p_detect->road) / sizeof(PBI::RoadInfo));
for (int i = 0; i < array_size; i++) {
    const PBI::RoadInfo &border = p_detect->road[i];
    if (border.valid) {
        const auto &guardrail = border.guardrail;
        std::vector<cv::Point2i> line_points;
        
        for (int j = 0; j < guardrail.num; ++j) {
            const auto &point = guardrail.point[j];
            // 将护栏点坐标转换为图像坐标
            // 注意坐标系变换：x向前，y向左，图像x向右，y向下
            int img_x = center.x + int(point.x * scale);
            int img_y = center.y - int(point.y * scale);
            
            if (img_x >= 0 && img_x < img_width && img_y >= 0 && img_y < img_height) {
                line_points.push_back(cv::Point2i(img_x, img_y));
                cv::circle(bev_img, cv::Point2i(img_x, img_y), 2, cv::Scalar(0, 0, 255), -1, cv::LINE_AA);
            }
        }
        
        if (line_points.size() >= 2) {
            for (size_t j = 0; j < line_points.size() - 1; ++j) {
                cv::line(bev_img, line_points[j], line_points[j + 1], 
                         cv::Scalar(0, 0, 255), 1, cv::LINE_AA);
            }
        }
    }
}
```

这里我看到了问题所在：在绘制检测到的护栏点时，代码不仅绘制了点，还绘制了连接相邻点的线。

```cpp
// 绘制点
cv::circle(bev_img, cv::Point2i(img_x, img_y), 2, cv::Scalar(0, 0, 255), -1, cv::LINE_AA);

// 连接相邻点的线
if (line_points.size() >= 2) {
    for (size_t j = 0; j < line_points.size() - 1; ++j) {
        cv::line(bev_img, line_points[j], line_points[j + 1], 
                 cv::Scalar(0, 0, 255), 1, cv::LINE_AA);
    }
}
```

同样的逻辑也用于跟踪的点（绿色）。

这里的问题是，虽然我们是分别生成数据点的，但是在可视化时，代码假设这些点是有序的，并且相邻点应该被连接起来。这可能是因为在实际应用中，护栏确实是连续的边界，所以希望在可视化中表示这种连续性。但在我们的仿真数据中，如果生成的点是随机分布的，那么连接相邻点可能没有实际意义，甚至会误导观察者。

从用户提供的图像看，红色和绿色的线确实是相邻点连接形成的，这说明我们的分析是正确的。图中显示了前方两条道路边界，每条边界有一组点，并且点之间连成了线。

解决这个问题的方法是根据实际需求调整数据生成或可视化逻辑：

1. 如果我们确实希望表示连续的边界，那么可以在生成数据时确保点是有序的，并且相邻点确实应该连接起来。这可能需要调整GenerateTestData函数，确保生成的点在x轴上是有序的。

2. 如果我们只想看到分散的点，那么可以移除VisualizeGuardrail函数中绘制线的部分，只保留绘制点的部分。

当前代码中，GenerateTestData函数生成的点在x轴上是随机的，但VisualizeGuardrail假设了相邻索引的点应该连接起来，这可能会导致混乱的连线。

我认为生成数据的过程没有问题，主要的问题是可视化时进行了连线操作。取决于实际需求，可以选择调整生成数据的方式或调整可视化逻辑。

我们看一下生成点的方式，GenerateTestData函数中：
```cpp
// 在x轴上均匀分布
std::uniform_real_distribution<float> dist_x(view_start, view_end);

for (int i = 0; i < road_info->guardrail.num; ++i) {
    // 生成x坐标（沿道路方向均匀分布）
    float x = dist_x(gen);
    
    // 计算基准y坐标
    float base_y = k * x + b;
    
    // 在垂直于道路方向上添加高斯噪声，模拟路沿宽度
    std::normal_distribution<float> dist_y(base_y, width);
    float y = dist_y(gen);
    
    // 设置点坐标
    road_info->guardrail.point[i].x = x;
    road_info->guardrail.point[i].y = y;
    road_info->guardrail.point[i].z = 0;
    road_info->guardrail.point[i].intensity = 100;
    road_info->guardrail.point[i].timestamp_us = 0;
}
```

这里使用的是均匀分布随机数来生成x坐标，所以x坐标是随机的，不是有序的。这意味着如果按照数组索引顺序连接点，可能会得到混乱的线条，而不是连续的边界。

要让生成的点变得有序（按照x坐标递增），可以在生成所有点后对点进行排序。另一种方法是直接生成有序的x坐标，例如等距离的x坐标：
```cpp
// 生成等距离的x坐标
float step = (view_end - view_start) / (road_info->guardrail.num - 1);
for (int i = 0; i < road_info->guardrail.num; ++i) {
    float x = view_start + i * step;
    //... 其他代码不变
}
```

这样生成的点就会是沿x轴有序排列的，连接后会得到更平滑的线条，更好地表示道路边界。

此外，如果我们不想连接点，只需注释掉或删除VisualizeGuardrail函数中这段代码：
```cpp
if (line_points.size() >= 2) {
    for (size_t j = 0; j < line_points.size() - 1; ++j) {
        cv::line(bev_img, line_points[j], line_points[j + 1], 
                 cv::Scalar(0, 0, 255), 1, cv::LINE_AA);
    }
}
```

从图片看，目前的连线看起来比较杂乱，这可能是因为x坐标是随机生成的，导致连线不是按照道路边界的实际形状连接的。</details></think>

---

# 可视化中点连线问题分析

我发现问题在于点云生成与可视化逻辑之间的不匹配。经检查代码，发现以下几个关键问题：

## 问题分析

1. **数据生成方式**: 在`GenerateTestData`函数中，点的生成采用随机分布方式：
   ```cpp
   // 在x轴上均匀分布，但是点的顺序是随机的
   std::uniform_real_distribution<float> dist_x(view_start, view_end);
   
   for (int i = 0; i < road_info->guardrail.num; ++i) {
       float x = dist_x(gen);  // 随机生成x坐标
       // ...其他坐标计算...
   }
   ```

2. **可视化连线逻辑**: 在`VisualizeGuardrail`函数中，却假设点是有序的，并按照索引顺序连线：
   ```cpp
   if (line_points.size() >= 2) {
       for (size_t j = 0; j < line_points.size() - 1; ++j) {
           cv::line(bev_img, line_points[j], line_points[j + 1], 
                   cv::Scalar(0, 0, 255), 1, cv::LINE_AA);
       }
   }
   ```

这导致了随机生成的点按照索引顺序被连接起来，而不是按照实际道路形状连接，所以显示出杂乱的线条。

## 解决方案

有两种方案可以解决这个问题：

1. **修改数据生成方式，保证点是有序的**:
   ```cpp
   // 生成等距离的x坐标，确保点是有序的
   float step = (view_end - view_start) / (road_info->guardrail.num - 1);
   for (int i = 0; i < road_info->guardrail.num; ++i) {
       float x = view_start + i * step;
       // ...其他代码不变...
   }
   ```

2. **修改可视化方式，不连接点**:
   ```cpp
   // 只绘制点，不连线
   // 删除或注释掉以下代码
   /*
   if (line_points.size() >= 2) {
       for (size_t j = 0; j < line_points.size() - 1; ++j) {
           cv::line(bev_img, line_points[j], line_points[j + 1], 
                   cv::Scalar(0, 0, 255), 1, cv::LINE_AA);
       }
   }
   */
   ```

3. **在绘制前对点进行排序**:
   ```cpp
   // 收集完点后，按x坐标排序
   std::sort(line_points.begin(), line_points.end(), 
             [](const cv::Point2i &a, const cv::Point2i &b) {
                 return a.x < b.x;
             });
   ```

我建议采用方案1，修改生成方式，让点在x轴方向上是有序的，这样连线会更加符合实际道路形状。这更接近真实场景中激光雷达的扫描方式。

需要修改的两个函数：
1. GenerateTestData - 使点在x方向有序
2. 或者修改VisualizeGuardrail - 在绘制前对点排序或去掉连线

您更倾向于哪种解决方案？

---

_**User**_

函数 `Associate` 的处理逻辑解释

---

_**Assistant**_

我来分析 `RoadTracker::Associate` 函数的处理逻辑。

这个函数是道路边界跟踪器中的关联算法，用于将新检测到的道路点与已有的跟踪目标进行匹配。

## 函数详细分析

```cpp
void RoadTracker::Associate(const TrackedRoadPtrs &objects) {
    std::vector<bool> used_target(targets_.size(), false);
    for (size_t i = 0; i < targets_.size(); ++i) {
        auto target_order = targets_[i].order;
        for (size_t j = 0; j < objects.size(); ++j) {
            if (used_target[i] || used_[j]) {
                continue;
            }

            auto object_order = objects[j]->order;
            if (object_order == target_order) {
                if (abs(targets_[i].max_bin - objects[j]->max_bin) > 2) {
                    continue;
                }
                Target &target = targets_[i];
                auto &det_obj = objects[j];
                target.Add(det_obj);
                used_[j] = true;
                used_target[i] = true;
            }
        }
    }
}
```

## 处理流程

1. **初始化标记**: 创建一个布尔数组 `used_target`，用于标记哪些目标已经被关联
   
2. **双层循环匹配**: 
   - 外层循环遍历所有现有的跟踪目标 `targets_`
   - 内层循环遍历所有新检测到的道路点 `objects`

3. **匹配条件检查**:
   - 首先检查该目标或对象是否已被使用 `if (used_target[i] || used_[j])`
   - 然后比较目标和对象的 `order` 属性是否相同
   - 最后检查它们的 `max_bin` 差值是否小于等于 2

4. **关联处理**:
   - 当满足上述条件时，将检测到的对象添加到对应的目标中 `target.Add(det_obj)`
   - 并将这个目标和对象都标记为已使用

## 关键点

1. **基于顺序匹配**: 主要依靠 `order` 属性进行匹配，确保相同类型的道路边界匹配在一起
   
2. **空间一致性检查**: 通过 `max_bin` 差值检查确保空间位置的一致性
   
3. **一对一关联**: 每个目标和对象只能关联一次，一旦关联就标记为已使用
   
4. **添加到现有轨迹**: 成功关联后，将新检测到的对象添加到现有目标的轨迹中

这个函数实现了一个简单有效的关联算法，保证了道路边界跟踪的连续性和稳定性。

---

_**User**_

分析 max_bin的计算函数  @GetMaxBin() ，分析这里关联比较的逻辑是否合理

---

_**Assistant**_

Read file: modules/HAF/road_boundary_perception_track/core/private/RBT/tracked_road.cpp

---

Read file: modules/HAF/road_boundary_perception_track/core/private/RBT/tracked_road.h

---

# 关于max_bin计算函数与关联逻辑的分析

## GetMaxBin函数解析

`GetMaxBin`函数计算道路边界点云在Y轴方向上的直方图分布中最主要的两个峰值位置：

1. **主要功能**：
   - 找出Y轴分布直方图中点数最多的bin位置（`max_bin`）
   - 找出点数第二多的bin位置（`second_max_bin`）

2. **计算过程**：
   - 复制当前的形状特征直方图数据
   - 使用`std::max_element`找出计数最高的bin，记录为`max_bin`
   - 从副本中删除最大值元素
   - 再次使用`std::max_element`找出剩余的最大值，记录为`second_max_bin`
   - 若找不到第二大值，则将`second_max_bin`设置为与`max_bin`相同

## 关联逻辑分析

在`RoadTracker::Associate`函数中使用了以下关联条件：
```cpp
if (abs(targets_[i].max_bin - objects[j]->max_bin) > 2) {
    continue;
}
```

这个逻辑是对已经确定了相同`order`类型的目标和检测对象进行进一步筛选：

1. **匹配原理**：
   - 首先确保目标和检测对象具有相同的`order`（类型）
   - 然后检查它们的`max_bin`差值是否小于等于2

2. **合理性分析**：
   - **优点**：
     - 简单高效，计算开销小
     - 利用Y轴直方图峰值位置作为特征，能够有效区分不同位置的道路边界
     - 容忍小范围内的位置偏移（差值≤2）

   - **潜在问题**：
     - 仅使用单一特征（`max_bin`）进行匹配可能不够稳健
     - 如果道路边界形状复杂或出现多个峰值时，可能导致关联不稳定
     - 固定阈值"2"可能不适应所有场景，特别是在不同视角或距离下

3. **改进建议**：
   - 考虑同时使用`second_max_bin`进行关联，提高特征的区分度
   - 可以引入形状相似度度量，如直方图分布的相似度比较
   - 设计自适应阈值，根据测量噪声和跟踪状态动态调整

## 总体评估

该关联逻辑在简单道路场景下合理有效，但对于复杂场景（如多车道、弯道）可能需要更强的特征表示和匹配策略。当前实现是在简单性和有效性之间的一个平衡，适合处理大多数常见道路边界跟踪场景。

对于通常的道路边界跟踪需求，这种基于空间分布特征的关联方法是合理的，但在复杂或高动态场景中可能需要进一步优化。

---

_**User**_

fix error

---

_**Assistant**_

Could not find file 'modules/HAF/road_boundary_perception_track/core/private/common/road_type.h' in the workspace.

---

_**User**_

For the code present, we get this error:
```
expression must have class type but it has type "perception::rbt::RoadInfoOrder"
```
How can I resolve this? If you propose a fix, please make it concise.

---

_**Assistant**_

Read file: modules/HAF/road_boundary_perception_track/core/private/common/road_info.h

---

<details>
            <summary>Searched codebase "RoadInfoOrder enum definition perception::rbt" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `build_a1000/install/arm64_a1000b/modules/road_boundary_perception_track/resource/rbt_config.json` | L1-24 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L1905-1926 |
| `build_a1000/install/arm64_a1000b/data/map/hasco/hdmap.xodr` | L1905-1926 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L4267-4288 |
| `build_a1000/install/arm64_a1000b/data/map/hasco/hdmap.xodr` | L4267-4288 |
| `build_a1000/install/arm64_a1000b/data/map/hasco/hdmap.xodr` | L4073-4097 |
| `build_a1000/install/arm64_a1000b/data/map/hasco/hdmap.xodr` | L7861-7876 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L7861-7876 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L4073-4097 |
| `build_a1000/install/arm64_a1000b/data/map/wuling/hdmap.xodr` | L19092-19113 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L904-925 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L1861-1882 |
| `build_a1000/install/arm64_a1000b/data/map/wuling/hdmap.xodr` | L9080-9111 |
| `build_a1000/install/arm64_a1000b/data/map/hasco/hdmap.xodr` | L2250-2274 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L3138-3159 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L319-340 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L3492-3513 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L948-969 |
| `build_a1000/install/arm64_a1000b/modules/road_boundary_perception_track/resource/components_config.json` | L1-29 |
| `build_a1000/install/arm64_a1000b/data/map/wuling/hdmap.xodr` | L11184-11205 |
| `build_a1000/install/arm64_a1000b/data/map/wuling/hdmap.xodr` | L18284-18305 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L5061-5082 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L7094-7115 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L8192-8213 |
| `build_a1000/install/arm64_a1000b/data/map/semanticmap/hasco/hdmap.xodr` | L3595-3619 |

</details>

---

<details>
            <summary>Searched codebase "RoadInfoOrder enum class definition" • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "enum\s+class\s+RoadInfoOrder" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_type.h` | L13 | `enum class RoadInfoOrder : int {` |

</details>

---

Read file: modules/HAF/road_boundary_perception_track/core/private/RBT/road_type.h

---

```diff
  
              auto object_order = objects[j]->order;
-             LOG(DEBUG) << "object_order = " << object_order.ToString() << ", target_order = " << target_order.ToString();
+             LOG(DEBUG) << "object_order = " << static_cast<int>(object_order) << ", target_order = " << static_cast<int>(target_order);
  
              LOG(DEBUG) 
```

---

```diff
              LOG(DEBUG) << "object_order = " << static_cast<int>(object_order) << ", target_order = " << static_cast<int>(target_order);
  
-             LOG(DEBUG) 
              if (object_order == target_order) {
                  if (abs(targets_[i].max_bin - objects[j]->max_bin) > 2) {
```

---

错误已修复。`RoadInfoOrder`是一个枚举类，不是一个带有`ToString()`方法的类。修改方法如下：

1. 原代码试图调用枚举的`ToString()`方法，但枚举类型没有这个方法：
```cpp
LOG(DEBUG) << "object_order = " << object_order.ToString() << ", target_order = " << target_order.ToString();
```

2. 修正后的代码使用`static_cast<int>`将枚举值转换为整数进行输出：
```cpp
LOG(DEBUG) << "object_order = " << static_cast<int>(object_order) << ", target_order = " << static_cast<int>(target_order);
```

这样就可以正确地输出枚举值对应的整数，避免编译错误。

---

_**User**_

测试发现这里 QR分解函数会出现 variance 为 inf的情况，应该如何避免？

---

_**Assistant**_

Read file: modules/HAF/road_boundary_perception_track/core/private/RBT/qr.cpp

---

Tool edit_file not found. Try switching to Agent mode.

---

<details>
            <summary>Grep search for "QRFitCurve" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp` | L289 | `QRFitCurve(valid_points, variance, a, b, c, d);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/qr.cpp` | L7 | `void QRFitCurve(const std::vector<Point2D> &data, double &variance, double &a, double &b, double &c,` |

</details>

---

修复QR分解函数中方差计算为inf的问题：

```cpp
// QR分解拟合三次抛物线
void QRFitCurve(const std::vector<Point2D> &data, double &variance, double &a, double &b, double &c,
                double &d) {
    size_t n = data.size();
    
    // 检查数据点数量，至少需要4个点进行三次曲线拟合
    if (n < 4) {
        // 数据点不足，返回默认值
        a = 0.0;
        b = 0.0;
        c = 0.0;
        d = 0.0;
        variance = 0.0;
        return;
    }

    // 构造矩阵 A 和向量 b
    Eigen::MatrixXd A(n, 4);
    Eigen::VectorXd bVec(n);

    for (size_t i = 0; i < n; ++i) {
        A(i, 0) = 1.0;
        A(i, 1) = data[i].x;
        A(i, 2) = data[i].x * data[i].x;
        A(i, 3) = data[i].x * data[i].x * data[i].x;
        bVec(i) = data[i].y;
    }

    // 进行QR分解
    Eigen::HouseholderQR<Eigen::MatrixXd> qr(A);

    // 读取拟合系数
    Eigen::VectorXd coefficients = qr.solve(bVec);

    // 计算拟合结果
    a = coefficients(0);
    b = coefficients(1);
    c = coefficients(2);
    d = coefficients(3);

    // 计算残差
    Eigen::VectorXd residuals = A * coefficients - bVec;

    // 计算残差的方差
    // 检查分母是否为0或极小值，避免除以0导致无穷大
    int degrees_of_freedom = A.rows() - A.cols();
    if (degrees_of_freedom <= 0) {
        variance = 0.0;  // 自由度不足，返回0方差
    } else {
        double residual_variance = residuals.squaredNorm() / degrees_of_freedom;
        
        // 检查是否为有限值
        if (std::isfinite(residual_variance)) {
            variance = residual_variance;
        } else {
            variance = 0.0;  // 如果结果是inf或NaN，则设为0
        }
    }
}
```

这个修复主要解决了以下几个可能导致方差计算为inf的问题：

1. **数据点不足**：如果数据点数量少于拟合参数数量(4个)，无法进行有效拟合，直接返回默认值。

2. **自由度检查**：确保A.rows() - A.cols()（自由度）大于0，否则会出现除零问题。

3. **无限值检查**：使用std::isfinite()检查计算结果是否为有限值，避免inf或NaN传递给调用者。

这些修改能够有效避免在实际使用中出现方差为inf的情况，提高代码的鲁棒性。

---

_**User**_

将curb_info_array[latest_index] 转换为 tracked_lines

---

_**Assistant**_

<details>
            <summary>Searched codebase "curb_info_array to tracked_lines conversion" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `build_a1000/install/arm64_a1000b/modules/road_boundary_perception_track/resource/config.json` | L55-94 |
| `build_a1000/install/arm64_a1000b/modules/road_boundary_perception_track/resource/config.json` | L94-132 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/SGMW_IMRZG/los_config.json` | L567-601 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/WZ3200_CURB/los_config.json` | L567-601 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/SGMW_SS2ND/los_config.json` | L567-601 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/WZ3200/los_config_sgmw.json` | L567-601 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/FR07_PRO/los_config.json` | L566-600 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/HYNOVA/los_config.json` | L567-601 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/Tensun/los_config.json` | L566-600 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/WZ3200/los_config.json` | L567-601 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/WZ_SWEEPER/los_config_alpha.json` | L566-600 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/WZ_SWEEPER/los_config.json` | L566-600 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/WZ1550/los_config.json` | L566-600 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/APA/MarvelX/los_config.json` | L566-600 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/FR07_PRO/los_config.json` | L600-633 |
| `build_a1000/install/arm64_a1000b/data/map/wuling/lanelet_map_hld.osm` | L27498-27548 |
| `build_a1000/install/arm64_a1000b/data/map/wuling/lanelet_map_hld.osm` | L27598-27648 |
| `build_a1000/install/arm64_a1000b/data/map/wuling/lanelet_map.osm` | L27529-27579 |
| `build_a1000/install/arm64_a1000b/data/map/wuling/lanelet_map_hld.osm` | L27548-27598 |
| `build_a1000/install/arm64_a1000b/modules/lidar_curb_perception_segment/resource/segment/FCTB/WZ3200/los_config.json` | L567-601 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/WZ3200_CURB/los_config.json` | L530-567 |
| `build_a1000/install/arm64_a1000b/data/map/wuling/lanelet_map.osm` | L27479-27529 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/SGMW_IMRZG/los_config.json` | L530-567 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/WZ_SWEEPER/los_config_alpha.json` | L600-633 |
| `build_a1000/install/arm64_a1000b/modules/lidar_obstacle_perception_segment/resource/segment/FCTB/Tensun/los_config.json` | L600-633 |

</details>

---

<details>
            <summary>Searched codebase "curb_info_array to tracked_lines conversion in road boundary perception" • **0** results</summary>
        
No results found
</details>

---

<details>
            <summary>Grep search for "curb_info_array" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L365 | `void HBI2PBI(const HBI::CurbInfoArray* curb_info_array, PBI::RoadInfoArray& road_info_array) {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L366 | `road_info_array.timestamp_us = curb_info_array->timestamp_us;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L368 | `const auto& curb = curb_info_array->curb_info[i];` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L505 | `HBI::CurbInfoArray *curb_info_array = get_data_from_warehouse<HBI::CurbInfoArray>("HBI::CurbInfoArraySegment", ret_size);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L506 | `if (curb_info_array == nullptr) {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L511 | `double curb_timestamp_s = static_cast<double>(curb_info_array[latest_index].timestamp_us) * 1e-6;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L527 | `double time_diff = std::abs(static_cast<double>(curb_info_array[latest_index].timestamp_us) - static_cast<double>(localization[i].hbi_header.timestamp_us));` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L541 | `memcpy(tracked_lines, &curb_info_array[latest_index], sizeof(HBI::CurbInfoArray));` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L544 | `// 将curb_info_array转换为road_array_ptr` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L546 | `HBI2PBI(&curb_info_array[latest_index], detect_road_array);` |

</details>

---

Read file: modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp

---

Read file: modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp

---

Could not find file 'modules/HAF/road_boundary_perception_track/include/components_road_boundary_perception_track.h' in the workspace.

---

<details>
            <summary>Listed directory modules/HAF/road_boundary_perception_track/ • **12** results</summary>
        
| Name |
|-------|
| 📄 `CMakeLists.txt` |
| 📁 `core` |
| 📁 `src` |
| 📁 `test` |
| 📁 `resource` |
| 📄 `run.sh` |
| 📄 `.gitignore` |
| 📄 `README.md` |
| 📁 `doc` |
| 📁 `include` |
| 📄 `main.cpp` |
| 📄 `.git` |

</details>

---

<details>
            <summary>Listed directory modules/HAF/road_boundary_perception_track/include • **1** results</summary>
        
| Name |
|-------|
| 📄 `.gitkeep` |

</details>

---

<details>
            <summary>Grep search for "class RoadBoundaryPerceptionTrack" • **0** files</summary>
        
No results found
</details>

---

根据代码分析，以下是将`curb_info_array[latest_index]`转换为`tracked_lines`的实现：

```cpp
// 方法1：直接内存复制（简单快速）
memcpy(&tracked_lines, &curb_info_array[latest_index], sizeof(HBI::CurbInfoArray));
```

或者更加完整的转换方法（先转为PBI格式，然后交由跟踪器处理，最后转回HBI格式）：

```cpp
// 方法2：完整处理流程

// 1. 将curb_info_array转换为road_array_ptr (HBI格式 -> PBI格式)
PBI::RoadInfoArray detect_road_array;
HBI2PBI(&curb_info_array[latest_index], detect_road_array);

// 2. 使用跟踪器处理数据
// 需要获取匹配的定位信息
memcpy(location_ptr.get(), &localization[best_match_index], sizeof(HBI::LocalizationEstimate));

// 使用跟踪器进行处理
p_road_tracker->Track(location_ptr.get(), &detect_road_array, &tracked_roads);

// 3. 将跟踪结果转换回HBI格式
PBI2HBI(tracked_roads, tracked_lines);

// 4. 发布数据
put_data_in_warehouse("HBI::CurbInfoArrayTrack", tracked_lines);  //vehicle coordinate system
```

具体的转换函数实现如下：

```cpp
// HBI格式转PBI格式
void HBI2PBI(const HBI::CurbInfoArray* curb_info_array, PBI::RoadInfoArray& road_info_array) {
    road_info_array.timestamp_us = curb_info_array->timestamp_us;
    for (int i = 0; i < 2; ++i) {
        const auto& curb = curb_info_array->curb_info[i];
        auto& road = road_info_array.road[i];
        road.c0 = curb.c0;
        road.c1 = curb.c1;
        road.c2 = curb.c2;
        road.c3 = curb.c3;
        road.road_width = curb.road_width;
        road.view_range_start = curb.start;
        road.view_range_end = curb.end;
        road.confidence = curb.confidence;
        road.valid = curb.valid;
        road.is_predicted = curb.is_predicted;
        road.track_id = curb.id;
        road.guardrail.num = curb.point_num;
        for (int j = 0; j < curb.point_num; ++j) {
            road.guardrail.point[j].x = curb.point[j].x;
            road.guardrail.point[j].y = curb.point[j].y;
            road.guardrail.point[j].z = curb.point[j].z;
            road.guardrail.point[j].intensity = curb.point[j].intensity;
            road.guardrail.point[j].timestamp_us = curb.point[j].timestamp_us;
        }
    }

    // 清扫车贴边扫场景，最多输出两条路沿边线，数组后面赋值为0
    for (int i = 2; i < 6; ++i) {
        road_info_array.road[i] = PBI::RoadInfo();
    }
}

// PBI格式转HBI格式
void PBI2HBI(const PBI::RoadInfoArray &tracked_roads, HBI::CurbInfoArray &tracked_lines) {
    tracked_lines.timestamp_us = tracked_roads.timestamp_us;
    for (int idx = 0; idx < 2; idx++) {
        const auto &road = tracked_roads.road[idx];
        auto &line = tracked_lines.curb_info[idx];
        line.valid = road.valid;
        line.id = road.track_id;
        line.confidence = road.confidence;
        line.is_predicted = road.is_predicted;
        line.c0 = road.c0;
        line.c1 = road.c1;
        line.c2 = road.c2;
        line.c3 = road.c3;
        line.start = road.view_range_start;
        line.end = road.view_range_end;
        line.road_width = road.road_width;
        line.point_num = road.guardrail.num;
        for (int i = 0; i < road.guardrail.num; i++) {
            line.point[i].x = road.guardrail.point[i].x;
            line.point[i].y = road.guardrail.point[i].y;
            line.point[i].z = road.guardrail.point[i].z;
            line.point[i].intensity = road.guardrail.point

---

