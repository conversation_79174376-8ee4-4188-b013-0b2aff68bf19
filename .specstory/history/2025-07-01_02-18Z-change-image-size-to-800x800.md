<!-- Generated by SpecStory -->

# Change image size to 800x800 (2025-07-01 02:18Z)

_**User**_

当前画图设定的尺寸是400*400，看起来太小了，改成800*800

---

_**Assistant**_

我看到您想要将当前的BEV图像尺寸从400×400改为800×800。根据代码分析，当前的图像尺寸是通过 `bev_x_size * pixels_per_meter` 和 `bev_y_size * pixels_per_meter` 计算得出的（20.0 × 20 = 400像素）。

要将图像尺寸改为800×800，您有两种方式：

**方式1：增加像素密度（推荐）**
```cpp:995-1002:modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp
    const float bev_x_size = 20.0f;
    const float bev_y_size = 20.0f;
    const int pixels_per_meter = 40;  // 从20改为40，保持20米范围但提高分辨率
    const int image_height = static_cast<int>(bev_x_size * pixels_per_meter);
    const int image_width  = static_cast<int>(bev_y_size * pixels_per_meter);
    const int origin_x = int(image_width / 2);
    const int origin_y = int(image_height / 2);
```

**方式2：扩大覆盖范围**
```cpp:995-1002:modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp
    const float bev_x_size = 40.0f;  // 从20.0f改为40.0f，扩大到40米范围
    const float bev_y_size = 40.0f;  // 从20.0f改为40.0f，扩大到40米范围
    const int pixels_per_meter = 20;
    const int image_height = static_cast<int>(bev_x_size * pixels_per_meter);
    const int image_width  = static_cast<int>(bev_y_size * pixels_per_meter);
    const int origin_x = int(image_width / 2);
    const int origin_y = int(image_height / 2);
```

**推荐使用方式1**，因为它保持了相同的物理覆盖范围（20米×20米），但提供了更高的图像分辨率，这样可以更清晰地显示路沿点的细节。如果您需要覆盖更大的区域，则可以选择方式2。

两种方式都会将最终的图像尺寸从400×400像素改为800×800像素。

---

