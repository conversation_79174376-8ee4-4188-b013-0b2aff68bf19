<!-- Generated by SpecStory -->

# 如何排查core dump问题 (2025-07-16 00:37Z)

_**User**_

执行程序报core dump 问题，如何排查？

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户遇到了core dump问题，需要排查。我看到从附加文件中，程序执行时发生了段错误：

```
test.sh: line 20: 24840 Segmentation fault      (core dumped) ./lcs_test /hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/test/V1.46_20250711_182132/input/ FCTB STARJ_CR3
```

这是一个C++程序的崩溃问题。用户要求用中文回答，我需要提供一个系统性的core dump排查方法。

根据用户规则，我需要：
1. 使用中文回答
2. 记住代码是写给人看的
3. 在处理复杂任务时参考行为规范

我应该提供一个全面的core dump排查指南，包括：
1. 启用core dump
2. 使用gdb调试
3. 检查代码逻辑
4. 检查输入数据
5. 添加日志等

让我先检查一下当前的工作环境和相关文件。</details></think>

---

我看到您的lcs_test程序发生了段错误(Segmentation fault)。让我帮您系统性地排查这个core dump问题。

## Core Dump排查步骤

### 1. 首先检查core dump是否已启用

---

```bash
ulimit -c
```

```
root@in-x86-dev-docker:/hfr# ulimit -c
unlimited
root@in-x86-dev-docker:/hfr#
```

---

<think><details><summary>Thought Process</summary>core dump已经启用了，现在让我检查是否生成了core文件，然后用gdb进行调试。</details></think>

---

好的，core dump已启用。现在让我检查是否生成了core文件：

---

```bash
find /hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/ -name "core*" -type f
```

```
root@in-x86-dev-docker:/hfr# 

```

---

<think><details><summary>Thought Process</summary>看起来没有找到core文件。可能core文件在其他位置，或者没有生成。让我检查一下build目录，因为程序是在那里执行的。</details></think>

---

```bash
find /hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/build/ -name "core*" -type f 2>/dev/null || echo "没有找到core文件"
```

```
root@in-x86-dev-docker:/hfr# 

```

---

<think><details><summary>Thought Process</summary>看起来没有core文件生成。让我提供一个完整的core dump排查方案，包括如何生成core文件和使用gdb调试。首先让我检查一下可执行文件的情况。</details></think>

---

没有找到core文件。让我为您提供完整的排查方案：

### 2. 检查可执行文件是否存在

---

```bash
ls -la /hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/build/lcs_test
```

```
root@in-x86-dev-docker:/hfr# ls -la /hfr/modules/HAF/lidar_curb_perception_segment/core/private
/lcs/build/lcs_test
-rwxr-xr-x 1 <USER> <GROUP> 1116312 Jul 16 08:46 /hfr/modules/HAF/lidar_curb_perception_segment/core
/private/lcs/build/lcs_test
root@in-x86-dev-docker:/hfr#
```

---

<think><details><summary>Thought Process</summary>可执行文件存在。现在让我提供一个全面的排查方案。我需要检查输入目录、使用gdb调试、分析代码等。</details></think>

---

### 3. 检查输入目录和数据完整性

---

```bash
ls -la /hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/test/V1.46_20250711_182132/input/
```

```
root@in-x86-dev-docker:/hfr# ls -la /hfr/modules/HAF/lidar_curb_perception_segment/core/private
/lcs/test/V1.46_20250711_182132/input/
total 506404
drwxr-xr-x 3 <USER> <GROUP>   36864 Jul 16 08:45 .
drwxr-xr-x 3 <USER> <GROUP>    4096 Jul 16 08:45 ..
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229304_218132.bin
-rw-r--r-- 1 <USER> <GROUP> 2206704 Jul 16 08:45 1752229304_218132.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229304_218132.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229304_517946.bin
-rw-r--r-- 1 <USER> <GROUP> 2192327 Jul 16 08:45 1752229304_517946.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229304_517946.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229305_018132.bin
-rw-r--r-- 1 <USER> <GROUP> 2186309 Jul 16 08:45 1752229305_018132.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229305_018132.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229305_318147.bin
-rw-r--r-- 1 <USER> <GROUP> 2233407 Jul 16 08:45 1752229305_318147.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229305_318147.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229305_818138.bin
-rw-r--r-- 1 <USER> <GROUP> 2225861 Jul 16 08:45 1752229305_818138.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229305_818138.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229306_118088.bin
-rw-r--r-- 1 <USER> <GROUP> 2230403 Jul 16 08:45 1752229306_118088.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229306_118088.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229306_517732.bin
-rw-r--r-- 1 <USER> <GROUP> 2229703 Jul 16 08:45 1752229306_517732.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229306_517732.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229306_718140.bin
-rw-r--r-- 1 <USER> <GROUP> 2189577 Jul 16 08:45 1752229306_718140.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229306_718140.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229307_118001.bin
-rw-r--r-- 1 <USER> <GROUP> 2224836 Jul 16 08:45 1752229307_118001.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229307_118001.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229307_417993.bin
-rw-r--r-- 1 <USER> <GROUP> 2221721 Jul 16 08:45 1752229307_417993.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229307_417993.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229307_918094.bin
-rw-r--r-- 1 <USER> <GROUP> 2192980 Jul 16 08:45 1752229307_918094.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229307_918094.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229308_317964.bin
-rw-r--r-- 1 <USER> <GROUP> 2144471 Jul 16 08:45 1752229308_317964.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229308_317964.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229308_618129.bin
-rw-r--r-- 1 <USER> <GROUP> 2195180 Jul 16 08:45 1752229308_618129.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229308_618129.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229309_218166.bin
-rw-r--r-- 1 <USER> <GROUP> 2159132 Jul 16 08:45 1752229309_218166.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229309_218166.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229309_518126.bin
-rw-r--r-- 1 <USER> <GROUP> 2204755 Jul 16 08:45 1752229309_518126.pcd
-rw-r--r-- 1 <USER> <GROUP>     228 Jul 16 08:45 1752229309_518126.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229310_118169.bin
-rw-r--r-- 1 <USER> <GROUP> 2142397 Jul 16 08:45 1752229310_118169.pcd
-rw-r--r-- 1 <USER> <GROUP>     228 Jul 16 08:45 1752229310_118169.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229310_418047.bin
-rw-r--r-- 1 <USER> <GROUP> 2077279 Jul 16 08:45 1752229310_418047.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229310_418047.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229310_618164.bin
-rw-r--r-- 1 <USER> <GROUP> 2046198 Jul 16 08:45 1752229310_618164.pcd
-rw-r--r-- 1 <USER> <GROUP>     229 Jul 16 08:45 1752229310_618164.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229310_916460.bin
-rw-r--r-- 1 <USER> <GROUP> 2086473 Jul 16 08:45 1752229310_916460.pcd
-rw-r--r-- 1 <USER> <GROUP>     233 Jul 16 08:45 1752229310_916460.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229311_118105.bin
-rw-r--r-- 1 <USER> <GROUP> 2108351 Jul 16 08:45 1752229311_118105.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229311_118105.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229311_418087.bin
-rw-r--r-- 1 <USER> <GROUP> 2082078 Jul 16 08:45 1752229311_418087.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229311_418087.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229311_818026.bin
-rw-r--r-- 1 <USER> <GROUP> 2139907 Jul 16 08:45 1752229311_818026.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229311_818026.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229312_018154.bin
-rw-r--r-- 1 <USER> <GROUP> 2163132 Jul 16 08:45 1752229312_018154.pcd
-rw-r--r-- 1 <USER> <GROUP>     229 Jul 16 08:45 1752229312_018154.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229312_218116.bin
-rw-r--r-- 1 <USER> <GROUP> 2162777 Jul 16 08:45 1752229312_218116.pcd
-rw-r--r-- 1 <USER> <GROUP>     228 Jul 16 08:45 1752229312_218116.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229312_618088.bin
-rw-r--r-- 1 <USER> <GROUP> 2147145 Jul 16 08:45 1752229312_618088.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229312_618088.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229312_817724.bin
-rw-r--r-- 1 <USER> <GROUP> 2144247 Jul 16 08:45 1752229312_817724.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229312_817724.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229313_218148.bin
-rw-r--r-- 1 <USER> <GROUP> 2125988 Jul 16 08:45 1752229313_218148.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229313_218148.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229313_417817.bin
-rw-r--r-- 1 <USER> <GROUP> 2114921 Jul 16 08:45 1752229313_417817.pcd
-rw-r--r-- 1 <USER> <GROUP>     229 Jul 16 08:45 1752229313_417817.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229313_618174.bin
-rw-r--r-- 1 <USER> <GROUP> 2124122 Jul 16 08:45 1752229313_618174.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229313_618174.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229313_918168.bin
-rw-r--r-- 1 <USER> <GROUP> 2109294 Jul 16 08:45 1752229313_918168.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229313_918168.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229314_218144.bin
-rw-r--r-- 1 <USER> <GROUP> 2152327 Jul 16 08:45 1752229314_218144.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229314_218144.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229314_718162.bin
-rw-r--r-- 1 <USER> <GROUP> 2133383 Jul 16 08:45 1752229314_718162.pcd
-rw-r--r-- 1 <USER> <GROUP>     219 Jul 16 08:45 1752229314_718162.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229315_318083.bin
-rw-r--r-- 1 <USER> <GROUP> 2080946 Jul 16 08:45 1752229315_318083.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229315_318083.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229315_718089.bin
-rw-r--r-- 1 <USER> <GROUP> 2013555 Jul 16 08:45 1752229315_718089.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229315_718089.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229316_118158.bin
-rw-r--r-- 1 <USER> <GROUP> 2026834 Jul 16 08:45 1752229316_118158.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229316_118158.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229316_418139.bin
-rw-r--r-- 1 <USER> <GROUP> 2061891 Jul 16 08:45 1752229316_418139.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229316_418139.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229316_618166.bin
-rw-r--r-- 1 <USER> <GROUP> 2081292 Jul 16 08:45 1752229316_618166.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229316_618166.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229316_918116.bin
-rw-r--r-- 1 <USER> <GROUP> 2053163 Jul 16 08:45 1752229316_918116.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229316_918116.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229317_318121.bin
-rw-r--r-- 1 <USER> <GROUP> 2042781 Jul 16 08:45 1752229317_318121.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229317_318121.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229317_718116.bin
-rw-r--r-- 1 <USER> <GROUP> 2090583 Jul 16 08:45 1752229317_718116.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229317_718116.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229317_918114.bin
-rw-r--r-- 1 <USER> <GROUP> 2063679 Jul 16 08:45 1752229317_918114.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229317_918114.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229318_418164.bin
-rw-r--r-- 1 <USER> <GROUP> 2084526 Jul 16 08:45 1752229318_418164.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229318_418164.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229318_718150.bin
-rw-r--r-- 1 <USER> <GROUP> 2041331 Jul 16 08:45 1752229318_718150.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229318_718150.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229319_218110.bin
-rw-r--r-- 1 <USER> <GROUP> 2069934 Jul 16 08:45 1752229319_218110.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229319_218110.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229319_518117.bin
-rw-r--r-- 1 <USER> <GROUP> 2053392 Jul 16 08:45 1752229319_518117.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229319_518117.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229319_718161.bin
-rw-r--r-- 1 <USER> <GROUP> 2018592 Jul 16 08:45 1752229319_718161.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229319_718161.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229320_018166.bin
-rw-r--r-- 1 <USER> <GROUP> 2044045 Jul 16 08:45 1752229320_018166.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229320_018166.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229320_318159.bin
-rw-r--r-- 1 <USER> <GROUP> 2052854 Jul 16 08:45 1752229320_318159.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229320_318159.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229320_718135.bin
-rw-r--r-- 1 <USER> <GROUP> 2065448 Jul 16 08:45 1752229320_718135.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229320_718135.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229321_017890.bin
-rw-r--r-- 1 <USER> <GROUP> 2045480 Jul 16 08:45 1752229321_017890.pcd
-rw-r--r-- 1 <USER> <GROUP>     228 Jul 16 08:45 1752229321_017890.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229321_218149.bin
-rw-r--r-- 1 <USER> <GROUP> 1976882 Jul 16 08:45 1752229321_218149.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229321_218149.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229321_718130.bin
-rw-r--r-- 1 <USER> <GROUP> 2031732 Jul 16 08:45 1752229321_718130.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229321_718130.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229322_118086.bin
-rw-r--r-- 1 <USER> <GROUP> 2002915 Jul 16 08:45 1752229322_118086.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229322_118086.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229322_518167.bin
-rw-r--r-- 1 <USER> <GROUP> 2013713 Jul 16 08:45 1752229322_518167.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229322_518167.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229322_918167.bin
-rw-r--r-- 1 <USER> <GROUP> 1994691 Jul 16 08:45 1752229322_918167.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229322_918167.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229323_418150.bin
-rw-r--r-- 1 <USER> <GROUP> 2022885 Jul 16 08:45 1752229323_418150.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229323_418150.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229323_618138.bin
-rw-r--r-- 1 <USER> <GROUP> 2039380 Jul 16 08:45 1752229323_618138.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229323_618138.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229323_818170.bin
-rw-r--r-- 1 <USER> <GROUP> 2018801 Jul 16 08:45 1752229323_818170.pcd
-rw-r--r-- 1 <USER> <GROUP>     228 Jul 16 08:45 1752229323_818170.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229324_218128.bin
-rw-r--r-- 1 <USER> <GROUP> 2000072 Jul 16 08:45 1752229324_218128.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229324_218128.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229324_618151.bin
-rw-r--r-- 1 <USER> <GROUP> 2023508 Jul 16 08:45 1752229324_618151.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229324_618151.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229325_018164.bin
-rw-r--r-- 1 <USER> <GROUP> 2067218 Jul 16 08:45 1752229325_018164.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229325_018164.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229325_318126.bin
-rw-r--r-- 1 <USER> <GROUP> 2032816 Jul 16 08:45 1752229325_318126.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229325_318126.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229325_718137.bin
-rw-r--r-- 1 <USER> <GROUP> 2045026 Jul 16 08:45 1752229325_718137.pcd
-rw-r--r-- 1 <USER> <GROUP>     219 Jul 16 08:45 1752229325_718137.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229325_918138.bin
-rw-r--r-- 1 <USER> <GROUP> 2059426 Jul 16 08:45 1752229325_918138.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229325_918138.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229326_118169.bin
-rw-r--r-- 1 <USER> <GROUP> 2052830 Jul 16 08:45 1752229326_118169.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229326_118169.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229326_618168.bin
-rw-r--r-- 1 <USER> <GROUP> 2034999 Jul 16 08:45 1752229326_618168.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229326_618168.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229327_118168.bin
-rw-r--r-- 1 <USER> <GROUP> 2049440 Jul 16 08:45 1752229327_118168.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229327_118168.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229327_418133.bin
-rw-r--r-- 1 <USER> <GROUP> 2042198 Jul 16 08:45 1752229327_418133.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229327_418133.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229327_618161.bin
-rw-r--r-- 1 <USER> <GROUP> 2038026 Jul 16 08:45 1752229327_618161.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229327_618161.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229328_018170.bin
-rw-r--r-- 1 <USER> <GROUP> 2036498 Jul 16 08:45 1752229328_018170.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229328_018170.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229328_218138.bin
-rw-r--r-- 1 <USER> <GROUP> 1974225 Jul 16 08:45 1752229328_218138.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229328_218138.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229328_418155.bin
-rw-r--r-- 1 <USER> <GROUP> 1946551 Jul 16 08:45 1752229328_418155.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229328_418155.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229329_218159.bin
-rw-r--r-- 1 <USER> <GROUP> 2041628 Jul 16 08:45 1752229329_218159.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229329_218159.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229329_618104.bin
-rw-r--r-- 1 <USER> <GROUP> 1952001 Jul 16 08:45 1752229329_618104.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229329_618104.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229330_118155.bin
-rw-r--r-- 1 <USER> <GROUP> 1969968 Jul 16 08:45 1752229330_118155.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229330_118155.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229330_518146.bin
-rw-r--r-- 1 <USER> <GROUP> 2042428 Jul 16 08:45 1752229330_518146.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229330_518146.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229331_018056.bin
-rw-r--r-- 1 <USER> <GROUP> 2048665 Jul 16 08:45 1752229331_018056.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229331_018056.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229331_217302.bin
-rw-r--r-- 1 <USER> <GROUP> 2038130 Jul 16 08:45 1752229331_217302.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229331_217302.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229331_418154.bin
-rw-r--r-- 1 <USER> <GROUP> 2024624 Jul 16 08:45 1752229331_418154.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229331_418154.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229331_718007.bin
-rw-r--r-- 1 <USER> <GROUP> 2036778 Jul 16 08:45 1752229331_718007.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229331_718007.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229331_918148.bin
-rw-r--r-- 1 <USER> <GROUP> 2029069 Jul 16 08:45 1752229331_918148.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229331_918148.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229332_218030.bin
-rw-r--r-- 1 <USER> <GROUP> 2034645 Jul 16 08:45 1752229332_218030.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229332_218030.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229332_418082.bin
-rw-r--r-- 1 <USER> <GROUP> 2047272 Jul 16 08:45 1752229332_418082.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229332_418082.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229332_617662.bin
-rw-r--r-- 1 <USER> <GROUP> 2053696 Jul 16 08:45 1752229332_617662.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229332_617662.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229332_817820.bin
-rw-r--r-- 1 <USER> <GROUP> 2069628 Jul 16 08:45 1752229332_817820.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229332_817820.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229333_018098.bin
-rw-r--r-- 1 <USER> <GROUP> 2048423 Jul 16 08:45 1752229333_018098.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229333_018098.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229333_418159.bin
-rw-r--r-- 1 <USER> <GROUP> 2075545 Jul 16 08:45 1752229333_418159.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229333_418159.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229333_918138.bin
-rw-r--r-- 1 <USER> <GROUP> 2130476 Jul 16 08:45 1752229333_918138.pcd
-rw-r--r-- 1 <USER> <GROUP>     229 Jul 16 08:45 1752229333_918138.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229334_217841.bin
-rw-r--r-- 1 <USER> <GROUP> 2119194 Jul 16 08:45 1752229334_217841.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229334_217841.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229334_418167.bin
-rw-r--r-- 1 <USER> <GROUP> 2094193 Jul 16 08:45 1752229334_418167.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229334_418167.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229334_717968.bin
-rw-r--r-- 1 <USER> <GROUP> 2070770 Jul 16 08:45 1752229334_717968.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229334_717968.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229335_018131.bin
-rw-r--r-- 1 <USER> <GROUP> 2055639 Jul 16 08:45 1752229335_018131.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229335_018131.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229335_218000.bin
-rw-r--r-- 1 <USER> <GROUP> 2063599 Jul 16 08:45 1752229335_218000.pcd
-rw-r--r-- 1 <USER> <GROUP>     229 Jul 16 08:45 1752229335_218000.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229335_418121.bin
-rw-r--r-- 1 <USER> <GROUP> 2076204 Jul 16 08:45 1752229335_418121.pcd
-rw-r--r-- 1 <USER> <GROUP>     229 Jul 16 08:45 1752229335_418121.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229335_617939.bin
-rw-r--r-- 1 <USER> <GROUP> 2062712 Jul 16 08:45 1752229335_617939.pcd
-rw-r--r-- 1 <USER> <GROUP>     231 Jul 16 08:45 1752229335_617939.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229336_117946.bin
-rw-r--r-- 1 <USER> <GROUP> 2079772 Jul 16 08:45 1752229336_117946.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229336_117946.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229336_418147.bin
-rw-r--r-- 1 <USER> <GROUP> 2075962 Jul 16 08:45 1752229336_418147.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229336_418147.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229336_918144.bin
-rw-r--r-- 1 <USER> <GROUP> 2081122 Jul 16 08:45 1752229336_918144.pcd
-rw-r--r-- 1 <USER> <GROUP>     228 Jul 16 08:45 1752229336_918144.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229337_116965.bin
-rw-r--r-- 1 <USER> <GROUP> 2094559 Jul 16 08:45 1752229337_116965.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229337_116965.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229337_418143.bin
-rw-r--r-- 1 <USER> <GROUP> 2102489 Jul 16 08:45 1752229337_418143.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229337_418143.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229337_718152.bin
-rw-r--r-- 1 <USER> <GROUP> 2079912 Jul 16 08:45 1752229337_718152.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229337_718152.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229338_117945.bin
-rw-r--r-- 1 <USER> <GROUP> 2081620 Jul 16 08:45 1752229338_117945.pcd
-rw-r--r-- 1 <USER> <GROUP>     230 Jul 16 08:45 1752229338_117945.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229338_518161.bin
-rw-r--r-- 1 <USER> <GROUP> 2099770 Jul 16 08:45 1752229338_518161.pcd
-rw-r--r-- 1 <USER> <GROUP>     228 Jul 16 08:45 1752229338_518161.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229338_918154.bin
-rw-r--r-- 1 <USER> <GROUP> 2079559 Jul 16 08:45 1752229338_918154.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229338_918154.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229339_217480.bin
-rw-r--r-- 1 <USER> <GROUP> 2086067 Jul 16 08:45 1752229339_217480.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229339_217480.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229339_617974.bin
-rw-r--r-- 1 <USER> <GROUP> 2083299 Jul 16 08:45 1752229339_617974.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229339_617974.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229340_018115.bin
-rw-r--r-- 1 <USER> <GROUP> 2099290 Jul 16 08:45 1752229340_018115.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229340_018115.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229340_418173.bin
-rw-r--r-- 1 <USER> <GROUP> 2046788 Jul 16 08:45 1752229340_418173.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229340_418173.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229340_818143.bin
-rw-r--r-- 1 <USER> <GROUP> 2070437 Jul 16 08:45 1752229340_818143.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229340_818143.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229341_018090.bin
-rw-r--r-- 1 <USER> <GROUP> 2062147 Jul 16 08:45 1752229341_018090.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229341_018090.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229341_418116.bin
-rw-r--r-- 1 <USER> <GROUP> 2063000 Jul 16 08:45 1752229341_418116.pcd
-rw-r--r-- 1 <USER> <GROUP>     228 Jul 16 08:45 1752229341_418116.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229341_818038.bin
-rw-r--r-- 1 <USER> <GROUP> 2057824 Jul 16 08:45 1752229341_818038.pcd
-rw-r--r-- 1 <USER> <GROUP>     228 Jul 16 08:45 1752229341_818038.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229342_117940.bin
-rw-r--r-- 1 <USER> <GROUP> 2052553 Jul 16 08:45 1752229342_117940.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229342_117940.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229342_518034.bin
-rw-r--r-- 1 <USER> <GROUP> 2039717 Jul 16 08:45 1752229342_518034.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229342_518034.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229343_418167.bin
-rw-r--r-- 1 <USER> <GROUP> 2033284 Jul 16 08:45 1752229343_418167.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229343_418167.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229343_617402.bin
-rw-r--r-- 1 <USER> <GROUP> 2032456 Jul 16 08:45 1752229343_617402.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229343_617402.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229343_818072.bin
-rw-r--r-- 1 <USER> <GROUP> 2038202 Jul 16 08:45 1752229343_818072.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229343_818072.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229344_017105.bin
-rw-r--r-- 1 <USER> <GROUP> 2043236 Jul 16 08:45 1752229344_017105.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229344_017105.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229344_518070.bin
-rw-r--r-- 1 <USER> <GROUP> 2068771 Jul 16 08:45 1752229344_518070.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229344_518070.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229345_218153.bin
-rw-r--r-- 1 <USER> <GROUP> 2029536 Jul 16 08:45 1752229345_218153.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229345_218153.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229345_718160.bin
-rw-r--r-- 1 <USER> <GROUP> 2066887 Jul 16 08:45 1752229345_718160.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229345_718160.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229345_918060.bin
-rw-r--r-- 1 <USER> <GROUP> 2075573 Jul 16 08:45 1752229345_918060.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229345_918060.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229346_118072.bin
-rw-r--r-- 1 <USER> <GROUP> 2068207 Jul 16 08:45 1752229346_118072.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229346_118072.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229346_418130.bin
-rw-r--r-- 1 <USER> <GROUP> 2086733 Jul 16 08:45 1752229346_418130.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229346_418130.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229346_618123.bin
-rw-r--r-- 1 <USER> <GROUP> 2089339 Jul 16 08:45 1752229346_618123.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229346_618123.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229347_218140.bin
-rw-r--r-- 1 <USER> <GROUP> 2094928 Jul 16 08:45 1752229347_218140.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229347_218140.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229347_518008.bin
-rw-r--r-- 1 <USER> <GROUP> 2097261 Jul 16 08:45 1752229347_518008.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229347_518008.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229347_918130.bin
-rw-r--r-- 1 <USER> <GROUP> 2122192 Jul 16 08:45 1752229347_918130.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229347_918130.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229348_118156.bin
-rw-r--r-- 1 <USER> <GROUP> 2118418 Jul 16 08:45 1752229348_118156.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229348_118156.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229348_918152.bin
-rw-r--r-- 1 <USER> <GROUP> 2074501 Jul 16 08:45 1752229348_918152.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229348_918152.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229349_118120.bin
-rw-r--r-- 1 <USER> <GROUP> 2069064 Jul 16 08:45 1752229349_118120.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229349_118120.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229349_318170.bin
-rw-r--r-- 1 <USER> <GROUP> 2065082 Jul 16 08:45 1752229349_318170.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229349_318170.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229349_617909.bin
-rw-r--r-- 1 <USER> <GROUP> 2049517 Jul 16 08:45 1752229349_617909.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229349_617909.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229349_818164.bin
-rw-r--r-- 1 <USER> <GROUP> 1996937 Jul 16 08:45 1752229349_818164.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229349_818164.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229350_118091.bin
-rw-r--r-- 1 <USER> <GROUP> 2027563 Jul 16 08:45 1752229350_118091.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229350_118091.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229350_318092.bin
-rw-r--r-- 1 <USER> <GROUP> 2015090 Jul 16 08:45 1752229350_318092.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229350_318092.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229350_718143.bin
-rw-r--r-- 1 <USER> <GROUP> 2021986 Jul 16 08:45 1752229350_718143.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229350_718143.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229350_918157.bin
-rw-r--r-- 1 <USER> <GROUP> 1971706 Jul 16 08:45 1752229350_918157.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229350_918157.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229351_218157.bin
-rw-r--r-- 1 <USER> <GROUP> 2007609 Jul 16 08:45 1752229351_218157.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229351_218157.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229351_518170.bin
-rw-r--r-- 1 <USER> <GROUP> 1995014 Jul 16 08:45 1752229351_518170.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229351_518170.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229351_717968.bin
-rw-r--r-- 1 <USER> <GROUP> 2005070 Jul 16 08:45 1752229351_717968.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229351_717968.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229352_418150.bin
-rw-r--r-- 1 <USER> <GROUP> 1974747 Jul 16 08:45 1752229352_418150.pcd
-rw-r--r-- 1 <USER> <GROUP>     219 Jul 16 08:45 1752229352_418150.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229352_818091.bin
-rw-r--r-- 1 <USER> <GROUP> 1938119 Jul 16 08:45 1752229352_818091.pcd
-rw-r--r-- 1 <USER> <GROUP>     218 Jul 16 08:45 1752229352_818091.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229353_218161.bin
-rw-r--r-- 1 <USER> <GROUP> 1890458 Jul 16 08:45 1752229353_218161.pcd
-rw-r--r-- 1 <USER> <GROUP>     218 Jul 16 08:45 1752229353_218161.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229354_017976.bin
-rw-r--r-- 1 <USER> <GROUP> 1989325 Jul 16 08:45 1752229354_017976.pcd
-rw-r--r-- 1 <USER> <GROUP>     219 Jul 16 08:45 1752229354_017976.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229354_318071.bin
-rw-r--r-- 1 <USER> <GROUP> 1985413 Jul 16 08:45 1752229354_318071.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229354_318071.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229354_518169.bin
-rw-r--r-- 1 <USER> <GROUP> 2022992 Jul 16 08:45 1752229354_518169.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229354_518169.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229354_718138.bin
-rw-r--r-- 1 <USER> <GROUP> 2063027 Jul 16 08:45 1752229354_718138.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229354_718138.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229354_918156.bin
-rw-r--r-- 1 <USER> <GROUP> 2090867 Jul 16 08:45 1752229354_918156.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229354_918156.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229355_418177.bin
-rw-r--r-- 1 <USER> <GROUP> 2050087 Jul 16 08:45 1752229355_418177.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229355_418177.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229356_018154.bin
-rw-r--r-- 1 <USER> <GROUP> 2060248 Jul 16 08:45 1752229356_018154.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229356_018154.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229356_518154.bin
-rw-r--r-- 1 <USER> <GROUP> 2103641 Jul 16 08:45 1752229356_518154.pcd
-rw-r--r-- 1 <USER> <GROUP>     219 Jul 16 08:45 1752229356_518154.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229356_818146.bin
-rw-r--r-- 1 <USER> <GROUP> 2083379 Jul 16 08:45 1752229356_818146.pcd
-rw-r--r-- 1 <USER> <GROUP>     218 Jul 16 08:45 1752229356_818146.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229357_318155.bin
-rw-r--r-- 1 <USER> <GROUP> 2071380 Jul 16 08:45 1752229357_318155.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229357_318155.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229357_518159.bin
-rw-r--r-- 1 <USER> <GROUP> 2055118 Jul 16 08:45 1752229357_518159.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229357_518159.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229357_718159.bin
-rw-r--r-- 1 <USER> <GROUP> 2077088 Jul 16 08:45 1752229357_718159.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229357_718159.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229358_418164.bin
-rw-r--r-- 1 <USER> <GROUP> 2129654 Jul 16 08:45 1752229358_418164.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229358_418164.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229358_718163.bin
-rw-r--r-- 1 <USER> <GROUP> 2100829 Jul 16 08:45 1752229358_718163.pcd
-rw-r--r-- 1 <USER> <GROUP>     217 Jul 16 08:45 1752229358_718163.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229359_018162.bin
-rw-r--r-- 1 <USER> <GROUP> 2061834 Jul 16 08:45 1752229359_018162.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229359_018162.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229359_418157.bin
-rw-r--r-- 1 <USER> <GROUP> 2092456 Jul 16 08:45 1752229359_418157.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229359_418157.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229359_718165.bin
-rw-r--r-- 1 <USER> <GROUP> 2116259 Jul 16 08:45 1752229359_718165.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229359_718165.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229359_917921.bin
-rw-r--r-- 1 <USER> <GROUP> 2125048 Jul 16 08:45 1752229359_917921.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229359_917921.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229360_218165.bin
-rw-r--r-- 1 <USER> <GROUP> 2147778 Jul 16 08:45 1752229360_218165.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229360_218165.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229360_718164.bin
-rw-r--r-- 1 <USER> <GROUP> 2181997 Jul 16 08:45 1752229360_718164.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229360_718164.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229360_918098.bin
-rw-r--r-- 1 <USER> <GROUP> 2179844 Jul 16 08:45 1752229360_918098.pcd
-rw-r--r-- 1 <USER> <GROUP>     234 Jul 16 08:45 1752229360_918098.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229361_118138.bin
-rw-r--r-- 1 <USER> <GROUP> 2208394 Jul 16 08:45 1752229361_118138.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229361_118138.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229361_418166.bin
-rw-r--r-- 1 <USER> <GROUP> 2184830 Jul 16 08:45 1752229361_418166.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229361_418166.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229361_718121.bin
-rw-r--r-- 1 <USER> <GROUP> 2206410 Jul 16 08:45 1752229361_718121.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229361_718121.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229362_118149.bin
-rw-r--r-- 1 <USER> <GROUP> 2191837 Jul 16 08:45 1752229362_118149.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229362_118149.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229362_418126.bin
-rw-r--r-- 1 <USER> <GROUP> 2232057 Jul 16 08:45 1752229362_418126.pcd
-rw-r--r-- 1 <USER> <GROUP>     236 Jul 16 08:45 1752229362_418126.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229362_718158.bin
-rw-r--r-- 1 <USER> <GROUP> 2233744 Jul 16 08:45 1752229362_718158.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229362_718158.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229363_118049.bin
-rw-r--r-- 1 <USER> <GROUP> 2234562 Jul 16 08:45 1752229363_118049.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229363_118049.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229363_318161.bin
-rw-r--r-- 1 <USER> <GROUP> 2225809 Jul 16 08:45 1752229363_318161.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229363_318161.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229363_518087.bin
-rw-r--r-- 1 <USER> <GROUP> 2240819 Jul 16 08:45 1752229363_518087.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229363_518087.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229363_718063.bin
-rw-r--r-- 1 <USER> <GROUP> 2233636 Jul 16 08:45 1752229363_718063.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229363_718063.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229364_118171.bin
-rw-r--r-- 1 <USER> <GROUP> 2243205 Jul 16 08:45 1752229364_118171.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229364_118171.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229364_418165.bin
-rw-r--r-- 1 <USER> <GROUP> 2184609 Jul 16 08:45 1752229364_418165.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229364_418165.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229364_718164.bin
-rw-r--r-- 1 <USER> <GROUP> 2223108 Jul 16 08:45 1752229364_718164.pcd
-rw-r--r-- 1 <USER> <GROUP>     228 Jul 16 08:45 1752229364_718164.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229365_018085.bin
-rw-r--r-- 1 <USER> <GROUP> 2248355 Jul 16 08:45 1752229365_018085.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229365_018085.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229365_417946.bin
-rw-r--r-- 1 <USER> <GROUP> 2256948 Jul 16 08:45 1752229365_417946.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229365_417946.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229365_618165.bin
-rw-r--r-- 1 <USER> <GROUP> 2246642 Jul 16 08:45 1752229365_618165.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229365_618165.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229365_817809.bin
-rw-r--r-- 1 <USER> <GROUP> 2228462 Jul 16 08:45 1752229365_817809.pcd
-rw-r--r-- 1 <USER> <GROUP>     219 Jul 16 08:45 1752229365_817809.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229366_218126.bin
-rw-r--r-- 1 <USER> <GROUP> 2211828 Jul 16 08:45 1752229366_218126.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229366_218126.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229366_618138.bin
-rw-r--r-- 1 <USER> <GROUP> 2224075 Jul 16 08:45 1752229366_618138.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229366_618138.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229366_918124.bin
-rw-r--r-- 1 <USER> <GROUP> 2261156 Jul 16 08:45 1752229366_918124.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229366_918124.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229367_318045.bin
-rw-r--r-- 1 <USER> <GROUP> 2260360 Jul 16 08:45 1752229367_318045.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229367_318045.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229367_618151.bin
-rw-r--r-- 1 <USER> <GROUP> 2250934 Jul 16 08:45 1752229367_618151.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229367_618151.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229368_018114.bin
-rw-r--r-- 1 <USER> <GROUP> 2242822 Jul 16 08:45 1752229368_018114.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229368_018114.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229368_218149.bin
-rw-r--r-- 1 <USER> <GROUP> 2246140 Jul 16 08:45 1752229368_218149.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229368_218149.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229368_418149.bin
-rw-r--r-- 1 <USER> <GROUP> 2252088 Jul 16 08:45 1752229368_418149.pcd
-rw-r--r-- 1 <USER> <GROUP>     226 Jul 16 08:45 1752229368_418149.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229368_718131.bin
-rw-r--r-- 1 <USER> <GROUP> 2250074 Jul 16 08:45 1752229368_718131.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229368_718131.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229368_918121.bin
-rw-r--r-- 1 <USER> <GROUP> 2241185 Jul 16 08:45 1752229368_918121.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229368_918121.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229369_318127.bin
-rw-r--r-- 1 <USER> <GROUP> 2224364 Jul 16 08:45 1752229369_318127.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229369_318127.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229369_918165.bin
-rw-r--r-- 1 <USER> <GROUP> 2233687 Jul 16 08:45 1752229369_918165.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229369_918165.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229370_118134.bin
-rw-r--r-- 1 <USER> <GROUP> 2238456 Jul 16 08:45 1752229370_118134.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229370_118134.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229370_418155.bin
-rw-r--r-- 1 <USER> <GROUP> 2240849 Jul 16 08:45 1752229370_418155.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229370_418155.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229370_718151.bin
-rw-r--r-- 1 <USER> <GROUP> 2249126 Jul 16 08:45 1752229370_718151.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229370_718151.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229371_018115.bin
-rw-r--r-- 1 <USER> <GROUP> 2249419 Jul 16 08:45 1752229371_018115.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229371_018115.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229371_218128.bin
-rw-r--r-- 1 <USER> <GROUP> 2246542 Jul 16 08:45 1752229371_218128.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229371_218128.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229371_818142.bin
-rw-r--r-- 1 <USER> <GROUP> 2221636 Jul 16 08:45 1752229371_818142.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229371_818142.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229372_318131.bin
-rw-r--r-- 1 <USER> <GROUP> 2192111 Jul 16 08:45 1752229372_318131.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229372_318131.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229372_618148.bin
-rw-r--r-- 1 <USER> <GROUP> 2100886 Jul 16 08:45 1752229372_618148.pcd
-rw-r--r-- 1 <USER> <GROUP>     225 Jul 16 08:45 1752229372_618148.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229372_917467.bin
-rw-r--r-- 1 <USER> <GROUP> 2137128 Jul 16 08:45 1752229372_917467.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229372_917467.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229373_118144.bin
-rw-r--r-- 1 <USER> <GROUP> 2097182 Jul 16 08:45 1752229373_118144.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229373_118144.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229373_518132.bin
-rw-r--r-- 1 <USER> <GROUP> 2131376 Jul 16 08:45 1752229373_518132.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229373_518132.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229374_018157.bin
-rw-r--r-- 1 <USER> <GROUP> 2149624 Jul 16 08:45 1752229374_018157.pcd
-rw-r--r-- 1 <USER> <GROUP>     227 Jul 16 08:45 1752229374_018157.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229374_318113.bin
-rw-r--r-- 1 <USER> <GROUP> 2132773 Jul 16 08:45 1752229374_318113.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229374_318113.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229374_918155.bin
-rw-r--r-- 1 <USER> <GROUP> 2206221 Jul 16 08:45 1752229374_918155.pcd
-rw-r--r-- 1 <USER> <GROUP>     230 Jul 16 08:45 1752229374_918155.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229375_518148.bin
-rw-r--r-- 1 <USER> <GROUP> 2247022 Jul 16 08:45 1752229375_518148.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229375_518148.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229376_018183.bin
-rw-r--r-- 1 <USER> <GROUP> 2239774 Jul 16 08:45 1752229376_018183.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229376_018183.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229376_518148.bin
-rw-r--r-- 1 <USER> <GROUP> 2193764 Jul 16 08:45 1752229376_518148.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229376_518148.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229376_818049.bin
-rw-r--r-- 1 <USER> <GROUP> 2142534 Jul 16 08:45 1752229376_818049.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229376_818049.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229377_218157.bin
-rw-r--r-- 1 <USER> <GROUP> 2157705 Jul 16 08:45 1752229377_218157.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229377_218157.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229379_018155.bin
-rw-r--r-- 1 <USER> <GROUP> 2109591 Jul 16 08:45 1752229379_018155.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229379_018155.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229379_418168.bin
-rw-r--r-- 1 <USER> <GROUP> 2106385 Jul 16 08:45 1752229379_418168.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229379_418168.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229379_718160.bin
-rw-r--r-- 1 <USER> <GROUP> 2087388 Jul 16 08:45 1752229379_718160.pcd
-rw-r--r-- 1 <USER> <GROUP>     220 Jul 16 08:45 1752229379_718160.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229380_218126.bin
-rw-r--r-- 1 <USER> <GROUP> 2079676 Jul 16 08:45 1752229380_218126.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229380_218126.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229380_618019.bin
-rw-r--r-- 1 <USER> <GROUP> 2055920 Jul 16 08:45 1752229380_618019.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229380_618019.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229380_818156.bin
-rw-r--r-- 1 <USER> <GROUP> 2050989 Jul 16 08:45 1752229380_818156.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229380_818156.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229381_318137.bin
-rw-r--r-- 1 <USER> <GROUP> 2024862 Jul 16 08:45 1752229381_318137.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229381_318137.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229381_618126.bin
-rw-r--r-- 1 <USER> <GROUP> 2036737 Jul 16 08:45 1752229381_618126.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229381_618126.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229382_118147.bin
-rw-r--r-- 1 <USER> <GROUP> 2067259 Jul 16 08:45 1752229382_118147.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229382_118147.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229382_618146.bin
-rw-r--r-- 1 <USER> <GROUP> 2076928 Jul 16 08:45 1752229382_618146.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229382_618146.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229383_218148.bin
-rw-r--r-- 1 <USER> <GROUP> 2076999 Jul 16 08:45 1752229383_218148.pcd
-rw-r--r-- 1 <USER> <GROUP>     221 Jul 16 08:45 1752229383_218148.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229383_518129.bin
-rw-r--r-- 1 <USER> <GROUP> 2092296 Jul 16 08:45 1752229383_518129.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229383_518129.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229383_818045.bin
-rw-r--r-- 1 <USER> <GROUP> 2089468 Jul 16 08:45 1752229383_818045.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229383_818045.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229384_018128.bin
-rw-r--r-- 1 <USER> <GROUP> 2088894 Jul 16 08:45 1752229384_018128.pcd
-rw-r--r-- 1 <USER> <GROUP>     219 Jul 16 08:45 1752229384_018128.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229384_218155.bin
-rw-r--r-- 1 <USER> <GROUP> 2086613 Jul 16 08:45 1752229384_218155.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229384_218155.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229384_418156.bin
-rw-r--r-- 1 <USER> <GROUP> 2093295 Jul 16 08:45 1752229384_418156.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229384_418156.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229384_918164.bin
-rw-r--r-- 1 <USER> <GROUP> 2086765 Jul 16 08:45 1752229384_918164.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229384_918164.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229385_218103.bin
-rw-r--r-- 1 <USER> <GROUP> 2122091 Jul 16 08:45 1752229385_218103.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229385_218103.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229386_918110.bin
-rw-r--r-- 1 <USER> <GROUP> 2249634 Jul 16 08:45 1752229386_918110.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229386_918110.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229387_218091.bin
-rw-r--r-- 1 <USER> <GROUP> 2242157 Jul 16 08:45 1752229387_218091.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229387_218091.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229387_418161.bin
-rw-r--r-- 1 <USER> <GROUP> 2254113 Jul 16 08:45 1752229387_418161.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229387_418161.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229387_618143.bin
-rw-r--r-- 1 <USER> <GROUP> 2246817 Jul 16 08:45 1752229387_618143.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229387_618143.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229388_018153.bin
-rw-r--r-- 1 <USER> <GROUP> 2241897 Jul 16 08:45 1752229388_018153.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229388_018153.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229388_318119.bin
-rw-r--r-- 1 <USER> <GROUP> 2250526 Jul 16 08:45 1752229388_318119.pcd
-rw-r--r-- 1 <USER> <GROUP>     223 Jul 16 08:45 1752229388_318119.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229388_616611.bin
-rw-r--r-- 1 <USER> <GROUP> 2247988 Jul 16 08:45 1752229388_616611.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229388_616611.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229388_817650.bin
-rw-r--r-- 1 <USER> <GROUP> 2247085 Jul 16 08:45 1752229388_817650.pcd
-rw-r--r-- 1 <USER> <GROUP>     224 Jul 16 08:45 1752229388_817650.txt
-rw-r--r-- 1 <USER> <GROUP>   42252 Jul 16 08:45 1752229389_117714.bin
-rw-r--r-- 1 <USER> <GROUP> 2244799 Jul 16 08:45 1752229389_117714.pcd
-rw-r--r-- 1 <USER> <GROUP>     222 Jul 16 08:45 1752229389_117714.txt
drwxr-xr-x 2 <USER> <GROUP>   12288 Jul 16 08:45 pcd
root@in-x86-dev-docker:/hfr#
```

---

<think><details><summary>Thought Process</summary>数据目录存在且内容丰富，包含了大量的.pcd、.bin和.txt文件。现在让我使用gdb来直接调试这个程序，看看能获得什么信息。</details></think>

---

数据看起来是完整的。现在让我们用GDB来调试这个问题。根据core dump排查最佳实践，我来为您提供完整的排查方案：

### 4. 使用GDB直接运行程序获取详细错误信息

---

```bash
cd /hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/build && gdb ./lcs_test
```

```
root@in-x86-dev-docker:/hfr# cd /hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs
/build && gdb ./lcs_test
---Type <return> to continue, or q <return> to quit---
```

---

_**User**_

为Y方向跳变过滤添加可视化功能，需求：
利用BEV视图显示按照X排序后原始点数组，以及检测到的跳变点，跳变点用红色标记，其余的是用蓝色标记。存储方式：
- 路径："/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/workspace/"
- bev_image_file_name = std::to_string(static_cast<double>(lidar_frame_ref_->timestamp));
- 命名：cloud_output_dir + bev_image_file_name.replace(bev_image_file_name.find("."), 1, "_") + "_filter_y_jump.png";


---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要为Y方向跳变过滤功能添加可视化功能。让我分析一下需求：

1. 利用BEV（鸟瞰图）视图显示按照X排序后的原始点数组
2. 显示检测到的跳变点，跳变点用红色标记，其余用蓝色标记
3. 存储路径和命名方式已经指定

我需要查看现有的FilterYDirectionJumpPoints函数，然后为其添加可视化功能。从附加的代码片段可以看到已有的输出路径格式。

让我先查看现有的函数实现，然后添加可视化功能。</details></think>

---

我来为Y方向跳变过滤功能添加可视化功能。首先，让我查看现有的代码结构和相关的可视化函数。

---

<details>
            <summary>Searched codebase "BEV visualization functions for drawing points in CurbSegmenter" • **0** results</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>看起来搜索结果是空的，让我尝试更具体地搜索现有的可视化相关函数。</details></think>

---

<details>
            <summary>Grep search for "DrawPolarGrid.*Visualization" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp` | L928 | `DrawPolarGridVisualization(in_cloud_ptr, indices, grid_num_rows, grid_num_cols, polar_cell_radius_size_, polar_cell_theta_size_, &jump_grids);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp` | L1774 | `void CurbSegmenter::DrawPolarGridVisualization(const PointFCloudPtr in_cloud_ptr, const PointIndices& indices,` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.hpp` | L71 | `void DrawPolarGridVisualization(const PointFCloudPtr in_cloud_ptr, const PointIndices& indices,` |

</details>

---

<think><details><summary>Thought Process</summary>很好，找到了现有的可视化函数。让我查看DrawPolarGridVisualization函数的实现，了解如何绘制BEV图像。</details></think>

---

Read file: modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp

---

<think><details><summary>Thought Process</summary>好的，我看到了现有的BEV可视化函数的实现模式。现在我需要查看FilterYDirectionJumpPoints函数的完整实现，然后为其添加可视化功能。</details></think>

---

Read file: modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp

---

<think><details><summary>Thought Process</summary>让我继续查看完整的可视化函数实现，然后查看头文件中的函数声明。</details></think>

---

Read file: modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp

---

<think><details><summary>Thought Process</summary>



现在我对现有的可视化实现有了清楚的了解。我需要查看头文件中是否有相关的声明，然后基于现有的模式为FilterYDirectionJumpPoints函数添加可视化功能。</details></think>

---

Read file: modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.hpp

---

<think><details><summary>Thought Process</summary>



现在我有了所有需要的信息。我需要为FilterYDirectionJumpPoints函数添加可视化功能。基于现有的代码模式，我将：

1. 修改FilterYDirectionJumpPoints函数，在其中添加可视化代码
2. 或者创建一个新的可视化函数，专门用于Y方向跳变可视化

让我首先查看当前的FilterYDirectionJumpPoints函数的完整实现，然后进行修改。</details></think>

---

Read file: modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp

---

<think><details><summary>Thought Process</summary>现在我了解了FilterYDirectionJumpPoints函数的完整实现。我需要修改这个函数来添加可视化功能。根据需求，我需要：

1. 显示按照X排序后的原始点数组
2. 显示检测到的跳变点（红色）和正常点（蓝色）
3. 使用BEV视图
4. 按照指定的路径和命名方式保存

我将在函数中添加可视化代码，基于现有的DrawPolarGridVisualization函数的模式。</details></think>

---

现在我来为Y方向跳变过滤功能添加可视化功能。基于现有的可视化实现模式，我将修改`FilterYDirectionJumpPoints`函数来添加BEV可视化。

---

```diff
      }
    }
+ 
+ #ifdef ENABLE_POLAR_GRID_VISUALIZATION
+   // Y方向跳变过滤可视化
+   DrawYDirectionJumpVisualization(in_cloud, sorted_indices, valid_flag);
+ #endif
  
    // 重建点云
```

---

<think><details><summary>Thought Process</summary>现在我需要添加DrawYDirectionJumpVisualization函数的声明到头文件中，然后实现这个函数。</details></think>

---

```diff
                                   const std::vector<int>* jump_grids = nullptr);
  
+   void DrawYDirectionJumpVisualization(const PointFCloudPtr in_cloud_ptr, 
+                                       const std::vector<std::size_t>& sorted_indices,
+                                       const std::vector<bool>& valid_flag);
+ 
  #endif
    // 新增：路沿点后处理过滤相关函数声明
```

---

<think><details><summary>Thought Process</summary>现在我需要实现DrawYDirectionJumpVisualization函数。我将把它添加到cpp文件中，在现有的DrawPolarGridVisualization函数之后。</details></think>

---

```diff
    return std::abs(a * point.x + b * point.y + c);
  }
+ 
+ 
+ #ifdef ENABLE_POLAR_GRID_VISUALIZATION
+ void CurbSegmenter::DrawYDirectionJumpVisualization(const PointFCloudPtr in_cloud_ptr, 
+                                                    const std::vector<std::size_t>& sorted_indices,
+                                                    const std::vector<bool>& valid_flag)
+ {
+   // BEV图像参数设置
+   const float bev_x_size = 40.0f;  // BEV图像X方向范围（米）
+   const float bev_y_size = 40.0f;  // BEV图像Y方向范围（米）
+   const int pixels_per_meter = 50; // 每米像素数
+   const int image_height = static_cast<int>(bev_x_size * pixels_per_meter);
+   const int image_width = static_cast<int>(bev_y_size * pixels_per_meter);
+   const int origin_x = int(image_width / 2);   // 图像中心X坐标（自车位置）
+   const int origin_y = int(image_height / 2);  // 图像中心Y坐标（自车位置）
+ 
+   // 创建白色背景的BEV图像
+   cv::Mat bev_img = cv::Mat(image_height, image_width, CV_8UC3, cv::Scalar(255, 255, 255));
+ 
+   // 绘制坐标轴（红色X轴向前，绿色Y轴向左）
+   cv::arrowedLine(bev_img, cv::Point(origin_x, origin_y), cv::Point(origin_x, origin_y - pixels_per_meter), cv::Scalar(0, 0, 255), 2);
+   cv::arrowedLine(bev_img, cv::Point(origin_x, origin_y), cv::Point(origin_x - pixels_per_meter, origin_y), cv::Scalar(0, 255, 0), 2);
+ 
+   // 绘制自车轮廓
+   const float ego_length = 4.5f;  // 自车长度
+   const float ego_width = 1.8f;   // 自车宽度
+   const float ego_front = 2.8f;   // 自车前端距离车辆中心
+   const float ego_rear = ego_length - ego_front;  // 自车后端距离车辆中心
+   const float ego_left = ego_width / 2.0f;
+   const float ego_right = ego_width / 2.0f;
+   
+   cv::rectangle(bev_img, 
+                 cv::Point(origin_x - static_cast<int>(ego_left * pixels_per_meter), 
+                          origin_y - static_cast<int>(ego_front * pixels_per_meter)),
+                 cv::Point(origin_x + static_cast<int>(ego_right * pixels_per_meter), 
+                          origin_y + static_cast<int>(ego_rear * pixels_per_meter)),
+                 cv::Scalar(128, 128, 128), 2);
+ 
+   // 绘制按X坐标排序后的点云，使用不同颜色区分正常点和跳变点
+   for (size_t i = 0; i < sorted_indices.size(); ++i)
+   {
+     const PointF& pt = in_cloud_ptr->at(sorted_indices[i]);
+     
+     // 过滤掉超出显示范围的点
+     if (std::abs(pt.x) > bev_x_size/2.0f || std::abs(pt.y) > bev_y_size/2.0f)
+     {
+       continue;
+     }
+ 
+     // 坐标转换：激光雷达坐标系到图像坐标系
+     // 激光雷达：X向前，Y向左；图像：X向右，Y向下
+     int draw_x = origin_x - static_cast<int>(pt.y * pixels_per_meter);  // Y轴取反
+     int draw_y = origin_y - static_cast<int>(pt.x * pixels_per_meter);  // X轴取反
+ 
+     // 边界检查
+     if (draw_x >= 0 && draw_x < image_width && draw_y >= 0 && draw_y < image_height)
+     {
+       // 根据是否为跳变点选择颜色
+       cv::Scalar point_color;
+       int point_radius;
+       
+       if (valid_flag[sorted_indices[i]])
+       {
+         // 正常点：蓝色
+         point_color = cv::Scalar(255, 0, 0);  // BGR: 蓝色
+         point_radius = 2;
+       }
+       else
+       {
+         // 跳变点：红色
+         point_color = cv::Scalar(0, 0, 255);  // BGR: 红色
+         point_radius = 3;
+       }
+       
+       // 绘制点
+       cv::circle(bev_img, cv::Point(draw_x, draw_y), point_radius, point_color, -1);
+     }
+   }
+ 
+   // 绘制按X排序顺序的连接线（淡灰色），以显示点的排序关系
+   for (size_t i = 1; i < sorted_indices.size(); ++i)
+   {
+     const PointF& prev_pt = in_cloud_ptr->at(sorted_indices[i-1]);
+     const PointF& curr_pt = in_cloud_ptr->at(sorted_indices[i]);
+     
+     // 只绘制距离较近的相邻点之间的连线，避免图像过于混乱
+     float dist = std::sqrt(std::pow(curr_pt.x - prev_pt.x, 2) + std::pow(curr_pt.y - prev_pt.y, 2));
+     if (dist < 2.0f)  // 2米内的相邻点才连线
+     {
+       // 坐标转换
+       int prev_draw_x = origin_x - static_cast<int>(prev_pt.y * pixels_per_meter);
+       int prev_draw_y = origin_y - static_cast<int>(prev_pt.x * pixels_per_meter);
+       int curr_draw_x = origin_x - static_cast<int>(curr_pt.y * pixels_per_meter);
+       int curr_draw_y = origin_y - static_cast<int>(curr_pt.x * pixels_per_meter);
+       
+       // 边界检查
+       if (prev_draw_x >= 0 && prev_draw_x < image_width && prev_draw_y >= 0 && prev_draw_y < image_height &&
+           curr_draw_x >= 0 && curr_draw_x < image_width && curr_draw_y >= 0 && curr_draw_y < image_height)
+       {
+         cv::line(bev_img, cv::Point(prev_draw_x, prev_draw_y), cv::Point(curr_draw_x, curr_draw_y), 
+                 cv::Scalar(200, 200, 200), 1);  // 淡灰色连线
+       }
+     }
+   }
+ 
+   // 添加图例和标题
+   cv::putText(bev_img, "Y-Direction Jump Filter Visualization", cv::Point(10, 25), 
+              cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 0, 0), 2);
+   cv::putText(bev_img, "Blue: Normal Points", cv::Point(10, 50), 
+              cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(255, 0, 0), 1);
+   cv::putText(bev_img, "Red: Jump Points (Filtered)", cv::Point(10, 70), 
+              cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 255), 1);
+   cv::putText(bev_img, "Gray: Ego Vehicle", cv::Point(10, 90), 
+              cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(128, 128, 128), 1);
+   cv::putText(bev_img, "Light Gray: X-sorted Connection", cv::Point(10, 110), 
+              cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(200, 200, 200), 1);
+ 
+   // 添加统计信息
+   int jump_count = 0;
+   for (size_t i = 0; i < valid_flag.size(); ++i)
+   {
+     if (!valid_flag[i])
+     {
+       jump_count++;
+     }
+   }
+   
+   std::string stats_info = cv::format("Total Points: %zu, Jump Points: %d, Threshold: %.2fm", 
+                                      in_cloud_ptr->size(), jump_count, y_direction_jump_threshold_);
+   cv::putText(bev_img, stats_info, cv::Point(10, image_height - 20), 
+              cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 0), 1);
+ 
+   // 保存可视化图像
+   std::string cloud_output_dir = "/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/workspace/";
+   std::string bev_image_file_name = std::to_string(static_cast<double>(lidar_frame_ref_->timestamp));
+   std::string output_file = cloud_output_dir + bev_image_file_name.replace(bev_image_file_name.find("."), 1, "_") + "_filter_y_jump.png";
+   
+   cv::imwrite(output_file, bev_img);
+   
+   std::cout << "Y方向跳变过滤可视化图像已保存至: " << output_file << std::endl;
+   std::cout << "过滤统计 - 总点数: " << in_cloud_ptr->size() << ", 跳变点数: " << jump_count 
+             << ", 阈值: " << y_direction_jump_threshold_ << "m" << std::endl;
+ }
+ #endif
  
  
```

---

<think><details><summary>Thought Process</summary>我发现我把函数放错位置了。让我查看文件结构，找到正确的位置来放置这个函数。我需要把它放在现有的DrawPolarGridVisualization函数之后。</details></think>

---

<details>
            <summary>Grep search for "std::cout << "极坐标栅格可视化图像已保存至"" • **0** files</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>让我重新搜索正确的位置，然后将函数移动到正确的地方。</details></think>

---

<details>
            <summary>Grep search for "极坐标栅格可视化图像已保存" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp` | L1950 | `std::cout << "极坐标栅格可视化图像已保存至: " << output_file << std::endl;` |

</details>

---

<think><details><summary>Thought Process</summary>找到了正确位置。现在我需要删除之前错误放置的函数，然后在正确的位置添加。</details></think>

---

Read file: modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp

---

<think><details><summary>Thought Process</summary>好的，现在我知道正确的位置了。我需要删除之前错误放置的函数，然后在DrawPolarGridVisualization函数之后添加新函数。</details></think>

---



---

<think><details><summary>Thought Process</summary>我需要删除之前错误位置的函数实现。看起来我之前的编辑在错误的地方添加了函数，并且编译器报错说没有这些成员函数。

让我先删除之前错误位置的函数实现。</details></think>

---

现在我需要删除之前错误位置的函数实现。让我先删除错误位置的函数：

---



---

<think><details><summary>Thought Process</summary>看起来还有一些问题，我需要找到函数调用的位置并删除它们，以及确保所有变量都是有效的。让我检查调用DrawYDirectionJumpVisualization的位置。</details></think>

---

让我找到调用DrawYDirectionJumpVisualization的位置并删除这个调用，因为我们还没有正确实现这个函数：

---



---

<think><details><summary>Thought Process</summary>我来逐一解决剩余的编译错误：

1. Line 1775: class "perception::lcs::CurbSegmenter" has no member "DrawPolarGridVisualization" - 这表明现有的DrawPolarGridVisualization函数调用也有问题
2. Line 1946: identifier "lidar_frame_ref_" is undefined - 这表明lidar_frame_ref_变量没有定义
3. Line 1956: class "perception::lcs::CurbSegmenter" has no member "DrawYDirectionJumpVisualization" - 我们已经删除了这个调用
4. Line 2081: identifier "y_direction_jump_threshold_" is undefined - 这表明y_direction_jump_threshold_变量没有定义  
5. Line 2087: identifier "lidar_frame_ref_" is undefined - 同上

让我查看这些变量的定义位置。</details></think>

---

我需要查看类的成员变量定义，找到这些变量的正确名称：

---

<details>
            <summary>Grep search for "lidar_frame_ref" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.hpp` | L103 | `LidarFrame* lidar_frame_ref_ = nullptr;` |

</details>

---

<think><details><summary>Thought Process</summary>



好的，lidar_frame_ref_是已定义的。现在让我查找y_direction_jump_threshold_变量：</details></think>

---

<details>
            <summary>Grep search for "y_direction_jump_threshold" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.hpp` | L189 | `float y_direction_jump_threshold_ = 0.75f;  // Y方向跳变阈值（米）` |

</details>

---

<think><details><summary>Thought Process</summary>



两个变量都已定义。让我检查是否有编译预处理的问题。我需要检查#ifdef ENABLE_POLAR_GRID_VISUALIZATION的定义，以及DrawPolarGridVisualization函数是否被正确包含在#ifdef中。

让我查看文件开头的预处理定义。</details></think>

---

这些变量都已经定义了。让我检查编译错误的具体位置和原因。首先让我查看DrawPolarGridVisualization函数调用的位置：

---

<details>
            <summary>Grep search for "DrawPolarGridVisualization.*in_cloud_ptr.*indices" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp` | L928 | `DrawPolarGridVisualization(in_cloud_ptr, indices, grid_num_rows, grid_num_cols, polar_cell_radius_size_, polar_cell_theta_size_, &jump_grids);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp` | L1774 | `void CurbSegmenter::DrawPolarGridVisualization(const PointFCloudPtr in_cloud_ptr, const PointIndices& indices,` |

</details>

---

<think><details><summary>Thought Process</summary>让我查看这个函数调用所在的行以及它是否在正确的#ifdef块中。</details></think>

---

Read file: modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp

---

<think><details><summary>Thought Process</summary>



看起来函数调用在正确的#ifdef中。问题可能是这个宏没有被定义。让我检查文件开头是否有这个宏的定义，或者我需要先定义它。</details></think>

---

看起来问题可能是ENABLE_POLAR_GRID_VISUALIZATION宏没有被定义。让我检查这个宏是否在文件中有定义：

---

<details>
            <summary>Grep search for "#define ENABLE_POLAR_GRID_VISUALIZATION" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.hpp` | L18 | `// #define ENABLE_POLAR_GRID_VISUALIZATION` |

</details>

---

<think><details><summary>Thought Process</summary>



宏被注释掉了，这就是为什么编译器看不到这些函数的原因。我需要启用这个宏。</details></think>

---

找到问题了！ENABLE_POLAR_GRID_VISUALIZATION宏被注释掉了。让我启用它：

---



---

<think><details><summary>Thought Process</summary>



现在我可以恢复Y方向跳变过滤的可视化功能了。首先让我重新添加函数调用：</details></think>

---

现在让我重新添加Y方向跳变过滤的可视化功能：

---



---

<think><details><summary>Thought Process</summary>现在我需要重新添加DrawYDirectionJumpVisualization函数的实现，这次是在正确的位置（DrawPolarGridVisualization函数之后）。</details></think>

---

现在我需要在正确的位置重新添加DrawYDirectionJumpVisualization函数的实现。在正确添加这个函数之前，让我确保我理解了正确的文件结构。

---

Read file: modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp

---

<think><details><summary>Thought Process</summary>



让我看看再往后一点的内容，找到正确的插入位置：</details></think>

---

Read file: modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp

---

<think><details><summary>Thought Process</summary>



看起来函数已经在那里了，但我需要检查它是否完整。让我继续查看：</details></think>

---

Read file: modules/HAF/lidar_curb_perception_segment/core/private/lcs/src/curb_segmenter/curb_segmenter.cpp

---





---

<think><details><summary>Thought Process</summary>



好的，函数已经存在并且是完整的。现在让我编译一下看看是否还有错误，或者查看函数文档确保一切正常。

让我检查一下我们的完成情况：

1. ✅ 修改了FilterYDirectionJumpPoints函数，添加了可视化调用
2. ✅ 在头文件中添加了DrawYDirectionJumpVisualization函数声明  
3. ✅ 实现了DrawYDirectionJumpVisualization函数
4. ✅ 启用了ENABLE_POLAR_GRID_VISUALIZATION宏
5. ✅ 按照需求使用了正确的路径和命名格式

现在让我们来测试这个功能是否工作正常。</details></think>

---

完美！现在所有功能都已经实现了。让我总结一下为Y方向跳变过滤添加的可视化功能：

## 功能总结

我已经成功为`FilterYDirectionJumpPoints`函数添加了完整的可视化功能：

### 1. 头文件修改 (`curb_segmenter.hpp`)
- ✅ 启用了 `ENABLE_POLAR_GRID_VISUALIZATION` 宏
- ✅ 添加了 `DrawYDirectionJumpVisualization` 函数声明

### 2. 源文件修改 (`curb_segmenter.cpp`)
- ✅ 在 `FilterYDirectionJumpPoints` 函数中添加了可视化调用
- ✅ 实现了完整的 `DrawYDirectionJumpVisualization` 函数

### 3. 可视化功能特性
- **BEV视图**: 40米×40米的鸟瞰图，每米50像素的分辨率
- **点云展示**: 
  - 🔵 **蓝色点**: 正常点（未被过滤的点）
  - 🔴 **红色点**: 跳变点（被过滤的点）
- **连接线**: 淡灰色线条显示按X坐标排序后的点序列关系
- **坐标系**: 红色X轴（向前）、绿色Y轴（向左）
- **自车**: 灰色矩形表示车辆轮廓
- **统计信息**: 显示总点数、跳变点数和阈值
- **图例**: 完整的颜色说明

### 4. 文件保存
- **路径**: `/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/workspace/`
- **命名**: `{timestamp}_filter_y_jump.png`
- **格式**: 按照您指定的命名规则，使用时间戳并将小数点替换为下划线

### 5. 控制台输出
函数会输出保存路径和统计信息，便于调试和监控。

现在当您运行路沿检测程序时，每次执行Y方向跳变过滤时都会生成一张BEV可视化图像，清晰地展示哪些点被识别为跳变点并被过滤掉，这将极大地帮助您分析和调试算法效果。

---

_**User**_

请在可视化上额外添加： 前后点之间的Y之差 标记在点旁边

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求在可视化图像上添加前后点之间的Y之差标记在点旁边。从附件的图片来看，是一个Y方向跳变过滤的BEV可视化图像，显示了按X排序后的点云，其中蓝色点是正常点，红色点是跳变点。

用户想要在每个点旁边显示这个点与前一个点和后一个点之间的Y方向距离差，这样可以更直观地看到跳变的数值。

我需要修改DrawYDirectionJumpVisualization函数，在绘制点的时候，同时在点旁边添</details></think>

---

