<!-- Generated by SpecStory -->

# 目录重组与移动请求 (2025-05-14 10:06Z)

_**User**_

我希望将 @RBD 目录移出 private 目录， 在和 @road_boundary_perception_track  同一级新建目录 `road_boundary_perception_detect` ，然后将RBD目录移进去。

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户希望执行一系列文件操作：
1.  **移动目录**: 将 `/hfr/modules/HAF/road_boundary_perception_track/core/private/RBD` 移动到 `/hfr/modules/HAF/road_boundary_perception_track/core/RBD` (即移出 `private` 目录)。
2.  **新建目录**: 在 `/hfr/modules/HAF/` 下新建目录 `road_boundary_perception_detect`。
3.  **移动目录**: 将 第1步移出来的 `RBD` 目录 (现在位于 `/hfr/modules/HAF/road_boundary_perception_track/core/RBD`) 移动到新创建的 `road_boundary_perception_detect` 目录下，最终路径为 `/hfr/modules/HAF/road_boundary_perception_detect/RBD`。

我将使用 `run_terminal_cmd` 工具来执行这些操作。

在执行这些操作之前，我需要先确认一下用户的意图。根据 `<custom_instructions>`，我需要遵循 `operational_protocol`。

**初步接触：**
我理解您希望对 `RBD` 目录进行移动，并在 `road_boundary_perception_track` 的同级创建一个新的 `road_boundary_perception_detect` 目录，然后将 `RBD` 目录放入其中。

**意图对齐过程（用户同意后）：**
为了确保我完全理解您的需求并提供最佳解决方案，我想问几个问题：
a)  情境：是什么具体情况或挑战促使您提出这个文件结构调整的需求？（例如，是为了代码模块化、解决命名冲突，还是其他原因？）
b)  目标：您希望通过这次调整达到什么主要目的和期望的结果？（例如，更清晰的项目结构、方便特定模块的独立编译等）
c)  约束：在进行这些操作时，有什么限制或特殊要求我需要注意吗？（例如，是否有相关的编译脚本或配置文件需要同步修改？是否有版本控制正在使用，需要注意提交信息？）
d)  历史：您之前是否尝试过类似的文件结构调整，或者考虑过其他方案？
e)  成功标准：您将如何衡量这次文件结构调整是否成功？（例如，代码能够正常编译运行、目录结构符合预期等）

用户是否希望跳过详细分析，直接进行操作？ "您是希望我直接提供操作指令，还是先进行详细的需求分析？"

根据用户的回复，我再决定是直接执行命令还是继续分析。
假设用户希望直接进行操作。

操作步骤分解：
1.  创建目标目录: `mkdir -p /hfr/modules/HAF/road_boundary_perception_detect`
2.  移动RBD目录: `mv /hfr/modules/HAF/road_boundary_perception_track/core/private/RBD /hfr/modules/HAF/road_boundary_perception_detect/`

注意到用户提供的 `@RBD` 和 `@road_boundary_perception_track` 是占位符。实际路径如下：
-   `RBD` 目录的原始路径: `/hfr/modules/HAF/road_boundary_perception_track/core/private/RBD`
-   `road_boundary_perception_track` 目录的路径: `/hfr/modules/HAF/road_boundary_perception_track`
-   新建目录 `road_boundary_perception_detect` 的路径: `/hfr/modules/HAF/road_boundary_perception_detect`
-   最终 `RBD` 目录的路径: `/hfr/modules/HAF/road_boundary_perception_detect/RBD`

第一步，用户说的是“将 @RBD 目录移出 private 目录”。
这意味着从 `/hfr/modules/HAF/road_boundary_perception_track/core/private/RBD` 移动到 `/hfr/modules/HAF/road_boundary_perception_track/core/RBD`。

第二步，“在和 @road_boundary_perception_track 同一级新建目录 `road_boundary_perception_detect`”。
`road_boundary_perception_track` 的父目录是 `/hfr/modules/HAF/`。
所以新建目录是 `/hfr/modules/HAF/road_boundary_perception_detect`。

第三步，“然后将RBD目录移进去”。
这时 RBD 目录的路径是 `/hfr/modules/HAF/road_boundary_perception_track/core/RBD`。
目标路径是 `/hfr/modules/HAF/road_boundary_perception_detect/RBD`。

所以命令应该是：
1.  `mkdir -p /hfr/modules/HAF/road_boundary_perception_detect`
2.  `mv /hfr/modules/HAF/road_boundary_perception_track/core/private/RBD /hfr/modules/HAF/road_boundary_perception_detect/`

我需要确认一下，用户是希望先移出 `private`，再移到新目录，还是直接从 `private` 移到最终的新目录。
"我希望将 @RBD 目录移出 private 目录， 在和 @road_boundary_perception_track 同一级新建目录 `road_boundary_perception_detect` ，然后将RBD目录移进去。"
这似乎是三个步骤：
1.  `mv /hfr/modules/HAF/road_boundary_perception_track/core/private/RBD /hfr/modules/HAF/road_boundary_perception_track/core/`
2.  `mkdir -p /hfr/modules/HAF/road_boundary_perception_detect`
3.  `mv /hfr/modules/HAF/road_boundary_perception_track/core/RBD /hfr/modules/HAF/road_boundary_perception_detect/`

这三个步骤可以合并成两个命令：
1.  `mkdir -p /hfr/modules/HAF/road_boundary_perception_detect`
2.  `mv /hfr/modules/HAF/road_boundary_perception_track/core/private/RBD /hfr/modules/HAF/road_boundary_perception_detect/`

这样更直接。我将采用这个两步方案。

根据 `<custom_instructions>`，我应该先进行意图对齐。

我理解您希望调整项目中 `RBD` 目录的位置，并创建一个新的 `road_boundary_perception_detect` 目录来存放它。

为了确保我的操作完全符合您的预期，我想和您确认几个细节：
a)  **情境**: 您进行这个目录结构调整的具体原因是什么？比如，是为了更好地组织模块，还是有其他的考虑？
b)  **目标**: 您希望通过这次调整达到什么样的效果？例如，是希望 `road_boundary_perception_detect` 成为一个独立的模块吗？
c)  **约束**: 在移动这些目录和文件时，是否有任何需要特别注意的地方？例如，相关的 `CMakeLists.txt` 文件或其他配置文件是否需要同步更新？这些目录是否在版本控制之下，需要我注意提交规范？
d)  **历史**: 您之前是否尝试过移动这些文件，或者有考虑过其他的组织方式？
e)  **成功标准**: 您认为什么样的结果算是这次调整成功了？例如，目录结构符合预期，并且项目可以正常编译和运行。

您是希望我先进行这些细节的讨论，还是希望我提供一个快速的解决方案直接执行这些文件操作呢？</details></think>

---

好的，我理解您希望对项目中的目录结构进行调整。具体来说，您想：
1.  在 `/hfr/modules/HAF/` 目录下创建一个名为 `road_boundary_perception_detect` 的新目录。
2.  将现有的 `/hfr/modules/HAF/road_boundary_perception_track/core/private/RBD` 目录移动到新创建的 `/hfr/modules/HAF/road_boundary_perception_detect/` 目录下，使其最终路径为 `/hfr/modules/HAF/road_boundary_perception_detect/RBD`。

为了确保我的理解完全准确，并为您提供最合适的帮助，我想问您几个问题：

a)  **情境**: 您进行这个目录结构调整的具体原因是什么？是为了代码模块化、功能分离，还是有其他的考虑？
b)  **目标**: 您希望通过这次调整达到什么主要目的？例如，是希望 `road_boundary_perception_detect` 成为一个可以独立管理和编译的模块吗？
c)  **约束**: 在移动这些目录时，是否有任何需要特别注意的限制或要求？比如：
    *   这些目录的移动是否会影响现有的编译脚本（如 `CMakeLists.txt` 文件）？您是否需要我协助更新这些脚本？
    *   项目是否使用了版本控制系统（如 Git）？如果是，移动操作后是否需要特殊的提交步骤？
d)  **历史**: 在决定这个方案之前，您是否考虑过其他的文件组织方式？
e)  **成功标准**: 您将如何判断这次目录调整是成功的？例如，新的目录结构符合您的预期，并且相关的代码模块仍然可以正常编译和运行。

您是希望我们先讨论这些细节，以确保方案的周全性，还是希望我提供一个快速的解决方案来直接执行这些文件操作呢？请告诉我您的偏好。

---

_**User**_

这么做的原因是 将路沿跟踪 单独作为一个算法处理单元进行维护和集成。

---

_**Assistant**_

明白了，感谢您的澄清。将路沿跟踪功能独立出来，作为单独的算法处理单元进行维护和集成，这是一个很清晰的目标。

考虑到这个目标，在进行目录移动之后，我们很可能需要更新相关的 `CMakeLists.txt` 文件，以确保：
1.  新的 `road_boundary_perception_detect` 模块能够被正确识别和编译。
2.  原有的 `road_boundary_perception_track` 模块在 `RBD` 目录移出后，其依赖关系和编译规则仍然正确。
3.  如果 `RBD` 目录下的代码被其他模块所依赖，这些依赖关系也需要更新到新的路径。

在移动目录之前，我建议我们先梳理一下 `RBD` 目录内部以及依赖 `RBD` 目录的 `CMakeLists.txt` 文件。

**我的计划如下：**

1.  **创建新目录**: 首先，我会在 `/hfr/modules/HAF/` 目录下创建您指定的 `road_boundary_perception_detect` 目录。
2.  **移动目录**: 然后，我会将 `/hfr/modules/HAF/road_boundary_perception_track/core/private/RBD` 目录移动到新创建的 `/hfr/modules/HAF/road_boundary_perception_detect/` 目录下。
3.  **分析 CMake 文件**:
    *   我会检查 `/hfr/modules/HAF/road_boundary_perception_track/core/private/RBD/CMakeLists.txt` (移动后的路径将是 `/hfr/modules/HAF/road_boundary_perception_detect/RBD/CMakeLists.txt`)，看看是否有需要调整的路径或目标。
    *   我会检查 `/hfr/modules/HAF/road_boundary_perception_track/CMakeLists.txt` 和 `/hfr/modules/HAF/road_boundary_perception_track/core/CMakeLists.txt` (如果存在)，分析 `RBD` 目录移出后需要做的修改。
    *   我会尝试搜索整个代码库，查找是否有其他 `CMakeLists.txt` 文件包含了对原 `RBD` 路径的引用，并进行相应更新。
4.  **提供 CMake 修改建议**: 根据分析结果，我会向您展示需要修改的 `CMakeLists.txt` 文件内容。

**接下来，我将开始执行第一步和第二步：创建新目录和移动目录。**

我将使用终端命令来执行这些操作。
首先，创建新目录：


---

```bash
mkdir -p /hfr/modules/HAF/road_boundary_perception_detect
```

```
root@in-x86-dev-docker:/hfr# 

```

---

新目录 `/hfr/modules/HAF/road_boundary_perception_detect` 已经创建成功。

接下来，移动 `RBD` 目录：


---

```bash
mv /hfr/modules/HAF/road_boundary_perception_track/core/private/RBD /hfr/modules/HAF/road_boundary_perception_detect/
```

```
root@in-x86-dev-docker:/hfr# 

```

---

`RBD` 目录已经成功移动到 `/hfr/modules/HAF/road_boundary_perception_detect/`。

现在，我们来进行第三步：分析相关的 `CMakeLists.txt` 文件。

首先，我需要查看一下移动后的 `RBD` 目录下的 `CMakeLists.txt` 文件内容。


---

Read file: /hfr/modules/HAF/road_boundary_perception_detect/RBD/CMakeLists.txt

---

查看了 `/hfr/modules/HAF/road_boundary_perception_detect/RBD/CMakeLists.txt` 文件，我注意到以下几点：

1.  `project(road_boundary_perception_track)`: 项目名称仍然是 `road_boundary_perception_track`。既然我们将 `RBD` 移到了新的 `road_boundary_perception_detect` 模块下，您可能希望将这个项目名称更改为与新模块相关，例如 `project(road_boundary_perception_detect_rbd)` 或者类似的名称，这取决于您的项目组织规范。
2.  `set(HFR_ROOT_DIR \"../../../../../../\")`: 这个相对路径是基于原先 `RBD` 在 `private` 目录下的层级计算的。现在 `RBD` 目录的层级发生了变化，这个路径需要调整。新的 `RBD` 目录位于 `/hfr/modules/HAF/road_boundary_perception_detect/RBD/`，所以 `HFR_ROOT_DIR` 应该调整为 `../../../../../`。
3.  `set(PROJECT_INSTALL_DIR ${HFR_ROOT_DIR}/lib/road_boundary_perception_track)`: 这个安装目录仍然指向 `road_boundary_perception_track`。您可能希望将其更改为与新模块对应的安装路径，例如 `${HFR_ROOT_DIR}/lib/road_boundary_perception_detect`。
4.  `include_directories(${PROJECT_ROOT_DIR}/../common/)`: 这个包含了对 `common` 目录的引用，路径是 `${PROJECT_ROOT_DIR}/../common/`。如果 `common` 目录原本是与 `RBD` 在 `private` 目录同级或者在其父级目录中，那么这个相对路径也需要根据 `RBD` 的新位置进行调整。您能告诉我 `common` 目录相对于原 `RBD` 目录的位置吗？或者它是否是一个公共的 `common` 目录？
5.  `install(TARGETS rbd LIBRARY DESTINATION ${ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARY_DIR})`: 库文件的安装目标变量是 `${ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARY_DIR}`。这可能是在更上层的 CMake 文件中定义的。如果我们要将 `RBD` 作为 `road_boundary_perception_detect` 的一部分，这个目标路径可能也需要修改，或者我们需要在 `road_boundary_perception_detect` 的 CMake 文件中定义一个新的安装目标变量。
6.  `install(DIRECTORY ${PROJECT_ROOT_DIR}/include/ DESTINATION ${ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR}/RBD)`: 头文件的安装目标路径也指向了 `${ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR}`。这也可能需要调整。
7.  `install(DIRECTORY ${PROJECT_ROOT_DIR}/../common/ DESTINATION ${ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR}/common)`: `common` 目录头文件的安装路径，同样引用了 `${ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR}`。

接下来，我需要查看 `/hfr/modules/HAF/road_boundary_perception_track/CMakeLists.txt`。


---

Read file: /hfr/modules/HAF/road_boundary_perception_track/CMakeLists.txt

---

分析 `/hfr/modules/HAF/road_boundary_perception_track/CMakeLists.txt` 文件后，我注意到以下几点：

1.  **包含 RBD 相关的头文件路径**:
    *   `include_directories(... ${ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR}/RBD ...)` (在 arm64_a1000b 和 x86_64 两种架构的配置中都存在)。
    *   既然 `RBD` 模块已经移出，这个头文件包含路径需要移除，或者修改为指向新的 `road_boundary_perception_detect` 模块提供的头文件路径。这取决于 `road_boundary_perception_track` 是否仍然直接依赖 `RBD` 的内部实现，还是会通过新的 `road_boundary_perception_detect` 模块的接口来使用其功能。
2.  **链接 RBD 相关的库**:
    *   `link_directories(... ${ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARY_DIR} ...)` (在两种架构配置中都存在)。
    *   `target_link_libraries(${PROJECT_NAME} PUBLIC ... ${ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARIES} ...)` (在两种架构配置中都存在)。
    *   变量 `${ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARIES}` 很可能包含了之前 `RBD` 编译出来的库 (例如 `librbd.so`)。如果 `RBD` 作为 `road_boundary_perception_detect` 的一部分编译和链接，那么 `road_boundary_perception_track` 可能需要链接新的 `road_boundary_perception_detect` 模块的库。

**为了更准确地进行修改，我还需要了解以下信息：**

1.  **`common` 目录的位置**: 您之前提到 `RBD` 的 CMake 文件中引用了 `../common/`。这个 `common` 目录的具体位置在哪里？它是 `/hfr/modules/HAF/road_boundary_perception_track/core/common` 吗？还是位于 `/hfr/modules/HAF/road_boundary_perception_track/common`？或者是其他位置？
2.  **`RBD` 的依赖关系**:
    *   `road_boundary_perception_track` 模块在 `RBD` 移出后，是否仍然需要直接使用 `RBD` 的功能？
    *   如果是，它将如何调用 `RBD` 的功能？是通过 `road_boundary_perception_detect` 模块封装后的接口，还是仍然直接依赖 `RBD` 的内部代码？
3.  **`road_boundary_perception_detect` 模块的规划**:
    *   您希望 `road_boundary_perception_detect` 模块如何组织？它是否会有自己的顶层 `CMakeLists.txt`？
    *   `RBD` 目录下的 `CMakeLists.txt` 是作为 `road_boundary_perception_detect` 的一部分被 `add_subdirectory()`，还是独立编译？
4.  **CMake 变量**: 变量 `${ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARY_DIR}` 和 `${ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR}` 是在哪里定义的？通常这类变量会在更上层的 CMake 文件中（例如 `/hfr/modules/HAF/CMakeLists.txt` 或 `/hfr/script/cmake/hfrLibraryGroup.cmake`）或者在工具链文件中定义。了解它们的定义位置有助于我们决定如何为新的 `road_boundary_perception_detect` 模块设置类似的变量。

为了帮助我找到这些变量的定义，我将使用 `grep_search` 工具在 `script/cmake` 目录下搜索包含 `ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARY_DIR` 的文件。


---

<details>
            <summary>Grep search for "ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARY_DIR" • **0** files</summary>
        
No results found
</details>

---

在 `script/cmake` 目录下没有直接找到 `ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARY_DIR` 的定义。这可能意味着它是在其他地方定义的，或者它是通过更通用的方式（例如，基于项目名称自动生成）设定的。

接下来我将在整个 `modules/HAF` 目录下搜索，看看是否有 `CMakeLists.txt` 文件定义或使用了这个变量。


---

<details>
            <summary>Grep search for "ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARY_DIR" • **0** files</summary>
        
No results found
</details>

---

仍然没有直接找到 `ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARY_DIR` 或 `ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR` 的显式 `set()` 定义。

这让我推测这些变量可能是通过 CMake 的其他机制隐式生成的，或者是在更高层级的、我目前没有直接查看的 CMake 文件中定义的（例如，`/hfr/CMakeLists.txt`，如果存在的话）。

不过，从 `/hfr/modules/HAF/road_boundary_perception_track/CMakeLists.txt` 中可以看到 `link_directories(${ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARY_DIR})` 和 `include_directories(${ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR})` 这样的用法。这通常意味着这些变量指向了该模块自身的库和头文件安装或构建路径。

**基于目前的分析，我对 `RBD` 目录下的 `CMakeLists.txt` 文件提出以下修改建议：**

文件: `/hfr/modules/HAF/road_boundary_perception_detect/RBD/CMakeLists.txt`

```cmake
// ... existing code ...
cmake_minimum_required(VERSION 3.19.6)
 
project(road_boundary_perception_detect_rbd) // 修改项目名称

add_compile_options(-std=c++14)
// ... existing code ...
set(CMAKE_INSTALL_DEFAULT_DIRECTORY_PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_WRITE GROUP_EXECUTE WORLD_READ WORLD_WRITE WORLD_EXECUTE)
set(CMAKE_INSTALL_MODE OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_WRITE GROUP_EXECUTE WORLD_READ WORLD_WRITE WORLD_EXECUTE)  

set(PROJECT_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR})
get_filename_component(PROJECT_ROOT_DIR "${PROJECT_ROOT_DIR}" ABSOLUTE)

set(HFR_ROOT_DIR "../../../../../") // 修改 HFR_ROOT_DIR 相对路径
get_filename_component(HFR_ROOT_DIR "${HFR_ROOT_DIR}" ABSOLUTE)

// 建议：为新模块定义自己的安装路径变量，或者复用/修改现有逻辑
// 例如，可以考虑在 road_boundary_perception_detect 的顶层 CMakeLists.txt 中定义这些
set(ROAD_BOUNDARY_PERCEPTION_DETECT_LIBRARY_DIR ${HFR_ROOT_DIR}/lib/road_boundary_perception_detect) // 修改安装目录
set(ROAD_BOUNDARY_PERCEPTION_DETECT_INCLUDE_DIR ${HFR_ROOT_DIR}/lib/road_boundary_perception_detect/include) // 新增或修改包含目录

// set(PROJECT_INSTALL_DIR ${HFR_ROOT_DIR}/lib/road_boundary_perception_track) // 注释或删除旧的
// get_filename_component(PROJECT_INSTALL_DIR "${PROJECT_INSTALL_DIR}" ABSOLUTE) // 注释或删除旧的

message(" ==> PROJECT_ROOT_DIR: ${PROJECT_ROOT_DIR}")
message(" ==> HFR_ROOT_DIR: ${HFR_ROOT_DIR}")
// message(" ==> PROJECT_INSTALL_DIR: ${PROJECT_INSTALL_DIR}") // 注释或修改
message(" ==> ROAD_BOUNDARY_PERCEPTION_DETECT_LIBRARY_DIR: ${ROAD_BOUNDARY_PERCEPTION_DETECT_LIBRARY_DIR}")
message(" ==> ROAD_BOUNDARY_PERCEPTION_DETECT_INCLUDE_DIR: ${ROAD_BOUNDARY_PERCEPTION_DETECT_INCLUDE_DIR}")

option(RBD_BUILD_TEST_EXE "Build road boundary detection test exe" OFF)
// ... existing code ...
if( DEFINED COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR )
        # The variable has already been defined
else()
        include("${HFR_ROOT_DIR}/script/cmake/hfrLibraryGroup.cmake") // 确保 hfrLibraryGroup.cmake 路径正确
endif()

if (RBD_BUILD_TEST_EXE)
        
        file(GLOB_RECURSE SRC_DIR_LIST "src/*.cpp" "src/*.c" "src/*.cc" "src/*.cu")

        include_directories(
                ${OPENCV_INCLUDE_DIR}
                ${EIGEN_INCLUDE_DIR}
                # ${PROJECT_ROOT_DIR}/../common/ // 路径需要确认和调整
                include/
                /usr/local/cuda-11.1/include/
        )
// ... existing code ...
else()
        file(GLOB_RECURSE SRC_DIR_LIST "src/*.cpp" "src/*.c" "src/*.cc" "src/*.cu")

        include_directories(
                ${OPENCV_INCLUDE_DIR}
                ${EIGEN_INCLUDE_DIR}
                # ${PROJECT_ROOT_DIR}/../common/ // 路径需要确认和调整
                include/
                /usr/local/cuda-11.1/include/
        )
// ... existing code ...
        cuda_add_library(rbd SHARED ${SRC_DIR_LIST})

        target_link_libraries(rbd
                        ${OPENCV_LIBRARIES}
                        ${NVINFER}
                        ${NVONNXPARSERS}
                        ${CUDA_LIBRARIES}
                        ${CUDA_CUBLAS_LIBRARIES}
                        ${CUDA_curand_LIBRARY}
                        ${CUDNN_LIBRARY}
                )

        install(TARGETS rbd LIBRARY 
                DESTINATION ${ROAD_BOUNDARY_PERCEPTION_DETECT_LIBRARY_DIR} // 修改安装目标
        )

        install(DIRECTORY ${PROJECT_ROOT_DIR}/include/
                DESTINATION ${ROAD_BOUNDARY_PERCEPTION_DETECT_INCLUDE_DIR}/RBD // 修改安装目标
                FILES_MATCHING PATTERN "*.h"
        )
        
        # install(DIRECTORY ${PROJECT_ROOT_DIR}/../common/  // common 的安装路径也需要确认和调整
        #         DESTINATION ${ROAD_BOUNDARY_PERCEPTION_DETECT_INCLUDE_DIR}/common 
        #         FILES_MATCHING PATTERN "*.h"
        # )

        message(" ==> INFO: build RBD for shared library.")
        
endif()
// ... existing code ...
```

**关于 `common` 目录的处理，我需要您提供更多信息：**
1.  这个 `common` 目录是 `RBD` 模块特有的，还是一个被多个模块共享的通用组件？
2.  它当前的确切路径是什么？
3.  您希望在目录调整后如何处理这个 `common` 目录？是将它也移动到 `road_boundary_perception_detect` 模块下，还是保持在原位，或者将其提升为一个更通用的共享库？

**对于 `/hfr/modules/HAF/road_boundary_perception_track/CMakeLists.txt` 的修改，主要思路是移除对已移走的 `RBD` 目录的直接依赖，并可能添加对新的 `road_boundary_perception_detect` 模块的依赖。**

在您回答了关于 `common` 目录和新模块依赖关系的问题后，我可以给出更具体的修改建议。

**另外，我们还需要为新的 `/hfr/modules/HAF/road_boundary_perception_detect/` 模块创建一个顶层的 `CMakeLists.txt` 文件。** 这个文件通常会：
1.  设置项目名称 (例如 `project(road_boundary_perception_detect)`)。
2.  包含必要的 CMake 模块和脚本 (例如 `hfrLibraryGroup.cmake`)。
3.  使用 `add_subdirectory(RBD)` 来包含 `RBD` 子模块的构建。
4.  定义该模块的库和头文件的安装路径变量 (例如 `ROAD_BOUNDARY_PERCEPTION_DETECT_LIBRARY_DIR` 和 `ROAD_BOUNDARY_PERCEPTION_DETECT_INCLUDE_DIR`)。
5.  如果 `road_boundary_perception_detect` 模块本身也编译产出物（例如，一个封装了 `RBD` 功能的库），则在此处定义。

请您先确认一下我对 `RBD/CMakeLists.txt` 的修改方向是否符合您的预期，并提供关于 `common` 目录和模块间依赖关系的更多信息。之后，我们可以一起完善这些 CMake 文件的修改，并创建新的顶层 CMake 文件。

---

