<!-- Generated by SpecStory -->

# CMake Error: Unknown Arguments in If Statement (2025-05-14 14:03:32)

_**User**_

执行cmake，调用该文件报错：

f(${HFR_ARCH} STREQUAL "tda4")
  set(TOOLCHAIN "$ENV{PSDKRA_PATH}/gcc-arm-9.2-2019.12-x86_64-aarch64-none-linux-gnu")
  set(CMAKE_C_COMPILER "${TOOLCHAIN}/bin/aarch64-none-linux-gnu-gcc")
  set(CMAKE_CXX_COMPILER "${TOOLCHAIN}/bin/aarch64-none-linux-gnu-g++")
endif()

报错信息：
cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=Debug
CMake Error at cmake/hfrPlatformSettings.cmake:17 (if):
  if given arguments:

    "STREQUAL" "tda4"

  Unknown arguments specified
Call Stack (most recent call first):
  CMakeLists.txt:36 (include)


-- Configuring incomplete, errors occurred!
See also "/hfr/build/CMakeFiles/CMakeOutput.log

---

_**Assistant**_



---

CMake 错误是因为 `${HFR_ARCH}` 变量为空，导致 if 语句变成了 `if( STREQUAL "tda4")`，缺少比较的第一个参数。

这个变量应该从环境变量 `HASCO_ENV` 获取值：
```cmake
set_global(VAR HFR_ARCH VALUE $ENV{HASCO_ENV})
```

解决方法：

1. 设置环境变量后再运行 cmake:
```
export HASCO_ENV=arm64_a1000b  # 或其他适合的目标架构
cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=Debug
```

2. 或修改 cmake 文件，增加默认值和检查:
```cmake
# 检查 HASCO_ENV 是否存在，不存在则设置默认值
if("$ENV{HASCO_ENV}" STREQUAL "")
  set_global(VAR HFR_ARCH VALUE "arm64_a1000b")  # 设置默认值
else()
  set_global(VAR HFR_ARCH VALUE $ENV{HASCO_ENV})
endif()

# 使用前确保变量非空
if(NOT "${HFR_ARCH}" STREQUAL "" AND "${HFR_ARCH}" STREQUAL "tda4")
```

这样可以避免变量为空导致的语法错误。

---

_**User**_

HFR_ARCH 在 @CMakeLists.txt 中的 L22-L24 定义，为什么执行cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=Debug 没有生效？

---

_**Assistant**_



---

问题在于 CMakeLists.txt 中的设置方式与实际执行方式不匹配:

1. CMakeLists.txt 中的逻辑是:
```cmake
if(NOT HFR_ARCH)
  set(HFR_ARCH  $ENV{HASCO_ENV})
endif()
```

2. 这段代码只检查了 HFR_ARCH 是否被定义，但没有检查环境变量 HASCO_ENV 是否为空

3. 当您执行 `cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=Debug` 时:
   - 没有通过 `-DHFR_ARCH=xxx` 设置 HFR_ARCH
   - 环境变量 HASCO_ENV 也未被设置

4. 结果 HFR_ARCH 被设置为空值，导致 hfrPlatformSettings.cmake 中的 `if(${HFR_ARCH} STREQUAL "tda4")` 变成了 `if( STREQUAL "tda4")`，这是语法错误

解决方法:

1. 设置环境变量再运行 cmake:
```
export HASCO_ENV=arm64_a1000b
cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=Debug
```

2. 直接在命令行指定 HFR_ARCH:
```
cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=Debug -DHFR_ARCH=arm64_a1000b
```

3. 或修改 CMakeLists.txt 增加检查和默认值:
```cmake
if(NOT HFR_ARCH)
  if("$ENV{HASCO_ENV}" STREQUAL "")
    set(HFR_ARCH "arm64_a1000b") # 设置默认值
    message(STATUS "环境变量 HASCO_ENV 未设置，使用默认架构: ${HFR_ARCH}")
  else()
    set(HFR_ARCH $ENV{HASCO_ENV})
  endif()
endif()
```

---

_**User**_

HASCO_ENV 默认值是在 @Dockerfile 中获得的，是否因为 在执行 bash script/docker/ @dev_start_gpu.sh  时跳过了？

---

_**Assistant**_



---

是的，您的分析正确。问题的原因是：

1. HASCO_ENV 环境变量是在 Docker 容器内部设置的：
   ```docker
   ENV HASCO_ENV=x86_64
   ```

2. 当您运行 `bash script/docker/dev_

---

