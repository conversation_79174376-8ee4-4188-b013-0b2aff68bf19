<!-- Generated by SpecStory -->

# 更新脚本以适配模块 (2025-04-10 05:41Z)

_**User**_

将脚本中的模块 更新为适配 road_boundary_perception_track 这个模块

---

_**Assistant**_

我会帮你将脚本更新为适配 road_boundary_perception_track 模块。首先我需要分析当前脚本结构并进行相应修改。

---

Read file: script/deploy_RBT.sh

---

<details>
            <summary>Listed directory lib/road_boundary_perception_track • **4** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📁 `arm64_a1000b` |
| 📄 `README.md` |
| 📁 `x86_64` |

</details>

---

<details>
            <summary>Listed directory lib/road_boundary_perception_track/arm64_a1000b • **2** results</summary>
        
| Name |
|-------|
| 📁 `lib` |
| 📁 `include` |

</details>

---

<details>
            <summary>Listed directory build_a1000/install/arm64_a1000b/modules • **26** results</summary>
        
| Name |
|-------|
| 📁 `lidar_curb_perception_segment` |
| 📁 `lidar_obstacle_perception_segment` |
| 📁 `vehicle_service_soc` |
| 📁 `lidar_service_hasco` |
| 📁 `mqtt_service` |
| 📁 `time_service` |
| 📁 `hlog_service` |
| 📁 `heartControlStop` |
| 📁 `mainboard` |
| 📁 `em_service` |
| 📁 `data_transfer` |
| 📁 `config_service` |
| 📁 `hpp_hmi` |
| 📁 `hpp_function` |
| 📁 `hpp_calib_function` |
| 📁 `road_boundary_perception_track` |
| 📁 `pointcloud_preprocess` |
| 📁 `planning` |
| 📁 `map` |
| 📁 `lidar_obstacle_perception_track` |
| 📁 `lidar_obstacle_perception_fusion` |
| 📁 `lidar_obstacle_perception_detect` |
| 📁 `fusion_localization_cargovehicle` |
| 📁 `control` |
| 📁 `autocalib_multilidar` |
| 📁 `autocalib` |

</details>

---

现在我已经确认了目录结构，我将修改脚本以适配 road_boundary_perception_track 模块。

---

```diff
  # ----------------------------
  # lib目录路径，包含库文件和软链接
- LIB_DIR="./lib/multi_sensor_obstacle_fusion/arm64_a1000b/lib"
+ LIB_DIR="./lib/road_boundary_perception_track/arm64_a1000b/lib"
  # 模块目录路径
- MODULE_DIR="./build_a1000/install/arm64_a1000b/modules/multi_sensor_obstacle_fusion"
+ MODULE_DIR="./build_a1000/install/arm64_a1000b/modules/road_boundary_perception_track"
  
  # ----------------------------
```

```diff
  
  # 目标目录名
- target_dir="msf_a1000_bin_v$version_$current_datetime"
+ target_dir="rbt_a1000_bin_v${version}_${current_datetime}"
  
  # ----------------------------
```

```diff
  cp -a "$LIB_DIR" "$tmp_dir/$target_dir/lib"
  
- # 复制 multi_sensor_obstacle_fusion 模块目录
- cp -a "$MODULE_DIR" "$tmp_dir/$target_dir/multi_sensor_obstacle_fusion"
+ # 复制 road_boundary_perception_track 模块目录
+ cp -a "$MODULE_DIR" "$tmp_dir/$target_dir/road_boundary_perception_track"
  
  # ----------------------------
  # 打包成 tar.gz 压缩包
  # ----------------------------
- pkg_name="msf_a1000_bin_v${version}_${current_datetime}.tar.gz"
+ pkg_name="rbt_a1000_bin_v${version}_${current_datetime}.tar.gz"
  pkg_path="$tmp_dir/$pkg_name"
  echo "正在打包为：$pkg_name"
```

```diff
  
  # JFrog 配置
- JFROG_URL="http://10.0.33.179:8082/artifactory/P_ADAS_HPP/hfr/wuling/haf_msf"
+ JFROG_URL="http://10.0.33.179:8082/artifactory/P_ADAS_HPP/hfr/wuling/haf_rbt"
  API_KEY="AKCpBu4SwHkfMQTtWhorf4XW9CPz3FKzSZSMHVFtPFL7jcmKZoSBezmxS4Cwt89hAwpBF5sx8"  # 请替换为实际的 API Key
  
```

---

脚本已成功更新，主要修改内容如下：

1. 将库目录路径从 `multi_sensor_obstacle_fusion` 修改为 `road_boundary_perception_track`
2. 将模块目录路径从 `multi_sensor_obstacle_fusion` 修改为 `road_boundary_perception_track`
3. 修改目标目录名前缀从 `msf` 改为 `rbt`
4. 修改打包文件名前缀从 `msf` 改为 `rbt`
5. 更新 JFrog 上传路径末端从 `haf_msf` 改为 `haf_rbt`
6. 修复了目标目录名中缺少的花括号，确保变量正确解析

现在，脚本已经完全适配 road_boundary_perception_track 模块，可以用于部署该模块。脚本会自动从库文件中提取版本号，创建适当的目录结构，复制所需文件，并将其打包为 tar.gz 格式，最后上传到 JFrog 仓库。

---

