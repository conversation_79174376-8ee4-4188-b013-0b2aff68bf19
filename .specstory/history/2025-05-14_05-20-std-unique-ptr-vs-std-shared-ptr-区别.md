<!-- Generated by SpecStory -->

# std::unique_ptr vs std::shared_ptr 区别 (2025-05-14 13:20:47)

_**User**_

这里使用 std::unique_ptr 和 std::shared_ptr的区别是什么

---

_**Assistant**_

非常感谢你的提问！你问到在如下代码中：

```cpp
std::unique_ptr<RoadTracker> p_road_tracker;
std::shared_ptr<PBI::LocalizationEstimate> location_ptr;
std::shared_ptr<PBI::RoadInfoArray> road_array_ptr;
```

`std::unique_ptr` 和 `std::shared_ptr` 的区别是什么？下面我会详细解释，并结合你的代码场景进行说明。

---

## 主要区别

### 1. 所有权模型

- **std::unique_ptr**
  - 独占所有权（unique ownership）：同一时间只能有一个 `unique_ptr` 指向某个对象。
  - 不能拷贝，只能移动（move）。
  - 适合用于“一个对象只被一个地方管理”的场景。
  - 当 `unique_ptr` 被销毁时，所管理的对象也会被销毁。

- **std::shared_ptr**
  - 共享所有权（shared ownership）：可以有多个 `shared_ptr` 指向同一个对象。
  - 内部维护一个引用计数，最后一个 `shared_ptr` 被销毁时，对象才会被释放。
  - 适合用于“多个地方需要访问和管理同一个对象”的场景。

### 2. 性能

- `unique_ptr` 更轻量，没有引用计数，性能更高。
- `shared_ptr` 由于有引用计数，稍微有点性能开销。

### 3. 语义

- `unique_ptr` 明确表达“此对象只归我所有”。
- `shared_ptr` 明确表达“此对象可以被多个地方共享”。

---

## 结合你的代码场景

- `std::unique_ptr<RoadTracker> p_road_tracker;`
  - 说明 `RoadTracker` 只归 `RoadBoundaryPerception` 这个类所有，生命周期由它独自管理，外部不会共享。
- `std::shared_ptr<PBI::LocalizationEstimate> location_ptr;`
- `std::shared_ptr<PBI::RoadInfoArray> road_array_ptr;`
  - 说明 `LocalizationEstimate` 和 `RoadInfoArray` 这两个对象，可能会被多个地方（比如其他组件、模块）共享和访问，所以用 `shared_ptr`。

---

## 具体举例

假设你有如下代码：

```cpp
std::unique_ptr<A> a1 = std::make_unique<A>();
std::unique_ptr<A> a2 = a1; // 错误，不能拷贝
std::unique_ptr<A> a3 = std::move(a1); // 正确，a1 失去所有权，a3 获得

std::shared_ptr<B> b1 = std::make_shared<B>();
std::shared_ptr<B> b2 = b1; // 正确，b1 和 b2 共享所有权
```

---

## 适用场景总结

- 只需要一个地方管理对象，用 `unique_ptr`。
- 需要多个地方共享对象，用 `shared_ptr`。

---

## 实现步骤

1. 判断对象是否需要被多个地方共享。
2. 只被一个类/模块管理，用 `unique_ptr`。
3. 需要跨模块/跨线程共享，用 `shared_ptr`。

---

## 备选方案

1. **std::weak_ptr**：如果担心 `shared_ptr` 循环引用导致内存泄漏，可以用 `weak_ptr` 弱引用。
2. **裸指针**：只做观察，不管理生命周期，但容易出错，不推荐。

---

## 量化成功标准

- 代码能否正确编译、运行，无内存泄漏。
- 对象生命周期是否符合预期（如析构函数被正确调用）。
- 资源管理是否高效（如无不必要的引用计数开销）。

---

## 结论

- `unique_ptr`：独占，轻量，适合单一所有权。
- `shared_ptr`：共享，适合多方共同管理。

如果你有更具体的使用场景或疑问，欢迎补充说明，我可以进一步为你分析！

---

**你需要更详细的底层实现原理，还是更关注实际用法和设计建议？**  
如果需要快速答案或详细分析，也请告知！

---

_**User**_

为了适配 @road_tracker.cpp 的 RoadTracker::Track 接口，添加 `HBI2PBI` 函数进行转换，可以认为是 @PBI2HBI(const PBI::RoadInfoArray &, HBI::CurbInfoArray &) 的逆操作，接口定义 参考 @perception_type.h  和 @road_info.h 。请补充 HBI2PBI的代码实现，并将 road_array_ptr 改成合适的变量声明

---

_**Assistant**_

非常感谢你的详细说明和上下文信息！  
你的需求是：

- 需要实现 `HBI2PBI` 函数，实现从 `HBI::CurbInfoArray` 转换为 `PBI::RoadInfoArray`，其功能为 `PBI2HBI` 的逆操作。
- 需要将 `road_array_ptr` 的声明方式调整为合适的类型（因为 `HBI2PBI` 需要输出 `PBI::RoadInfoArray`，而不是 `shared_ptr`）。

---

## 1. 需求澄清与意图确认

- **场景**：你在做道路边界感知的跟踪，需要将感知层的路沿（`HBI::CurbInfoArray`）转换为跟踪层的道路信息（`PBI::RoadInfoArray`），以便后续跟踪处理。
- **目标**：实现 `HBI2PBI`，并确保变量声明与接口匹配。
- **约束**：类型定义参考 `perception_type.h` 和 `road_info.h`，转换逻辑需与 `PBI2HBI` 对应。

---

## 2. 方案设计

### 2.1 类型分析

- `HBI::CurbInfoArray` 结构体有2个 `CurbInfo`（0-L, 1-R）。
- `PBI::RoadInfoArray` 结构体有6个 `RoadInfo`（0-L；1-R；2-LL；3-RR；4-L；5-R）。
- 你只需将 `CurbInfoArray` 的 0/1 路沿信息，映射到 `RoadInfoArray` 的 0/1（或4/5，视具体需求）即可。

### 2.2 变量声明建议

- 如果只在本地用，建议直接用 `PBI::RoadInfoArray` 对象而非 `shared_ptr`。
- 如果需要跨函数/线程共享，才用 `std::shared_ptr<PBI::RoadInfoArray>`。

---

## 3. 代码实现

### 3.1 HBI2PBI 函数实现

假设你只需要将 `CurbInfoArray` 的 0/1 映射到 `RoadInfoArray` 的 0/1（L/R），其余 RoadInfo 清零：

```cpp
// HBI2PBI: 将 HBI::CurbInfoArray 转换为 PBI::RoadInfoArray
inline void HBI2PBI(const HBI::CurbInfoArray& curb_info_array, PBI::RoadInfoArray& road_info_array) {
    road_info_array.timestamp_us = curb_info_array.timestamp_us;
    // 只处理前两个（L/R）路沿
    for (int i = 0; i < 2; ++i) {
        const auto& curb = curb_info_array.curb_info[i];
        auto& road = road_info_array.road[i];
        road.c0 = curb.c0;
        road.c1 = curb.c1;
        road.c2 = curb.c2;
        road.c3 = curb.c3;
        road.road_width = curb.road_width;
        road.view_range_start = curb.start;
        road.view_range_end = curb.end;
        road.confidence = curb.confidence;
        road.valid = curb.valid;
        road.is_predicted = curb.is_predicted;
        // 其余字段如 track_id/guardrail 可根据需要补充
    }
    // 其余 RoadInfo 清零
    for (int i = 2; i < 6; ++i) {
        road_info_array.road[i] = PBI::RoadInfo();
    }
}
```

如需将 curb_info_array 的 0/1 映射到 road_info_array 的 4/5（护栏），只需调整下标即可。

---

### 3.2 变量声明建议

如果你只在本地用，建议这样声明：

```cpp
PBI::RoadInfoArray road_array;
HBI2PBI(curb_info_array, road_array);
```

如果确实需要指针（如接口要求），则：

```cpp
auto road_array_ptr = std::make_shared<PBI::RoadInfoArray>();
HBI2PBI(curb_info_array, *road_array_ptr);
```

---

## 4. 备选方案

1. **映射到 4/5（护栏）**：如需将 curb_info 映射到 road_info_array.road[4]/[5]，只需调整 for 循环下标。
2. **深拷贝/浅拷贝**：如有复杂成员（如 point[]），可补充拷贝逻辑。

---

## 5. 量化成功标准

- 转换后 `road_info_array` 的 L/R 路沿信息与 `curb_info_array` 一致。
- 其余 `RoadInfo` 字段被清零或初始化。
- 代码可编译、单元测试通过。

---

## 6. 代码示例（完整）

```cpp
// 头文件声明
void HBI2PBI(const HBI::CurbInfoArray& curb_info_array, PBI::RoadInfoArray& road_info_array);

// 实现
void HBI2PBI(const HBI::CurbInfoArray& curb_info_array, PBI::RoadInfoArray& road_info_array) {
    road_info_array.timestamp_us = curb_info_array.timestamp_us;
    for (int i = 0; i < 2; ++i) {
        const auto& curb = curb_info_array.curb_info[i];
        auto& road = road_info_array.road[i];
        road.c0 = curb.c0;
        road.c1 = curb.c1;
        road.c2 = curb.c2;
        road.c3 = curb.c3;
        road.road_width = curb.road_width;
        road.view_range_start = curb.start;
        road.view_range_end = curb.end;
        road.confidence = curb.confidence;
        road.valid = curb.valid;
        road.is_predicted = curb.is_predicted;
        // 其他字段如 track_id/guardrail 可根据需要补充
    }
    for (int i = 2; i < 6; ++i) {
        road_info_array.road[i] = PBI::RoadInfo();
    }
}
```

---

## 7. 结论与确认

- 你可以根据实际需求调整映射下标和字段。
- 请确认你需要的映射关系（L/R 还是护栏），如需调整请告知。
- 变量声明建议用值类型，除非有特殊需求。

---

### 你是否需要将 curb_info 映射到 road_info_array 的 0/1（L/R）还是 4/5（护栏）？  
如需进一步定制，请补充说明！  
如需单元测试样例，也可告知！

---

**请确认上述方案是否满足你的需求，或提出具体调整意见。**

---

