<!-- Generated by SpecStory -->

# 更新脚本以解决执行报错 (2025-07-24 07:13Z)

_**User**_

脚本执行报错如截图所示，其中传入的参数 {install_dir} 是 install_148， install_148下的目录结构为：
install_148/arm64_a1000b/lib 和 install_148/arm64_a1000b/modules ，分别用于将 so库复制到的目的地和 lidar_curb_perception_segment 文件夹复制/替换的目的地。

请更新脚本以排除运行报错（L224）和 find 没有找到文件和目录的问题 



---

_**Assistant**_



---

Read file: lcs_deploy.sh

---



---

Read file: lcs_deploy.sh

---



---

Read file: lcs_deploy.sh

---



---

<details>
            <summary>Grep search for "echo "" • **9** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/run_em.sh` | L8 | `echo "$ARCH"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/run_em.sh` | L14 | `#    echo "match"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/run_em.sh` | L29 | `echo "$config_name file not exist"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/deploy_RBT.sh` | L17 | `echo "未找到符合要求的库文件 (*.so.*)！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/deploy_RBT.sh` | L24 | `echo "检测到版本号：$version"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/deploy_RBT.sh` | L30 | `echo "当前日期时间：$current_datetime"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/deploy_RBT.sh` | L39 | `echo "创建临时目录：$tmp_dir"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/deploy_RBT.sh` | L53 | `echo "正在打包为：$pkg_name"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/deploy_RBT.sh` | L62 | `echo "临时目录已删除。"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/deploy_RBT.sh` | L64 | `echo "打包完成，生成压缩包：$pkg_name"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/deploy_RBT.sh` | L73 | `echo "开始上传到 JFrog"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/jfrog_lcs.sh` | L17 | `echo "未找到符合要求的库文件 (*.so.*)！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/jfrog_lcs.sh` | L24 | `echo "检测到版本号：$version"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/jfrog_lcs.sh` | L30 | `echo "当前日期时间：$current_datetime"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/jfrog_lcs.sh` | L39 | `echo "创建临时目录：$tmp_dir"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/jfrog_lcs.sh` | L53 | `echo "正在打包为：$pkg_name"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/jfrog_lcs.sh` | L62 | `echo "临时目录已删除。"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/jfrog_lcs.sh` | L64 | `echo "打包完成，生成压缩包：$pkg_name"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/jfrog_lcs.sh` | L73 | `echo "开始上传到 JFrog"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/stop_em_all.sh` | L11 | `echo "$ARCH"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/stop_em_all.sh` | L24 | `echo "Stopping application: $1 (PID=$killedpid)"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/stop_em_all.sh` | L28 | `echo "No active process found for application: $1"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/stop_em_all.sh` | L39 | `echo "Stopping application: $2 in session $1 (PID=$killedpid)"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/stop_em_all.sh` | L43 | `echo "No active process found for application: $2 in session $1"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/stop_em_all.sh` | L59 | `echo "Stopping applications in session PID: $1"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/stop_em_all.sh` | L78 | `echo "No Docker container processes found with ID: $dockerID"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/stop_em_all.sh` | L84 | `echo "Stopping heart service"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/stop_em_all.sh` | L100 | `echo "Verifying and force-killing remaining processes"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/stop_em_all.sh` | L104 | `echo "Force-killing process: $pid"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/stop_em_all.sh` | L107 | `echo "Process $pid no longer exists"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L41 | `echo "ready to create tmp file : ${CODE_TMP_PATH}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L46 | `echo "Fault : failed to create :[ ${CODE_TMP_PATH} ], error code : ${ret}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L62 | `echo "=== will use $REPO"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L66 | `echo "Fault : failed to repo init code :[ ${REPO} ${GIT_SSH_URL}${PROJECT_MANIFEST} ], error code : ${ret}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L72 | `echo "Fault : failed to repo synccd code :[ ${REPO} ${GIT_SSH_URL}${PROJECT_MANIFEST} ], error code : ${ret}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L83 | `echo "start x86 docker"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L87 | `echo "start a1000 docker"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L91 | `echo "start yocto docker"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L95 | `echo "Unsupported target arch"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L100 | `echo "Fault : failed to start docker in  [ ${PROJECT_NAME} ] in docker, error code : ${ret}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L110 | `echo "Fault : failed to compile  [ ${PROJECT_NAME} ] in docker, error code : ${ret}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L119 | `echo "packing sdk"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L136 | `echo "Fault : failed to copy  [ ${PROJECT_NAME}_${PROJECT_VERSION} ] , error code : ${ret}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L138 | `echo "Success to copy the file to ${PROJECT_DESTPATH}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L151 | `echo "Multiple option ${opt} specified, only the last one will take effect."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L160 | `echo "Multiple option ${opt} specified, only the last one will take effect."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L169 | `echo "Multiple option ${opt} specified, only the last one will take effect."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L177 | `echo "Multiple option ${opt} specified, only the last one will take effect."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L184 | `echo "Error : the directory does not exist, dir : ${1}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L188 | `echo "Multiple option ${opt} specified, only the last one will take effect."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L195 | `echo "Multiple option ${opt} specified, only the last one will take effect."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L217 | `echo "Unknown option: ${opt}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L228 | `echo "remove sdk tmp dir"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L232 | `echo "remove hfr tmp dir"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L242 | `echo "Success to call ${2}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/project_compile.sh` | L245 | `echo "Fault : in [$2], ret = ${ret}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/run_gdb.sh` | L2 | `echo "$path"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/build_function.sh` | L7 | `echo "script/CMakeLists.txt not exist"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/build_function.sh` | L12 | `echo "run: rm -rf build, please waiting..."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/build_function.sh` | L17 | `echo "run: cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=release"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/build_function.sh` | L20 | `echo "run: cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=debug"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/build_function.sh` | L28 | `echo "env is not build, build first, dev"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/build_function.sh` | L32 | `echo "run: cmake --build build --target install -j32"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/build_function.sh` | L45 | `echo "install dir is not exist"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/script/build_function.sh` | L55 | `echo "script dir is not exist"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L28 | `echo "错误：缺少必要参数！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L29 | `echo ""` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L30 | `echo "用法: $0 <tar_package> [install_dir]"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L31 | `echo ""` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L32 | `echo "参数说明："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L33 | `echo "  tar_package    待部署的tar.gz压缩包文件路径"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L34 | `echo "  install_dir    域控端软件部署目录名（可选，默认为install）"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L35 | `echo "                 通常为install_VX.XX格式，例如：install_V1.21、install_V2.00 等"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L36 | `echo ""` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L37 | `echo "示例："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L38 | `echo "  $0 lcs_a1000_bin_v1.21.1.3_12081430.tar.gz"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L39 | `echo "  $0 lcs_a1000_bin_v1.21.1.3_12081430.tar.gz install_V1.21"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L40 | `echo "  $0 lcs_a1000_bin_v2.00.0.1_12081500.tar.gz install_V2.00"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L54 | `echo "错误：指定的tar包文件不存在: $pkg_name"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L58 | `echo "使用tar包: $pkg_name"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L59 | `echo "安装目录: $install_dir"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L65 | `echo "目标目录名: $target_dir"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L78 | `echo "完整安装目录为: $full_install_dir"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L86 | `echo "检测到sshpass工具，将使用自动密码认证"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L88 | `echo "未检测到sshpass工具，将使用交互式密码认证"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L89 | `echo "提示：您可以通过以下命令安装sshpass以实现自动认证："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L90 | `echo "  Ubuntu/Debian: sudo apt-get install sshpass"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L91 | `echo "  CentOS/RHEL:   sudo yum install sshpass"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L92 | `echo ""` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L98 | `echo "正在上传文件到域控端..."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L99 | `echo "目标地址: ${domain_controller_user}@${domain_controller_ip}:${full_install_dir}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L104 | `echo "使用自动密码认证上传文件..."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L109 | `echo "请输入域控端密码进行认证（密码：hasco123）："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L116 | `echo "文件上传成功！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L117 | `echo "上传的文件: $pkg_name"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L118 | `echo "上传到: ${domain_controller_user}@${domain_controller_ip}:${full_install_dir}/"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L121 | `echo "正在域控端解压文件..."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L130 | `echo "请再次输入域控端密码进行解压操作："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L137 | `echo "文件解压成功！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L142 | `echo "正在执行文件部署操作..."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L28 | `echo "错误：缺少必要参数！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L29 | `echo ""` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L30 | `echo "用法: $0 <tar_package> [install_dir]"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L31 | `echo ""` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L32 | `echo "参数说明："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L33 | `echo "  tar_package    待部署的tar.gz压缩包文件路径"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L34 | `echo "  install_dir    域控端软件部署目录名（可选，默认为install）"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L35 | `echo "                 通常为install_VX.XX格式，例如：install_V1.21、install_V2.00 等"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L36 | `echo ""` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L37 | `echo "示例："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L38 | `echo "  $0 lcs_a1000_bin_v1.21.1.3_12081430.tar.gz"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L39 | `echo "  $0 lcs_a1000_bin_v1.21.1.3_12081430.tar.gz install_V1.21"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L40 | `echo "  $0 lcs_a1000_bin_v2.00.0.1_12081500.tar.gz install_V2.00"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L54 | `echo "错误：指定的tar包文件不存在: $pkg_name"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L58 | `echo "使用tar包: $pkg_name"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L59 | `echo "安装目录: $install_dir"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L65 | `echo "目标目录名: $target_dir"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L78 | `echo "完整安装目录为: $full_install_dir"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L86 | `echo "检测到sshpass工具，将使用自动密码认证"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L88 | `echo "未检测到sshpass工具，将使用交互式密码认证"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L89 | `echo "提示：您可以通过以下命令安装sshpass以实现自动认证："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L90 | `echo "  Ubuntu/Debian: sudo apt-get install sshpass"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L91 | `echo "  CentOS/RHEL:   sudo yum install sshpass"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L92 | `echo ""` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L98 | `echo "正在上传文件到域控端..."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L99 | `echo "目标地址: ${domain_controller_user}@${domain_controller_ip}:${full_install_dir}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L104 | `echo "使用自动密码认证上传文件..."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L109 | `echo "请输入域控端密码进行认证（密码：hasco123）："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L116 | `echo "文件上传成功！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L117 | `echo "上传的文件: $pkg_name"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L118 | `echo "上传到: ${domain_controller_user}@${domain_controller_ip}:${full_install_dir}/"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L121 | `echo "正在域控端解压文件..."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L130 | `echo "请再次输入域控端密码进行解压操作："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L137 | `echo "文件解压成功！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L142 | `echo "正在执行文件部署操作..."` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L223 | `echo "  共找到 $src_so_count 个so相关文件"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L307 | `echo "请输入域控端密码进行文件部署操作："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L313 | `echo "文件部署成功！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L314 | `echo "部署完成！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L315 | `echo ""` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L316 | `echo "部署摘要："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L317 | `echo "- 压缩包: $pkg_name"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L318 | `echo "- 目标位置: ${domain_controller_user}@${domain_controller_ip}:${full_install_dir}/"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L319 | `echo "- 认证方式: $([ "$use_sshpass" = true ] && echo "自动密码认证" || echo "交互式密码认证")"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L320 | `echo "- 库文件已更新至: ${full_install_dir}/lib/"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L321 | `echo "- 模块已替换至: ${full_install_dir}/modules/lidar_curb_perception_segment/"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L323 | `echo "错误：文件部署失败！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L324 | `echo "请手动登录域控端检查部署状态或执行以下操作："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L325 | `echo "1. 检查解压后的目录结构"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L326 | `echo "2. 手动复制库文件到 ${full_install_dir}/lib/"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L327 | `echo "3. 手动替换模块到 ${full_install_dir}/modules/lidar_curb_perception_segment/"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L331 | `echo "警告：文件解压失败，请手动检查！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L332 | `echo "您可以手动登录域控端执行以下命令："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L333 | `echo "  ssh ${domain_controller_user}@${domain_controller_ip}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L334 | `echo "  cd ${full_install_dir}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L335 | `echo "  tar -xzf $pkg_name"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L339 | `echo "错误：文件上传失败！"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L340 | `echo "请检查："` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L341 | `echo "1. 网络连接是否正常"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L342 | `echo "2. 域控端IP地址是否正确: ${domain_controller_ip}"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L343 | `echo "3. 用户名和密码是否正确"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/lcs_deploy.sh` | L344 | `echo "4. 目标目录是否存在: ${full_install_dir}"` |

</details>

---



---

```diff
                  # 统计so库文件数量
                  src_so_count=$(find lidar_curb_perception_segment/bin/ -name "*.so*" | wc -l)
-                 echo "  共找到 $src_so_count 个so相关文件"
+                 echo \"  共找到 \$src_so_count 个so相关文件\"
              else
                  echo '警告：源模块未包含 bin 目录'
```

---

已修复 `deploy_commands` 字符串中的未转义双引号问题：

1. 原来 `echo "  共找到 $src_so_count 个so相关文件"` 这一行的双引号导致多行字符串提前结束，脚本后续内容被本地 shell 直接执行，引发  
   • `find lidar_curb_perception_segment/bin/` 目录不存在报错  
   • 第 224 行 “共找到: 未找到命令” 报错

2. 现已改为  
   `echo \"  共找到 \$src_so_count 个so相关文件\"`  
   通过转义双引号与变量，确保整段命令只在远端执行，避免本地误解析。

请重新运行脚本验证，理论上应不再出现 L224 与 find 路径不存在的错误。如仍有问题请随时反馈！

---

_**User**_

检查这两个find语句错误的风险

---

_**Assistant**_



---

