<!-- Generated by SpecStory -->

# PostProcessLidarObstacle function analysis (2025-06-25 01:57Z)

_**User**_

@PostProcessLidarObstacle(base::LidarFrame *) 函数中有没有涉及到路沿检测相关的处理逻辑，如果有，用途是什么？

---

_**Assistant**_

我来分析一下 `PostProcessLidarObstacle` 函数中是否涉及路沿检测相关的处理逻辑。

---

<details>
            <summary>Searched codebase "路沿检测 curb detection PostProcessLidarObstacle" • **15** results</summary>
        
| File | Lines |
|------|-------|
| `resource/YX_G21A/transform_matrix.json` | L1-75 |
| `resource/HYNOVA/transform_matrix.json` | L1-74 |
| `resource/HYNOVA/transform_matrix.json` | L74-93 |
| `resource/MarvelR/transform_matrix.json` | L1-74 |
| `resource/WZ3200/transform_matrix.json` | L1-75 |
| `resource/YX_G21A/transform_matrix_default.json` | L1-74 |
| `resource/HYNOVA/transform_matrix_default.json` | L74-93 |
| `resource/FR07_PRO/transform_matrix.json` | L1-74 |
| `resource/HYNOVA/transform_matrix_default.json` | L1-74 |
| `resource/SGMW_SS2ND/transform_matrix.json` | L74-93 |
| `resource/MarvelX/transform_matrix.json` | L1-74 |
| `resource/MarvelR/transform_matrix.json` | L74-93 |
| `resource/MarvelX/transform_matrix.json` | L74-93 |
| `resource/FR07_PRO/transform_matrix.json` | L74-93 |
| `resource/SGMW_SS2ND/transform_matrix.json` | L1-74 |

</details>

---

<details>
            <summary>Grep search for "curb|路沿|Curb" • **5** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L47 | `KalmanFilterCurb3DofInit();  // 初始化卡尔曼滤波器` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L153 | `/* calculate the similarity of the curb coefficients */` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L154 | `double RoadTracker::CalculateCurbCoeffiSimilarity(const PBI::RoadInfo *det, const PBI::RoadInfo *track) {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L185 | `LOG(INFO) << "curb coeffi match score: " << coeffi_consist_score;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L190 | `/* calculate the similarity of the curb point cloud */` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L191 | `double RoadTracker::CalculateCurbPCLSimilarity(const PBI::RoadInfo *det, const PBI::RoadInfo *track) {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L199 | `LOG(WARN) << "no overlap between detected and tracked curb, curb point match score = 0";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L214 | `LOG(WARN) << "not enough points in overlap area, curb point match score = 0";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L233 | `LOG(INFO) << "curb point match score: " << point_consist_score;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L238 | `/* calculate the similarity of the curb location */` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L239 | `double RoadTracker::CalculateCurbLocationSimilarity(const PBI::RoadInfo *det, const PBI::RoadInfo *track) {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L241 | `// 路沿重合` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L249 | `LOG(INFO) << "curb location match score: " << location_consist_score;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L254 | `CurbMatchResult RoadTracker::CurbMatch(const PBI::RoadInfo *det, const PBI::RoadInfo *track) {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L256 | `CurbMatchResult match_result;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L260 | `match_result.match_detail = "Curb matched";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L266 | `match_result.match_detail = "Detected curb is invalid";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L274 | `match_result.match_detail = "detected curb point number is much less than tracked";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L282 | `double coeffi_consist_score   = CalculateCurbCoeffiSimilarity(det, track);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L283 | `double point_consist_score    = CalculateCurbPCLSimilarity(det, track);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L284 | `double location_consist_score = CalculateCurbLocationSimilarity(det, track);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L305 | `LOG(INFO) << "CurbMatch result: " << match_result.match_detail` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L313 | `* @brief  determine if the detected curb matches the tracked curb` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L314 | `* @param[in] det - pointer to the detected curb` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L315 | `* @param[in] track - pointer to the tracked curb` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L320 | `bool RoadTracker::IsMatchedCurb(const PBI::RoadInfo *det, const PBI::RoadInfo *track) {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L321 | `CurbMatchResult match_result = CurbMatch(det, track);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L322 | `LOG(INFO) << "CurbMatch result: " << match_result.match_detail;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L480 | `void RoadTracker::KalmanFilterCurb3DofInit() {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L494 | `kalman_filter_curb3dof_.set_process_noise(process_noise(0), process_noise(1), process_noise(2));` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L503 | `kalman_filter_curb3dof_.set_variance(variance);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L507 | `kalman_filter_curb3dof_.set_measurement_noise(measure_noise);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L510 | `kalman_filter_curb3dof_.Init(initial_state, variance);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L515 | `<< kalman_filter_curb3dof_.get_process_noise()(0,0) << ", "` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L516 | `<< kalman_filter_curb3dof_.get_process_noise()(1,1) << ", "` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L517 | `<< kalman_filter_curb3dof_.get_process_noise()(2,2) << "]";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L518 | `LOG(INFO) << "Measurement noise R: " << kalman_filter_curb3dof_.get_measurement_noise();` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L520 | `<< kalman_filter_curb3dof_.get_variance()(0,0) << ", "` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L521 | `<< kalman_filter_curb3dof_.get_variance()(1,1) << ", "` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L522 | `<< kalman_filter_curb3dof_.get_variance()(2,2) << "]";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L538 | `kalman_filter_curb3dof_.set_process_noise(c0_noise, c1_noise, c2_noise);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L548 | `kalman_filter_curb3dof_.set_variance(variance);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L552 | `kalman_filter_curb3dof_.set_measurement_noise(measure_noise);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L555 | `kalman_filter_curb3dof_.Init(initial_state, variance);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L567 | `kalman_filter_curb3dof_.Predict(delta_x);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L601 | `kalman_filter_curb3dof_.BatchUpdate(final_measurements);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L608 | `Eigen::Vector3d updated_state = kalman_filter_curb3dof_.get_state();` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L633 | `Eigen::Matrix3d variance = kalman_filter_curb3dof_.get_variance();` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L645 | `LOG(ERROR) << "pointer to curb detect or track is nullptr";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L656 | `LOG(INFO) << "detected curb is invalid, skip update";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L663 | `LOG(INFO) << "init tracked curb with detected curb";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L718 | `if (IsMatchedCurb(right_rbd, right_rbt)) {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L720 | `LOG(INFO) << "associate existing detected curb, update tracked curb with it";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L725 | `LOG(INFO) << "received new detected curb, init tracked curb with it";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp` | L786 | `Eigen::Vector3d state = kalman_filter_curb3dof_.get_state();` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L59 | `const HBI::CurbInfoArray& curb_seg_array,` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L60 | `const HBI::CurbInfoArray& curb_trk_array)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L70 | `LOG(INFO) << "rbt module write curb seg data with size: " << sizeof(curb_seg_array) << " bytes";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L71 | `file->write(reinterpret_cast<const char*>(&curb_seg_array), sizeof(curb_seg_array));` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L73 | `LOG(INFO) << "rbt module write curb trk data with size: " << sizeof(curb_trk_array) << " bytes";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L74 | `file->write(reinterpret_cast<const char*>(&curb_trk_array), sizeof(curb_trk_array));` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L422 | `void HBI2PBI(const HBI::CurbInfoArray* curb_info_array, PBI::RoadInfoArray& road_info_array) {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L423 | `road_info_array.timestamp_us = curb_info_array->timestamp_us;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L425 | `const auto& curb = curb_info_array->curb_info[i];` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L427 | `road.c0 = curb.c0;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L428 | `road.c1 = curb.c1;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L429 | `road.c2 = curb.c2;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L430 | `road.c3 = curb.c3;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L431 | `road.road_width = curb.road_width;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L432 | `road.view_range_start = curb.start;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L433 | `road.view_range_end = curb.end;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L434 | `road.confidence = curb.confidence;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L435 | `road.valid = curb.valid;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L436 | `road.is_predicted = curb.is_predicted;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L437 | `road.track_id = curb.id;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L438 | `road.guardrail.num = curb.point_num;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L439 | `for (int j = 0; j < curb.point_num; ++j) {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L440 | `road.guardrail.point[j].x = curb.point[j].x;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L441 | `road.guardrail.point[j].y = curb.point[j].y;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L442 | `road.guardrail.point[j].z = curb.point[j].z;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L443 | `road.guardrail.point[j].intensity = curb.point[j].intensity;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L444 | `road.guardrail.point[j].timestamp_us = curb.point[j].timestamp_us;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L448 | `// info: 清扫车贴边扫场景，最多输出两条路沿边线，数组后面赋值为0` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L454 | `void PBI2HBI(const PBI::RoadInfoArray &tracked_roads, HBI::CurbInfoArray &tracked_lines) {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L458 | `auto &line = tracked_lines.curb_info[idx];` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L489 | `subscribe_data_warehouse<HBI::CurbInfoArray>("HBI::CurbInfoArraySegment");  /* 从激光路沿检测模块(QX)订阅路沿信息 */` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L497 | `LOG(INFO) << "global curb track list init successfully";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L574 | `// 订阅路沿检测输出的消息 -- "HBI::CurbInfoArraySegment"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L575 | `HBI::CurbInfoArray *curb_seg_array = get_data_from_warehouse<HBI::CurbInfoArray>("HBI::CurbInfoArraySegment", ret_size);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L576 | `if (curb_seg_array == nullptr) {` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L577 | `LOG(ERROR) << "HBI::CurbInfoArray == nullptr";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L581 | `double curb_timestamp_s = static_cast<double>(curb_seg_array[latest_index].timestamp_us) * 1e-6;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L582 | `if (std::abs(curb_timestamp_s - last_curb_timestamp_s) < 0.001) {  // <1ms` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L583 | `// LOG(ERROR) << "filter the curb frame at timestamp:" << std::to_string(curb_timestamp_s)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L584 | `//           << " with last timestamp:" << std::to_string(last_curb_timestamp_s);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L597 | `double time_diff = std::abs(static_cast<double>(curb_seg_array[latest_index].timestamp_us) - static_cast<double>(localization[i].hbi_header.timestamp_us));` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L608 | `LOG(INFO) << "received curb data and localization data";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L611 | `// 将curb_info_array转换为road_array_ptr` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L613 | `HBI2PBI(&curb_seg_array[latest_index], detect_road_array);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L652 | `PBI2HBI(tracked_roads, curb_trk_array);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L654 | `LOG(INFO) << "publish tracked curb data, "` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L655 | `<< "left c3 = " << curb_trk_array.curb_info[0].c3` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L656 | `<< ", right c3 = " << curb_trk_array.curb_info[1].c3;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L658 | `put_data_in_warehouse("HBI::CurbInfoArrayTrack", curb_trk_array);  //vehicle coordinate system` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L661 | `std::string detect_file_name = std::to_string(curb_timestamp_s);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L665 | `std::string border_file_name = std::to_string(curb_timestamp_s);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L669 | `std::string track_file_name = std::to_string(curb_timestamp_s);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L676 | `last_curb_timestamp_s = curb_timestamp_s;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L679 | `save_data_to_file(bin_file_stream, localization[best_match_index], curb_seg_array[latest_index], curb_trk_array);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L684 | `std::string localization_file_name = std::to_string(curb_timestamp_s);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L689 | `memcpy(&curb_trk_array, &curb_seg_array[latest_index], sizeof(HBI::CurbInfoArray));` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L690 | `put_data_in_warehouse("HBI::CurbInfoArrayTrack", curb_trk_array);  //vehicle coordinate system` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L691 | `LOG(INFO) << "publish tracked curb data: "` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L692 | `<< "c0 = " << curb_trk_array.curb_info[1].c0` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L693 | `<< ", c1 = " << curb_trk_array.curb_info[1].c1` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L694 | `<< ", c2 = " << curb_trk_array.curb_info[1].c2` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp` | L695 | `<< ", c3 = " << curb_trk_array.curb_info[1].c3;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3886 | `else if (car_name_ == "WZ3200" || car_name_ == "WZ3200_CURB")` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L4191 | `std::shared_ptr<PBI::CurbInfoArray> curb_array_ptr = std::shared_ptr<PBI::CurbInfoArray>(new PBI::CurbInfoArray);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L4192 | `curb_array_ptr->timestamp_us = cloud_ptr->timestamp_us;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L4193 | `curb_array_ptr->curb_info[0].valid = false;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L4194 | `curb_array_ptr->curb_info[1].valid = false;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L4198 | `los_ptr_->ProcessCurb(segment_options, cloud_ptr.get(), location_ptr.get(), curb_array_ptr.get(), hdmap_polygon_ptr.get());` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L4202 | `std::cout << "curb point_cloud_file_path: " << point_cloud_file_path << std::endl;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L4204 | `los_ptr_->SaveCurbMessageToPcd(curb_array_ptr.get(), point_cloud_file_path);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | Lundefined | `#include "curb_segmenter.hpp"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L25 | `bool CurbSegmenter::Init(const CurbSegmenterInitOptions& options)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L27 | `use_bev_image_extract_curb_ = options.use_bev_image_extract_curb;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L48 | `use_point_jump_extract_curb_ = options.use_point_jump_extract_curb;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L85 | `use_fuse_frames_curb_points_ = options.use_fuse_frames_curb_points;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L86 | `num_fuse_frames_curb_points_ = options.num_fuse_frames_curb_points;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L87 | `fuse_time_interval_curb_points_ = options.fuse_time_interval_curb_points;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L88 | `use_pose_euler_check_curb_points_ = options.use_pose_euler_check_curb_points;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L89 | `fuse_yaw_interval_curb_points_ = options.fuse_yaw_interval_curb_points;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L96 | `if (use_bev_image_extract_curb_)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L108 | `bool CurbSegmenter::Detect(const CurbSegmenterOptions& options, base::LidarFrame* frame)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L123 | `if (frame->candidate_curb_cloud == nullptr)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L125 | `frame->candidate_curb_cloud = std::shared_ptr<base::PointFCloud>(new base::PointFCloud);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L130 | `curb_points_cloud_local_ = std::shared_ptr<base::PointFCloud>(new base::PointFCloud);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L185 | `if (use_bev_image_extract_curb_)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L190 | `if (use_point_jump_extract_curb_)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L203 | `SearchJumpInPolarGrid(input_cloud, input_indices, curb_points_cloud_local_);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L208 | `base::PointFCloudPtr curb_points_after_filter;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L209 | `RoadBoundarySideDistFilter(frame, curb_points_cloud_local_, &curb_points_after_filter);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L210 | `curb_points_cloud_local_ = curb_points_after_filter;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L215 | `JumpPointsDownsampleFilter(curb_points_cloud_local_);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L220 | `base::PointFCloudPtr curb_points_after_sort;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L221 | `JumpPointsGrowthSearchSort(curb_points_cloud_local_, &curb_points_after_sort);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L222 | `curb_points_cloud_local_ = curb_points_after_sort;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L227 | `SmoothJumpPointsRefine(curb_points_cloud_local_);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L230 | `if (use_fuse_frames_curb_points_ && num_fuse_frames_curb_points_ > 1)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L232 | `while (!prev_world_curb_clouds_.empty() &&` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L233 | `(frame->timestamp - prev_world_curb_clouds_.front()->get_timestamp() > fuse_time_interval_curb_points_ ||` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L234 | `frame->timestamp - prev_world_curb_clouds_.front()->get_timestamp() < 1e-5 ||` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L235 | `(use_pose_euler_check_curb_points_ && std::abs(frame->yaw - prev_world_yaws_.front()) > fuse_yaw_interval_curb_points_)))` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L237 | `prev_world_curb_clouds_.pop_front();` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L241 | `FuseCurbCloud(curb_points_cloud_local_, prev_world_curb_clouds_);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L243 | `base::PointDCloudPtr cur_world_curb_cloud_ptr = std::make_shared<base::PointDCloud>();` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L244 | `cur_world_curb_cloud_ptr->reserve(curb_points_cloud_local_->size());` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L245 | `for (size_t i = 0; i < curb_points_cloud_local_->size(); ++i)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L247 | `if (curb_points_cloud_local_->points_beam_id(i) == -100)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L252 | `const base::PointF& pt = curb_points_cloud_local_->at(i);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L260 | `cur_world_curb_cloud_ptr->push_back(world_point);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L262 | `cur_world_curb_cloud_ptr->set_timestamp(frame->timestamp);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L264 | `while (static_cast<int>(prev_world_curb_clouds_.size()) >= num_fuse_frames_curb_points_ - 1)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L266 | `prev_world_curb_clouds_.pop_front();` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L269 | `prev_world_curb_clouds_.emplace_back(cur_world_curb_cloud_ptr);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L273 | `for (size_t i = 0; i < curb_points_cloud_local_->size(); i++)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L275 | `const base::PointF& pt = curb_points_cloud_local_->at(i);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L276 | `frame->candidate_curb_cloud->push_back(` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L281 | `static_cast<uint8_t>(base::LidarPointLabel::CURB_RIGHT));` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L289 | `SavePointCloudToPcd(frame->candidate_curb_cloud, cloud_file_path);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L306 | `void CurbSegmenter::FindRoadBoundaryLine(const base::LidarFrame* frame)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L572 | `bool CurbSegmenter::CalculateDistAndDirToSegs(const Eigen::Matrix<double, 3, 1> &pt,` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L643 | `void CurbSegmenter::SearchJumpInPolarGrid(const base::PointFCloudPtr in_cloud_ptr, const base::PointIndices& indices, base::PointFCloudPtr& jump_points)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L964 | `void CurbSegmenter::RoadBoundarySideDistFilter(const base::LidarFrame* frame, const base::PointFCloudPtr& in_cloud, base::PointFCloudPtr* out_cloud)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L1061 | `void CurbSegmenter::JumpPointsDownsampleFilter(base::PointFCloudPtr& in_cloud)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L1140 | `void CurbSegmenter::JumpPointsGrowthSearchSort(const base::PointFCloudPtr& in_cloud, base::PointFCloudPtr* out_cloud)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L1317 | `void CurbSegmenter::SmoothJumpPointsRefine(base::PointFCloudPtr& in_cloud)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L1476 | `void CurbSegmenter::FuseCurbCloud(base::PointFCloudPtr& in_cloud, const std::deque<base::PointDCloudPtr>& fuse_clouds)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L1535 | `void CurbSegmenter::GetDistanceAndDirPointToLine(const base::PointF& line_start_pt,` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L1584 | `bool CurbSegmenter::IsPointXYInPolygon2DXY(const base::PointD &point, const base::PointCloud<base::PointD> &polygon)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/curb_segmenter/curb_segmenter.cpp` | L1636 | `void CurbSegmenter::SavePointCloudToPcd(const base::PointFCloudPtr point_cloud_ptr, const std::string file_path)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L16 | `#include "curb_segmenter/curb_segmenter.hpp"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L19 | `#include "curb_builder/curb_builder.hpp"` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L73 | `lidar_segment_init_options.use_curb_segmenter = option_reader.get<bool>("use_curb_segmenter");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L76 | `lidar_segment_init_options.use_curb_builder = option_reader.get<bool>("use_curb_builder");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L329 | `lidar_segment_init_options.ground_detector_init_options.extract_curb_points = option_reader.get<bool>("ground_detector_extract_curb_points");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L330 | `lidar_segment_init_options.ground_detector_init_options.curb_height_min = option_reader.get<float>("ground_detector_curb_height_min");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L331 | `lidar_segment_init_options.ground_detector_init_options.curb_height_max = option_reader.get<float>("ground_detector_curb_height_max");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L640 | `if (lidar_segment_init_options.use_curb_segmenter)` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L642 | `lidar_segment_init_options.curb_segmenter_init_options.use_bev_image_extract_curb = option_reader.get<bool>("curb_segmenter_use_bev_image_extract_curb");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L643 | `lidar_segment_init_options.curb_segmenter_init_options.enable_nonground_cloud_downsample = option_reader.get<bool>("curb_segmenter_enable_nonground_cloud_downsample");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L644 | `lidar_segment_init_options.curb_segmenter_init_options.downsample_leaf_size_x = option_reader.get<float>("curb_segmenter_downsample_leaf_size_x");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L645 | `lidar_segment_init_options.curb_segmenter_init_options.downsample_leaf_size_y = option_reader.get<float>("curb_segmenter_downsample_leaf_size_y");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L646 | `lidar_segment_init_options.curb_segmenter_init_options.downsample_leaf_size_z = option_reader.get<float>("curb_segmenter_downsample_leaf_size_z");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L647 | `lidar_segment_init_options.curb_segmenter_init_options.downsample_leaf_growth_step = option_reader.get<float>("curb_segmenter_downsample_leaf_growth_step");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L648 | `lidar_segment_init_options.curb_segmenter_init_options.min_points_per_voxel = option_reader.get<unsigned int>("curb_segmenter_min_points_per_voxel");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L649 | `lidar_segment_init_options.curb_segmenter_init_options.downsampled_cloud_max_points = option_reader.get<unsigned int>("curb_segmenter_downsampled_cloud_max_points");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L650 | `lidar_segment_init_options.curb_segmenter_init_options.downsample_max_iteration_num = option_reader.get<unsigned int>("curb_segmenter_downsample_max_iteration_num");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L651 | `lidar_segment_init_options.curb_segmenter_init_options.semantic_range_min_x = option_reader.get<float>("curb_segmenter_semantic_range_min_x");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L652 | `lidar_segment_init_options.curb_segmenter_init_options.semantic_range_max_x = option_reader.get<float>("curb_segmenter_semantic_range_max_x");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L653 | `lidar_segment_init_options.curb_segmenter_init_options.semantic_range_min_y = option_reader.get<float>("curb_segmenter_semantic_range_min_y");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L654 | `lidar_segment_init_options.curb_segmenter_init_options.semantic_range_max_y = option_reader.get<float>("curb_segmenter_semantic_range_max_y");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L655 | `lidar_segment_init_options.curb_segmenter_init_options.semantic_img_resolution = option_reader.get<float>("curb_segmenter_semantic_img_resolution");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L656 | `lidar_segment_init_options.curb_segmenter_init_options.enable_find_image_edge_points = option_reader.get<bool>("curb_segmenter_enable_find_image_edge_points");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L657 | `lidar_segment_init_options.curb_segmenter_init_options.enable_find_image_edge_lines = option_reader.get<bool>("curb_segmenter_enable_find_image_edge_lines");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L658 | `lidar_segment_init_options.curb_segmenter_init_options.use_opencv_lsd_algorithm = option_reader.get<bool>("curb_segmenter_use_opencv_lsd_algorithm");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L659 | `lidar_segment_init_options.curb_segmenter_init_options.use_point_jump_extract_curb = option_reader.get<bool>("curb_segmenter_use_point_jump_extract_curb");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L660 | `lidar_segment_init_options.curb_segmenter_init_options.enable_calculate_road_boundary_line = option_reader.get<bool>("curb_segmenter_enable_calculate_road_boundary_line");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L661 | `lidar_segment_init_options.curb_segmenter_init_options.use_random_sample_nonroi_pts = option_reader.get<bool>("curb_segmenter_use_random_sample_nonroi_pts");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L662 | `lidar_segment_init_options.curb_segmenter_init_options.random_sample_nonroi_pts_num = option_reader.get<unsigned int>("curb_segmenter_random_sample_nonroi_pts_num");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L663 | `lidar_segment_init_options.curb_segmenter_init_options.road_boundary_line_dist_threshold = option_reader.get<double>("curb_segmenter_road_boundary_line_dist_threshold");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L664 | `lidar_segment_init_options.curb_segmenter_init_options.use_polar_grid_search_jump = option_reader.get<bool>("curb_segmenter_use_polar_grid_search_jump");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L665 | `lidar_segment_init_options.curb_segmenter_init_options.polar_grid_radius = option_reader.get<float>("curb_segmenter_polar_grid_radius");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L666 | `lidar_segment_init_options.curb_segmenter_init_options.polar_cell_radius_size = option_reader.get<float>("curb_segmenter_polar_cell_radius_size");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L667 | `lidar_segment_init_options.curb_segmenter_init_options.polar_cell_angle_size = option_reader.get<float>("curb_segmenter_polar_cell_angle_size");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L668 | `lidar_segment_init_options.curb_segmenter_init_options.polar_check_cell_valid = option_reader.get<bool>("curb_segmenter_polar_check_cell_valid");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L669 | `lidar_segment_init_options.curb_segmenter_init_options.polar_cell_min_num_points = option_reader.get<int>("curb_segmenter_polar_cell_min_num_points");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L670 | `lidar_segment_init_options.curb_segmenter_init_options.polar_grid_adjacent_min_z_diff = option_reader.get<float>("curb_segmenter_polar_grid_adjacent_min_z_diff");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L671 | `lidar_segment_init_options.curb_segmenter_init_options.polar_grid_adjacent_max_z_diff = option_reader.get<float>("curb_segmenter_polar_grid_adjacent_max_z_diff");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L672 | `lidar_segment_init_options.curb_segmenter_init_options.use_polar_grid_centroid_as_jump_point = option_reader.get<bool>("curb_segmenter_use_polar_grid_centroid_as_jump_point");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L673 | `lidar_segment_init_options.curb_segmenter_init_options.enable_road_boundary_side_dist_filter = option_reader.get<bool>("curb_segmenter_enable_road_boundary_side_dist_filter");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L674 | `lidar_segment_init_options.curb_segmenter_init_options.boundary_side_filter_max_allow_dist = option_reader.get<double>("curb_segmenter_boundary_side_filter_max_allow_dist");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L675 | `lidar_segment_init_options.curb_segmenter_init_options.enable_jump_points_downsample = option_reader.get<bool>("curb_segmenter_enable_jump_points_downsample");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L676 | `lidar_segment_init_options.curb_segmenter_init_options.jump_downsample_leaf_size_x = option_reader.get<float>("curb_segmenter_jump_downsample_leaf_size_x");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L677 | `lidar_segment_init_options.curb_segmenter_init_options.jump_downsample_leaf_size_y = option_reader.get<float>("curb_segmenter_jump_downsample_leaf_size_y");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L678 | `lidar_segment_init_options.curb_segmenter_init_options.jump_downsample_leaf_size_z = option_reader.get<float>("curb_segmenter_jump_downsample_leaf_size_z");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L679 | `lidar_segment_init_options.curb_segmenter_init_options.jump_min_points_per_voxel = option_reader.get<unsigned int>("curb_segmenter_jump_min_points_per_voxel");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L680 | `lidar_segment_init_options.curb_segmenter_init_options.enable_jump_points_growth_search_sort = option_reader.get<bool>("curb_segmenter_enable_jump_points_growth_search_sort");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L681 | `lidar_segment_init_options.curb_segmenter_init_options.jump_point_adjacent_max_dist = option_reader.get<float>("curb_segmenter_jump_point_adjacent_max_dist");` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/src/lidar_obstacle_segment.cpp` | L682 | `lidar_segment_init_options.curb_segmenter_init_options.use_jump_point_line_break = option_reader.get<bool>("curb_segmenter_use_jump_point_line_break");` |

</details>

---

现在让我仔细查看 `PostProcessLidarObstacle` 函数的代码：

根据我对 `PostProcessLidarObstacle` 函数的分析，**该函数中没有涉及到路沿检测相关的处理逻辑**。

该函数主要包含两个处理模块：

## 1. 空中悬浮物体过滤 (`use_aerial_leaves_filter_`)
这部分逻辑主要用于过滤掉空中的悬浮物体（如树叶、标志牌等），通过以下方式：
- 计算物体底部中心点到地面的距离
- 根据物体在不同距离范围内的位置，应用不同的尺寸和点云数量阈值
- 过滤掉不符合条件的小尺寸空中物体

## 2. 底部拉伸处理 (`use_bottom_stretch_`)
这部分逻辑用于将检测到的物体底部延伸到地面：
- 计算物体底部中心点到地面的距离
- 如果距离在合理范围内，将物体的高度增加这个距离
- 调整物体的中心点位置和多边形顶点的Z坐标

## 与路沿检测的关系
虽然这个函数中使用了 `ground_detector_->QueryPointToGroundDistance()` 来查询点到地面的距离，但这是用于**障碍物处理**，而不是路沿检测。路沿检测在这个代码库中是由专门的模块处理的：

- `CurbSegmenter` 类负责路沿分割
- `CurbBuilder` 类负责路沿构建  
- 相关的路沿处理逻辑在 `ProcessCurb` 等专门的函数中

因此，`PostProcessLidarObstacle` 函数专注于**激光雷达障碍物的后处理**，不涉及路沿检测的相关逻辑。

---

_**User**_

分析下面的参数在路沿检测过程中的使用方式和对结果的影响：

"cloud_preprocessor_roi_size_x_min": 2.4,
    "cloud_preprocessor_roi_size_x_max": 10.0,
    "cloud_preprocessor_roi_size_y_min": -10.0,
    "cloud_preprocessor_roi_size_y_max": 0.0,
    "cloud_preprocessor_roi_size_z_min": -1.0,
    "cloud_preprocessor_roi_size_z_max": 1.8,

---

_**Assistant**_

<think><details><summary>Thought Process</summary>**Exploring Curb Detection Parameters**

I'm currently investigating the `cloud_preprocessor_roi_size_* ` parameters and their impact on curb detection within `curb_segmenter.cpp`. My focus is on understanding how these parameters influence the process. I'm considering their implications on detection accuracy and efficiency.


**Analyzing ROI Parameter Usage**

I'm now examining the `curb_segmenter.cpp` code, specifically the `CurbSegmenter::Detect` function and its interactions with the `base::LidarFrame`.  I've found a few places where the ROI parameters might be used, specifically in the preprocessing stages,  prior to curb detection. My goal is to map these parameter names to their specific implementation details and figure out how the ROI sizes are calculated. I will determine how the ROI affects the curb segmentation.


**Identifying Preprocessing Stage**

I've determined that the `cloud_preprocessor_roi_size_*` parameters likely influence an initial point cloud filtering stage, preceding the main curb detection within `CurbSegmenter::Detect`. I'm tracing how `roi_indices` and `non_roi_indices` are generated and utilized within `curb_segmenter.cpp`, particularly how the ROI size affects the initial cloud data fed into curb detection. I suspect that this is the result of using a filter, and now need to understand which filter is used, and how it gets the ROI parameters.


**Confirming ROI's Functionality**

I've determined that the `cloud_preprocessor_roi_size_*` parameters are likely used to define an ROI, resulting in the generation of `roi_indices` and `non_roi_indices` within the `LidarFrame`. This confirms that the initial point cloud is being filtered. I'm focusing on how `non_roi_indices` are used in `FindRoadBoundaryLine` for refining the curb detection. My priority is establishing how this influences the overall segmenter's performance.


**Unveiling Non-ROI Role**

I've determined that the `non_roi_indices` from the preprocessing stage are used in `FindRoadBoundaryLine`, only if `enable_calculate_road_boundary_line_` is true.  This suggests that the ROI is used for an optional road boundary refinement, happening *before* the main curb detection using `SearchJumpInPolarGrid`. However, the latter appears to analyze the *entire* cloud. This requires more investigation to understand the precise interaction.


**Uncovering Parameter Application**

I'm now investigating the `CloudPreprocessor` to confirm where `cloud_preprocessor_roi_size_*` parameters are utilized. It seems the preprocessing stage generates the ROI indices, but the subsequent main detection process, via `SearchJumpInPolarGrid`, iterates over the full cloud data. I will be identifying how the ROI is used outside of this process.


**Unraveling Parameter Application**

I am currently investigating the `cloud_preprocessor_roi_size_* ` parameters' application within `curb_segmenter.cpp`, specifically within `CurbSegmenter::Detect`. I found the parameters influence a preprocessing step that yields ROI and non-ROI indices, stored in the `LidarFrame`. While the main curb detection uses the full cloud via `SearchJumpInPolarGrid`, `FindRoadBoundaryLine` leverages the non-ROI indices for optional road boundary refinement. This helps determine which HDMap boundaries are correct, prior to filtering.


**Deciphering Parameter Usage**

I've determined that the `cloud_preprocessor_roi_size_*` parameters are applied in a preprocessing stage, generating `roi_indices` and `non_roi_indices` within the `LidarFrame`. This confirms the ROI. While the main curb detection, via `SearchJumpInPolarGrid`, iterates over the full cloud, `FindRoadBoundaryLine` leverages the non-ROI indices for optional road boundary refinement prior to determining which HDMap boundaries are correct. I'll need to confirm how it interacts with the later filtering steps.


**Clarifying Parameter Role**

I'm now investigating the `cloud_preprocessor_roi_size_*` parameters' precise function within the `curb_segmenter.cpp` file. After an initial scan, I'm examining how those parameters are being used for filtering purposes, as these parameters seem to define an ROI. I need to be sure that the pre-filtering is happening as I suspect.


**Deciphering Parameter Usage**

I've determined that the `cloud_preprocessor_roi_size_*` parameters are applied in a preprocessing stage, generating `roi_indices` and `non_roi_indices` within the `LidarFrame`. This confirms the ROI. While the main curb detection, via `SearchJumpInPolarGrid`, iterates over the full cloud, `FindRoadBoundaryLine` leverages the non-ROI indices for optional road boundary refinement prior to determining which HDMap boundaries are correct. I'll need to confirm how it interacts with the later filtering steps.


**Deciphering Parameter Usage**

I'm now investigating the `cloud_preprocessor_roi_size_*` parameters' application within `curb_segmenter.cpp`, specifically within `CurbSegmenter::Detect`. I found the parameters influence a preprocessing step that yields ROI and non-ROI indices, stored in the `LidarFrame`. While the main curb detection uses the full cloud via `SearchJumpInPolarGrid`, `FindRoadBoundaryLine` leverages the non-ROI indices for optional road boundary refinement prior to determining which HDMap boundaries are correct. This helps determine which HDMap boundaries are correct, prior to filtering.

**Unveiling Preprocessing Influence**

I'm now investigating the `cloud_preprocessor_roi_size_* ` parameters' application, specifically how they are used for filtering within `curb_segmenter.cpp`. While the main curb detection (`SearchJumpInPolarGrid`) utilizes the full cloud, `FindRoadBoundaryLine` leverages the non-ROI indices, indicating that a preprocessing stage defines and uses the ROI to exclude points, potentially for road boundary refinement or to refine the results of the `SearchJumpInPolarGrid`. I need to understand the relationship between the ROI and each component of the segmentation process.


**Pinpointing ROI Interaction**

I'm currently investigating the `cloud_preprocessor_roi_size_* ` parameters' application and their impact on the `curb_segmenter.cpp` process. I am now looking for where the ROI parameters are parsed to understand how they are set. While the main curb detection utilizes the entire cloud via `SearchJumpInPolarGrid`, `FindRoadBoundaryLine` leverages the non-ROI indices. This indicates that a prior stage, possibly `CloudPreprocessor`, defines and uses the ROI to filter points. I'm focusing on that stage now.


**Deciphering Filtering Interaction**

I've determined that the `cloud_preprocessor_roi_size_*` parameters are applied in a preprocessing stage to generate `roi_indices` and `non_roi_indices` within the `LidarFrame`. This confirms the ROI.  While the main curb detection, through `SearchJumpInPolarGrid`, iterates over the full cloud, `FindRoadBoundaryLine` leverages the `non_roi_indices` for road boundary refinement. This points to a filtering mechanism. I will clarify the specific filter used and its role within the detection pipeline.


</details></think>

---

好的，我们来详细分析一下您提供的这些参数在路沿检测过程中的作用和影响。

这些参数定义了一个三维空间中的“感兴趣区域”（Region of Interest, ROI）。从参数名称 `cloud_preprocessor_roi_size_*` 可以看出，它们是在点云预处理阶段使用的。

### 1. 参数的含义

我们首先明确一下每个参数定义的空间范围（在标准的车辆坐标系中：X轴向前，Y轴向左，Z轴向上）：

*   `"cloud_preprocessor_roi_size_x_min": 2.4`
*   `"cloud_preprocessor_roi_size_x_max": 10.0`
    *   **作用**: 定义了ROI在车辆前方2.4米到10.0米的范围。

*   `"cloud_preprocessor_roi_size_y_min": -10.0`
*   `"cloud_preprocessor_roi_size_y_max": 0.0`
    *   **作用**: 定义了ROI在车辆右侧0米到10.0米的范围 (因为Y轴朝左，所以负值代表右侧)。

*   `"cloud_preprocessor_roi_size_z_min": -1.0`
*   `"cloud_preprocessor_roi_size_z_max": 1.8`
    *   **作用**: 定义了ROI在垂直方向上，从车辆坐标系原点下方1.0米到上方1.8米的范围。

综合来看，这些参数定义了一个位于**车辆前方偏右侧**的一个长方体区域。

### 2. 在路沿检测流程中的使用方式

通过分析代码，这些ROI参数的核心作用是在点云预处理阶段，将点云分割为**ROI**（感兴趣区域内的点）和**NON-ROI**（感兴趣区域外的点）两部分。

然而，在`CurbSegmenter::Detect`这个核心的路沿检测函数中，情况有些特殊：

*   **路沿候选点提取 (`SearchJumpInPolarGrid`)**:
    这个步骤是路沿检测的第一步，它通过极坐标格网寻找高度跳变点，以此作为路沿的候选点。
    **有趣的是，这一步是针对整个原始点云进行的，并未使用ROI进行过滤。**
    ```cpp
    // in CurbSegmenter::Detect
    base::PointFCloudPtr input_cloud = frame->cloud;

    base::PointIndices input_indices;
    input_indices.indices.resize(frame->cloud->size(), 0);
    std::iota(input_indices.indices.begin(), input_indices.indices.end(), 0);

    // SearchJumpInPolarGrid 使用了包含所有点的 input_indices
    SearchJumpInPolarGrid(input_cloud, input_indices, curb_points_cloud_local_);
    ```
    这样做的目的可能是为了在全局范围内最大限度地发现潜在的路沿特征，避免因ROI设置不当而过早地滤除真实的路沿点。

*   **高精地图边界线关联 (`FindRoadBoundaryLine`)**:
    在提取路沿候选点之前，有一个可选的步骤`FindRoadBoundaryLine`，它的作用是找出与高精地图中道路边界线接近的激光雷达点云，从而确定当前最可能存在的道路边界。
    **在这个函数中，使用的是 `non_roi_indices`**，也就是在ROI之外的点。
    ```cpp
    // in CurbSegmenter::FindRoadBoundaryLine
    const base::PointIndices& non_roi_indices = frame->non_roi_indices;
    // ...
    for (size_t i = 0; i < non_roi_indices.indices.size(); i++)
    {
        // ...
    }
    ```
    这表明，ROI区域内的点被认为与“背景环境”（如道路）无关，因此在匹配地图边界时被**排除**。这通常用于排除车辆本身或车上固定物体的干扰。

### 3. 对结果的影响

基于上述分析，这些参数对路沿检测结果的影响是间接但关键的：

1.  **主要影响地图边界的确认**:
    *   ROI区域的设定直接影响 `FindRoadBoundaryLine` 函数的结果。如果ROI设置过大，可能会错误地将本应属于道路环境的点排除掉，导致无法正确关联到高精地图中的路沿，从而影响后续处理流程。
    *   如果ROI设置过小，无法完全覆盖车身等干扰物，则可能将车身上的点误判为靠近地图边界的点，造成错误的边界定位。

2.  **对最终路沿点云无直接过滤作用**:
    *   由于核心的路沿候选点提取 (`SearchJumpInPolarGrid`) 作用于全点云，因此理论上，即使路沿位于您设定的ROI区域内，它仍然有潜力被检测到。
    *   但是，后续的一系列滤波和聚类步骤（如`RoadBoundarySideDistFilter`、`JumpPointsGrowthSearchSort`等）会依赖于前面步骤的结果。如果`FindRoadBoundaryLine`因为不合理的ROI设置而出错，也可能会间接影响最终的路沿线生成。

### 总结

`cloud_preprocessor_roi_*`系列参数定义了一个3D框，主要用于**排除**特定区域内的点，使其不参与高精地图边界的匹配过程。这通常是为了**滤除车身自身的点云干扰**。

这个ROI区域**不会直接**从输入点云中删除用于路沿检测的点，但它通过影响地图边界的识别，间接地影响了整个路沿检测流程的准确性和鲁棒性。这是一个“先全局搜索，再结合先验信息（地图）进行精炼”的策略。

---

