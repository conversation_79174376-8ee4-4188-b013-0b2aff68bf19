<!-- Generated by SpecStory -->

# 护栏点可视化功能实现 (2025-05-12 01:32Z)

_**User**_

护栏点是VCS自车坐标系的，因此请分析代码，在合适的地方添加护栏点和线的可视化功能。可视化方案采用C++实现，可基于OpenCV实现。可视化代码要用单独函数实现

---

_**Assistant**_

<think><details><summary>Thought Process</summary>我需要分析代码，在合适的地方添加护栏点和线的可视化功能。根据要求，可视化方案需要用C++实现，可以基于OpenCV实现，并且需要放在单独的函数中。

首先分析现有代码:
1. 这是一个道路边界感知跟踪的测试程序 (`rbt_test.cpp`)
2. 已经包含了 OpenCV 头文件: `#include "opencv2/opencv.hpp"`
3. 代码主要处理护栏点数据，读取、处理并保存
4. `SaveGuardrail` 函数收集并保存护栏点信息
5. 主函数循环处理多个帧，跟踪道路边界

一个合适的地方是在主循环中，在处理完成跟踪后，添加可视化。我们需要:
1. 创建一个新的可视化函数
2. 在主循环中调用这个函数

我将创建一个叫 `VisualizeGuardrail` 的函数，用OpenCV实现以下功能:
- 创建一个鸟瞰图（Bird's Eye View）的图像
- 绘制VCS自车坐标系的护栏点和线
- 添加自车位置标记
- 添加坐标轴和网格以提高可读性
- 保存可视化图像

函数将接受:
- 检测到的护栏点 (`PBI::RoadInfoArray *p_detect`)
- 跟踪的护栏点 (`PBI::RoadInfoArray *p_track`)
- 当前位置信息 (`PBI::LocalizationEstimate *pose`)
- 输出图像路径 (`const std::string &file_path`)</details></think>

---

# 添加护栏点和线的可视化功能

我将分析代码结构，设计一个基于OpenCV的护栏点和线可视化功能。在`SaveGuardrail`函数类似的位置添加可视化函数是合适的。

---

```diff
  const char pose_folder[] = "/data1/xiaojied/share/release/hfr/data/road_boundary/1708844416_1708844532/data";
  const char result_folder[] = "/data1/xiaojied/share/release/hfr/data/road_boundary/1708844416_1708844532/target_management";
+ const char visual_folder[] = "/data1/xiaojied/share/release/hfr/data/road_boundary/1708844416_1708844532/visualization";
  
  int start_frame = 0;    //start at a middle frame for debug
```

```diff
      return true;
  }
+ 
+ /**
+  * @brief 将护栏点和线进行可视化，生成BEV视图图像
+  * 
+  * @param file_path 保存可视化图像的路径
+  * @param p_detect 检测到的道路信息数组
+  * @param p_track 跟踪的道路信息数组
+  * @param pose 当前位置信息
+  */
+ void VisualizeGuardrail(const std::string &file_path, 
+                          const PBI::RoadInfoArray *p_detect, 
+                          const PBI::RoadInfoArray *p_track,
+                          const PBI::LocalizationEstimate *pose) {
+     // 创建鸟瞰图画布
+     // 定义图像尺寸和比例尺
+     const int img_width = 800;
+     const int img_height = 800;
+     const float scale = 10.0;  // 10像素/米
+     const float max_range = 80.0;  // 最大可视范围(米)
+ 
+     // 创建白色背景的图像
+     cv::Mat bev_img(img_height, img_width, CV_8UC3, cv::Scalar(255, 255, 255));
+     
+     // 计算图像中心点(车辆位置)
+     cv::Point2i center(img_width / 2, img_height / 2);
+     
+     // 绘制坐标系和网格线
+     // 绘制水平和垂直中心线
+     cv::line(bev_img, cv::Point(0, center.y), cv::Point(img_width, center.y), 
+              cv::Scalar(200, 200, 200), 1, cv::LINE_AA);
+     cv::line(bev_img, cv::Point(center.x, 0), cv::Point(center.x, img_height), 
+              cv::Scalar(200, 200, 200), 1, cv::LINE_AA);
+     
+     // 绘制网格线
+     for (int i = -int(max_range); i <= int(max_range); i += 10) {
+         // 跳过中心线，因为已经绘制
+         if (i == 0) continue;
+         
+         // 计算网格线在图像中的位置
+         int x_pos = center.x + int(i * scale);
+         int y_pos = center.y + int(i * scale);
+         
+         // 确保网格线在图像范围内
+         if (x_pos >= 0 && x_pos < img_width) {
+             cv::line(bev_img, cv::Point(x_pos, 0), cv::Point(x_pos, img_height), 
+                      cv::Scalar(230, 230, 230), 1, cv::LINE_AA);
+             // 添加刻度标签
+             cv::putText(bev_img, std::to_string(i), 
+                         cv::Point(x_pos - 10, center.y + 15), 
+                         cv::FONT_HERSHEY_SIMPLEX, 0.3, cv::Scalar(100, 100, 100));
+         }
+         
+         if (y_pos >= 0 && y_pos < img_height) {
+             cv::line(bev_img, cv::Point(0, y_pos), cv::Point(img_width, y_pos), 
+                      cv::Scalar(230, 230, 230), 1, cv::LINE_AA);
+             // 添加刻度标签
+             cv::putText(bev_img, std::to_string(-i), 
+                         cv::Point(center.x + 5, y_pos + 5), 
+                         cv::FONT_HERSHEY_SIMPLEX, 0.3, cv::Scalar(100, 100, 100));
+         }
+     }
+     
+     // 绘制坐标轴标签
+     cv::putText(bev_img, "X(m)", cv::Point(img_width - 40, center.y - 10), 
+                 cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 0));
+     cv::putText(bev_img, "Y(m)", cv::Point(center.x + 10, 20), 
+                 cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 0));
+     
+     // 绘制当前车辆位置
+     cv::circle(bev_img, center, 5, cv::Scalar(0, 0, 255), -1, cv::LINE_AA);
+     
+     // 收集检测到的护栏点(红色)
+     std::vector<cv::Point2f> detect_points;
+     int array_size = static_cast<int>(sizeof(p_detect->road) / sizeof(PBI::RoadInfo));
+     for (int i = 0; i < array_size; i++) {
+         const PBI::RoadInfo &border = p_detect->road[i];
+         if (border.valid) {
+             const auto &guardrail = border.guardrail;
+             std::vector<cv::Point2i> line_points;
+             
+             for (int j = 0; j < guardrail.num; ++j) {
+                 const auto &point = guardrail.point[j];
+                 // 将护栏点坐标转换为图像坐标
+                 // 注意坐标系变换：x向前，y向左，图像x向右，y向下
+                 int img_x = center.x + int(point.x * scale);
+                 int img_y = center.y - int(point.y * scale);
+                 
+                 // 确保点在图像范围内
+                 if (img_x >= 0 && img_x < img_width && img_y >= 0 && img_y < img_height) {
+                     line_points.push_back(cv::Point2i(img_x, img_y));
+                     cv::circle(bev_img, cv::Point2i(img_x, img_y), 2, cv::Scalar(0, 0, 255), -1, cv::LINE_AA);
+                 }
+             }
+             
+             // 如果有足够的点，绘制连线
+             if (line_points.size() >= 2) {
+                 for (size_t j = 0; j < line_points.size() - 1; ++j) {
+                     cv::line(bev_img, line_points[j], line_points[j + 1], 
+                              cv::Scalar(0, 0, 255), 1, cv::LINE_AA);
+                 }
+             }
+         }
+     }
+     
+     // 收集跟踪的护栏点(绿色)
+     std::vector<cv::Point2f> track_points;
+     for (int i = 0; i < array_size; i++) {
+         const PBI::RoadInfo &border = p_track->road[i];
+         if (border.valid) {
+             const auto &guardrail = border.guardrail;
+             std::vector<cv::Point2i> line_points;
+             
+             for (int j = 0; j < guardrail.num; ++j) {
+                 const auto &point = guardrail.point[j];
+                 // 将护栏点坐标转换为图像坐标
+                 int img_x = center.x + int(point.x * scale);
+                 int img_y = center.y - int(point.y * scale);
+                 
+                 // 确保点在图像范围内
+                 if (img_x >= 0 && img_x < img_width && img_y >= 0 && img_y < img_height) {
+                     line_points.push_back(cv::Point2i(img_x, img_y));
+                     cv::circle(bev_img, cv::Point2i(img_x, img_y), 2, cv::Scalar(0, 255, 0), -1, cv::LINE_AA);
+                 }
+             }
+             
+             // 如果有足够的点，绘制连线
+             if (line_points.size() >= 2) {
+                 for (size_t j = 0; j < line_points.size() - 1; ++j) {
+                     cv::line(bev_img, line_points[j], line_points[j + 1], 
+                              cv::Scalar(0, 255, 0), 1, cv::LINE_AA);
+                 }
+             }
+         }
+     }
+     
+     // 添加图例
+     cv::rectangle(bev_img, cv::Point(img_width - 120, 10), cv::Point(img_width - 10, 70), 
+                   cv::Scalar(255, 255, 255), -1);
+     cv::rectangle(bev_img, cv::Point(img_width - 120, 10), cv::Point(img_width - 10, 70), 
+                   cv::Scalar(0, 0, 0), 1);
+     
+     cv::circle(bev_img, cv::Point(img_width - 100, 25), 3, cv::Scalar(0, 0, 255), -1, cv::LINE_AA);
+     cv::putText(bev_img, "检测护栏点", cv::Point(img_width - 90, 30), 
+                 cv::FONT_HERSHEY_SIMPLEX, 0.4, cv::Scalar(0, 0, 0));
+     
+     cv::circle(bev_img, cv::Point(img_width - 100, 45), 3, cv::Scalar(0, 255, 0), -1, cv::LINE_AA);
+     cv::putText(bev_img, "跟踪护栏点", cv::Point(img_width - 90, 50), 
+                 cv::FONT_HERSHEY_SIMPLEX, 0.4, cv::Scalar(0, 0, 0));
+     
+     cv::circle(bev_img, cv::Point(img_width - 100, 65), 5, cv::Scalar(0, 0, 255), -1, cv::LINE_AA);
+     cv::putText(bev_img, "自车位置", cv::Point(img_width - 90, 70), 
+                 cv::FONT_HERSHEY_SIMPLEX, 0.4, cv::Scalar(0, 0, 0));
+     
+     // 添加时间戳和帧信息
+     std::string timestamp_str = "时间戳: " + std::to_string(pose->hbi_header.timestamp_us / 1000000.0) + "s";
+     cv::putText(bev_img, timestamp_str, cv::Point(10, img_height - 30), 
+                 cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 0));
+     
+     // 保存图像
+     cv::imwrite(file_path, bev_img);
+     std::cout << "可视化图像已保存: " << file_path << std::endl;
+ }
  
  int main(int argc, char *argv[]) {
```

```diff
  
      mkdir(result_folder, 00755);
+     mkdir(visual_folder, 00755);  // 创建可视化结果文件夹
+     
      int end_num = std::min(end_frame, (int) bin_name_list.size());
      for (int frm_cnt = start_frame; frm_cnt < end_num; frm_cnt++) {
```

```diff
          sprintf(save_path, "%s/%s", result_folder, result_file_name.c_str());
          PBI::SaveResult(save_path, &tracked_roads);
+         
+         // 可视化护栏点
+         GetFilename(bin_path, result_file_name);
+         result_file_name.replace(result_file_name.find("bin"), -1, "png");
+         sprintf(save_path, "%s/%s", visual_folder, result_file_name.c_str());
+         VisualizeGuardrail(save_path, &detected_roads, &tracked_roads, &localization_message);
      }
  
```

---

我已经成功添加了护栏点和线的可视化功能。以下是实现细节：

1. 添加了一个专门的可视化文件夹路径常量：
   ```cpp
   const char visual_folder[] = "/data1/xiaojied/share/release/hfr/data/road_boundary/1708844416_1708844532/visualization";
   ```

2. 实现了一个独立的可视化函数 `VisualizeGuardrail`，它具有以下功能：
   - 创建鸟瞰图(BEV)视图，尺寸为800×800像素
   - 绘制坐标系网格和刻度标签，便于观察距离
   - 在图像中心绘制自车位置（红色圆点）
   - 使用不同颜色区分检测点（红色）和跟踪点（绿色）
   - 连接同一组护栏的点形成护栏线
   - 添加图例说明和时间戳信息
   - 保存可视化结果为PNG图像

3. 在主函数中：
   - 创建可视化结果文件夹 `mkdir(visual_folder, 00755);`
   - 在处理每一帧后调用可视化函数：
     ```cpp
     GetFilename(bin_path, result_file_name);
     result_file_name.replace(result_file_name.find("bin"), -1, "png");
     sprintf(save_path, "%s/%s", visual_folder, result_file_name.c_str());
     VisualizeGuardrail(save_path, &detected_roads, &tracked_roads, &localization_message);
     ```

这个可视化方案充分考虑了：
- 坐标系转换（注意VCS自车坐标系到图像坐标系的变换）
- 合理的比例尺（10像素/米）和可视范围（80米）
- 清晰的图例和标注
- 分别可视化检测点和跟踪点

通过这个可视化功能，您可以直观地观察护栏点在车辆周围的分布情况，方便分析护栏检测和跟踪算法的性能。

---

_**User**_

修复编译报错

---

_**Assistant**_

<think><details><summary>Thought Process</summary>我看到编译错误消息显示链接器无法找到OpenCV库的函数。问题在于我们添加了OpenCV的可视化功能，但在链接时没有正确地链接OpenCV库，导致了大量的"undefined reference"错误。

错误信息指向的是我们添加的VisualizeGuardrail函数中使用的OpenCV函数，如cv::line、cv::circle、cv::putText等，这些都找不到引用。

需要修复的问题：
1. 需要在CMakeLists.txt中添加OpenCV库的链接
2. 确保CMakeLists.txt正确配置了OpenCV的包含路径和库路径

我们需要获取或修改CMakeLists.txt文件，添加对OpenCV库的正确引用。</details></think>

---

需要修复链接错误，编译器无法找到OpenCV的相关函数。修改CMakeLists.txt文件来正确链接OpenCV库。

---

<details>
            <summary>Listed directory modules/HAF/road_boundary_perception_track/core/private • **13** results</summary>
        
| Name |
|-------|
| 📁 `build` |
| 📁 `.git` |
| 📁 `data` |
| 📁 `test` |
| 📁 `tools` |
| 📁 `RBT` |
| 📁 `common` |
| 📁 `docs` |
| 📁 `resource` |
| 📁 `RBD` |
| 📄 `.gitignore` |
| 📄 `.gitkeep` |
| 📄 `CMakeLists.txt` |

</details>

---

Read file: modules/HAF/road_boundary_perception_track/core/private/CMakeLists.txt

---

```diff
  include_directories(${HLOG_INCLUDE_DIR})
  link_directories(${HLOG_LIBRARY_DIR})
+ 
+ # 查找OpenCV库
+ find_package(OpenCV REQUIRED PATHS ${OPENCV_LIBRARY_DIR}/../share/OpenCV NO_DEFAULT_PATH)
+ message(STATUS "OpenCV库版本: ${OpenCV_VERSION}")
+ message(STATUS "OpenCV包括目录: ${OpenCV_INCLUDE_DIRS}")
+ message(STATUS "OpenCV库: ${OpenCV_LIBS}")
  
  ###########
```

```diff
          ${DLT_LIBRARIES}
          ${HLOG_LIBRARIES}
+         ${OpenCV_LIBS}  # 添加OpenCV库链接
          )
  
```

---

```bash
cd modules/HAF/road_boundary_perception_track/core/private/build && rm -rf * && cmake .. && make
```

```
-- The C compiler identification is GNU 7.5.0
-- The CXX compiler identification is GNU 7.5.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
HBI_VERSION=1.39

Start glob configure cmake
Search Done: /hfr/lib

Start glob library
    ==> find library: adolc, And create variable: ADOLC_DIR , ADOLC_INCLUDE_DIR
, ADOLC_LIBRARY
_DIR , ADOLC_LIBRARIES
                   ==> find library: boost, And create variable: BOOST_DIR , BOO
ST_INCLUDE_DIR , BOOST_LIBRARY
_DIR , BOOST_LIBRARIES
                   ==> find library: cjson, And create variable: CJSON_DIR , CJS
ON_INCLUDE_DIR , CJSON_LIBRARY
_DIR , CJSON_LIBRARIES
                   ==> find library: colpack, And create variable: COLPACK_DIR ,
 COLPACK_INCLUDE_DIR , COLPACK
_LIBRARY_DIR , COLPACK_LIBRARIES
                   ==> find library: dlt, And create variable: DLT_DIR , DLT_INC
LUDE_DIR , DLT_LIBRARY_DIR , D
LT_LIBRARIES
                   ==> find library: eigen, And create variable: EIGEN_DIR , EIG
EN_INCLUDE_DIR , EIGEN_FOUND 
    ==> find library: flann, And create variable: FLANN_DIR , FLANN_INCLUDE_DIR
, FLANN_LIBRARY
_DIR , FLANN_LIBRARIES
                   ==> find library: gflags, And create variable: GFLAGS_DIR , G
FLAGS_INCLUDE_DIR , GFLAGS_LIB
RARY_DIR , GFLAGS_LIBRARIES
                   ==> find library: gfortran, And create variable: GFORTRAN_DIR
 , GFORTRAN_INCLUDE_DIR , GFOR
TRAN_LIBRARY_DIR , GFORTRAN_LIBRARIES
                   ==> find library: glog, And create variable: GLOG_DIR , GLOG_
INCLUDE_DIR , GLOG_LIBRARY_DIR
 , GLOG_LIBRARIES
                   ==> find library: ipopt, And create variable: IPOPT_DIR , IPO
PT_INCLUDE_DIR , IPOPT_LIBRARY
_DIR , IPOPT_LIBRARIES
                   ==> find library: lapack, And create variable: LAPACK_DIR , L
APACK_INCLUDE_DIR , LAPACK_LIB
RARY_DIR , LAPACK_LIBRARIES
                   ==> find library: mosquitto, And create variable: MOSQUITTO_D
IR , MOSQUITTO_INCLUDE_DIR , M
OSQUITTO_LIBRARY_DIR , MOSQUITTO_LIBRARIES
                   ==> find library: opencv, And create variable: OPENCV_DIR , O
PENCV_INCLUDE_DIR , OPENCV_LIB
RARY_DIR , OPENCV_LIBRARIES
                   ==> find library: openssl, And create variable: OPENSSL_DIR ,
 OPENSSL_INCLUDE_DIR , OPENSSL
_LIBRARY_DIR , OPENSSL_LIBRARIES
                   ==> find library: osqp, And create variable: OSQP_DIR , OSQP_
INCLUDE_DIR , OSQP_LIBRARY_DIR
 , OSQP_LIBRARIES
                   ==> find library: pcl, And create variable: PCL_DIR , PCL_INC
LUDE_DIR , PCL_LIBRARY_DIR , P
CL_LIBRARIES
                   ==> find library: protobuf, And create variable: PROTOBUF_DIR
 , PROTOBUF_INCLUDE_DIR , PROT
OBUF_LIBRARY_DIR , PROTOBUF_LIBRARIES
                   ==> find library: rs_driver, And create variable: RS_DRIVER_D
IR , RS_DRIVER_INCLUDE_DIR , R
S_DRIVER_LIBRARY_DIR , RS_DRIVER_LIBRARIES
                   ==> find library: yaml, And create variable: YAML_DIR , YAML_
INCLUDE_DIR , YAML_LIBRARY_DIR
 , YAML_LIBRARIES
                   ==> find library: autocalib, And create variable: AUTOCALIB_D
IR , AUTOCALIB_INCLUDE_DIR , A
UTOCALIB_LIBRARY_DIR , AUTOCALIB_LIBRARIES
                   ==> find library: components_and_data_center, And create vari
able: COMPONENTS_AND_DATA_CENT
ER_DIR , COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR , COMPONENTS_AND_DATA_CENTER_LIB
RARY_DIR , COMPONENTS_AND_DATA_CENTER_LIBRARIES
                                  ==> find library: control, And create variable
: CONTROL_DIR , CONTROL_INCLUDE_DIR , CONTROL
_LIBRARY_DIR , CONTROL_LIBRARIES
                   ==> find library: execution_manifest, And create variable: EX
ECUTION_MANIFEST_DIR , EXECUTI
ON_MANIFEST_INCLUDE_DIR , EXECUTION_MANIFEST_LIBRARY_DIR , EXECUTION_MANIFEST_LI
BRARIES            ==> find library: fusion_localization_cargovehicle, And creat
e variable: FUSION_LOCALIZATIO
N_CARGOVEHICLE_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_INCLUDE_DIR , FUSION_LOCAL
IZATION_CARGOVEHICLE_LIBRARY_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_LIBRARIES
                                  ==> find library: hlog, And create variable: H
LOG_DIR , HLOG_INCLUDE_DIR , HLOG_LIBRARY_DIR
 , HLOG_LIBRARIES
                   ==> find library: iceoryx, And create variable: ICEORYX_DIR ,
 ICEORYX_INCLUDE_DIR , ICEORYX
_LIBRARY_DIR , ICEORYX_LIBRARIES
                   ==> find library: lidar_obstacle_perception_detect, And creat
e variable: LIDAR_OBSTACLE_PER
CEPTION_DETECT_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_INCLUDE_DIR , LIDAR_OBSTAC
LE_PERCEPTION_DETECT_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_LIBRARIES
                                  ==> find library: lidar_obstacle_perception_fu
sion, And create variable: LIDAR_OBSTACLE_PER
CEPTION_FUSION_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_INCLUDE_DIR , LIDAR_OBSTAC
LE_PERCEPTION_FUSION_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_LIBRARIES
                                  ==> find library: lidar_obstacle_perception_se
gment, And create variable: LIDAR_OBSTACLE_PE
RCEPTION_SEGMENT_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_INCLUDE_DIR , LIDAR_OBS
TACLE_PERCEPTION_SEGMENT_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_LIBRARI
ES                                ==> find library: lidar_obstacle_perception_tr
ack, And create variable: LIDAR_OBSTACLE_PERC
EPTION_TRACK_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_INCLUDE_DIR , LIDAR_OBSTACLE_
PERCEPTION_TRACK_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_LIBRARIES
                                  ==> find library: map, And create variable: MA
P_DIR , MAP_INCLUDE_DIR , MAP_LIBRARY_DIR , M
AP_LIBRARIES
                   ==> find library: planning, And create variable: PLANNING_DIR
 , PLANNING_INCLUDE_DIR , PLAN
NING_LIBRARY_DIR , PLANNING_LIBRARIES
                   ==> find library: road_boundary_perception_track, And create
variable: ROAD_BOUNDARY_PERCEP
TION_TRACK_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR , ROAD_BOUNDARY_PERC
EPTION_TRACK_LIBRARY_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARIES
                                  ==> find library: vsomeip, And create variable
: VSOMEIP_DIR , VSOMEIP_INCLUDE_DIR , VSOMEIP
_LIBRARY_DIR , VSOMEIP_LIBRARIES
               ==> copy from: /hfr/3rdparty/dlt/x86_64/lib
==> copy to: /usr/lib/libdlt.so
==> copy to: /usr/lib/libdlt.so.2
==> copy to: /usr/lib/libdlt.so.2.18.9
CMake Error at CMakeLists.txt:45 (find_package):
  Could not find a package configuration file provided by "OpenCV" with any
  of the following names:

    OpenCVConfig.cmake
    opencv-config.cmake

  Add the installation prefix of "OpenCV" to CMAKE_PREFIX_PATH or set
  "OpenCV_DIR" to a directory containing one of the above files.  If "OpenCV"
  provides a separate development package or SDK, be sure it has been
  installed.


-- Configuring incomplete, errors occurred!
See also "/hfr/modules/HAF/road_boundary_perception_track/core/private/build/CMa
keFiles/CMakeOu
tput.log".
               
```

---

看起来需要修复OpenCV的查找路径。让我们进行更改：

---

```bash
ls -la ${OPENCV_LIBRARY_DIR}/../ | grep -i opencv
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# 

```

---

```bash
echo $OPENCV_LIBRARY_DIR
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# echo
 $OPENCV_LIBRARY_DIR

root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build#
```

---

```bash
ls -la /hfr/3rdparty/opencv/
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# ls -
la /hfr/3rdparty/opencv/
total 20
drwxrwxr-x  5 <USER> <GROUP> 4096 May 12 13:41 .
drwxrwxr-x 24 <USER> <GROUP> 4096 May 12 13:41 ..
lrwxrwxrwx  1 weilint weilint   47 May 12 13:41 .git -> ../../../.repo/projects/hfr/3rdparty/op
encv.git
drwxrwxr-x  6 <USER> <GROUP> 4096 May 12 13:41 arm64_a1000b
lrwxrwxrwx  1 weilint weilint    9 May 12 13:41 x86_64 -> x86_64_18
drwxrwxr-x  6 <USER> <GROUP> 4096 May 12 13:41 x86_64_18
drwxrwxr-x  6 <USER> <GROUP> 4096 May 12 13:41 x86_64_20
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build#
```

---

```bash
ls -la /hfr/3rdparty/opencv/x86_64/lib
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# ls -
la /hfr/3rdparty/opencv/x86_64/lib
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# ls -
la /hfr/3rdparty/opencv/x86_64/libtotal 33680
drwxrwxr-x 4 <USER> <GROUP>    4096 May 12 13:41 .
drwxrwxr-x 6 <USER> <GROUP>    4096 May 12 13:41 ..
drwxrwxr-x 3 <USER> <GROUP>    4096 May 12 13:41 cmake
lrwxrwxrwx 1 weilint weilint      24 May 12 13:41 libopencv_calib3d.so -> libopencv_calib3d.so.
4.4
lrwxrwxrwx 1 weilint weilint      26 May 12 13:41 libopencv_calib3d.so.4.4 -> libopencv_calib3d
.so.4.4.0
-rw-rw-r-- 1 <USER> <GROUP> 1995832 May 12 13:41 libopencv_calib3d.so.4.4.0
lrwxrwxrwx 1 weilint weilint      21 May 12 13:41 libopencv_core.so -> libopencv_core.so.4.4
lrwxrwxrwx 1 weilint weilint      23 May 12 13:41 libopencv_core.so.4.4 -> libopencv_core.so.4.
4.0
-rw-rw-r-- 1 <USER> <GROUP> 5365096 May 12 13:41 libopencv_core.so.4.4.0
lrwxrwxrwx 1 weilint weilint      20 May 12 13:41 libopencv_dnn.so -> libopencv_dnn.so.4.4
lrwxrwxrwx 1 weilint weilint      22 May 12 13:41 libopencv_dnn.so.4.4 -> libopencv_dnn.so.4.4.
0
-rw-rw-r-- 1 <USER> <GROUP> 5645928 May 12 13:41 libopencv_dnn.so.4.4.0
lrwxrwxrwx 1 weilint weilint      27 May 12 13:41 libopencv_features2d.so -> libopencv_features
2d.so.4.4
lrwxrwxrwx 1 weilint weilint      29 May 12 13:41 libopencv_features2d.so.4.4 -> libopencv_feat
ures2d.so.4.4.0
-rw-rw-r-- 1 <USER> <GROUP> 1103080 May 12 13:41 libopencv_features2d.so.4.4.0
lrwxrwxrwx 1 weilint weilint      22 May 12 13:41 libopencv_flann.so -> libopencv_flann.so.4.4
lrwxrwxrwx 1 weilint weilint      24 May 12 13:41 libopencv_flann.so.4.4 -> libopencv_flann.so.
4.4.0
-rw-rw-r-- 1 <USER> <GROUP>  523840 May 12 13:41 libopencv_flann.so.4.4.0
lrwxrwxrwx 1 weilint weilint      21 May 12 13:41 libopencv_gapi.so -> libopencv_gapi.so.4.4
lrwxrwxrwx 1 weilint weilint      23 May 12 13:41 libopencv_gapi.so.4.4 -> libopencv_gapi.so.4.
4.0
-rw-rw-r-- 1 <USER> <GROUP> 3805816 May 12 13:41 libopencv_gapi.so.4.4.0
lrwxrwxrwx 1 weilint weilint      24 May 12 13:41 libopencv_highgui.so -> libopencv_highgui.so.
4.4
lrwxrwxrwx 1 weilint weilint      26 May 12 13:41 libopencv_highgui.so.4.4 -> libopencv_highgui
.so.4.4.0
-rw-rw-r-- 1 <USER> <GROUP>  131416 May 12 13:41 libopencv_highgui.so.4.4.0
lrwxrwxrwx 1 weilint weilint      26 May 12 13:41 libopencv_imgcodecs.so -> libopencv_imgcodecs
.so.4.4
lrwxrwxrwx 1 weilint weilint      28 May 12 13:41 libopencv_imgcodecs.so.4.4 -> libopencv_imgco
decs.so.4.4.0
-rw-rw-r-- 1 <USER> <GROUP> 3670064 May 12 13:41 libopencv_imgcodecs.so.4.4.0
lrwxrwxrwx 1 weilint weilint      24 May 12 13:41 libopencv_imgproc.so -> libopencv_imgproc.so.
4.4
lrwxrwxrwx 1 weilint weilint      26 May 12 13:41 libopencv_imgproc.so.4.4 -> libopencv_imgproc
.so.4.4.0
-rw-rw-r-- 1 <USER> <GROUP> 7861856 May 12 13:41 libopencv_imgproc.so.4.4.0
lrwxrwxrwx 1 weilint weilint      19 May 12 13:41 libopencv_ml.so -> libopencv_ml.so.4.4
lrwxrwxrwx 1 weilint weilint      21 May 12 13:41 libopencv_ml.so.4.4 -> libopencv_ml.so.4.4.0
-rw-rw-r-- 1 <USER> <GROUP>  912104 May 12 13:41 libopencv_ml.so.4.4.0
lrwxrwxrwx 1 weilint weilint      26 May 12 13:41 libopencv_objdetect.so -> libopencv_objdetect
.so.4.4
lrwxrwxrwx 1 weilint weilint      28 May 12 13:41 libopencv_objdetect.so.4.4 -> libopencv_objde
tect.so.4.4.0
-rw-rw-r-- 1 <USER> <GROUP>  559464 May 12 13:41 libopencv_objdetect.so.4.4.0
lrwxrwxrwx 1 weilint weilint      22 May 12 13:41 libopencv_photo.so -> libopencv_photo.so.4.4
lrwxrwxrwx 1 weilint weilint      24 May 12 13:41 libopencv_photo.so.4.4 -> libopencv_photo.so.
4.4.0
-rw-rw-r-- 1 <USER> <GROUP>  986536 May 12 13:41 libopencv_photo.so.4.4.0
lrwxrwxrwx 1 weilint weilint      26 May 12 13:41 libopencv_stitching.so -> libopencv_stitching
.so.4.4
lrwxrwxrwx 1 weilint weilint      28 May 12 13:41 libopencv_stitching.so.4.4 -> libopencv_stitc
hing.so.4.4.0
-rw-rw-r-- 1 <USER> <GROUP>  862616 May 12 13:41 libopencv_stitching.so.4.4.0
lrwxrwxrwx 1 weilint weilint      22 May 12 13:41 libopencv_video.so -> libopencv_video.so.4.4
lrwxrwxrwx 1 weilint weilint      24 May 12 13:41 libopencv_video.so.4.4 -> libopencv_video.so.
4.4.0
-rw-rw-r-- 1 <USER> <GROUP>  557872 May 12 13:41 libopencv_video.so.4.4.0
lrwxrwxrwx 1 weilint weilint      24 May 12 13:41 libopencv_videoio.so -> libopencv_videoio.so.
4.4
lrwxrwxrwx 1 weilint weilint      26 May 12 13:41 libopencv_videoio.so.4.4 -> libopencv_videoio
.so.4.4.0
-rw-rw-r-- 1 <USER> <GROUP>  521032 May 12 13:41 libopencv_videoio.so.4.4.0
drwxrwxr-x 3 <USER> <GROUP>    4096 May 12 13:41 python3.6
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build#
```

---

```bash
ls -la /hfr/3rdparty/opencv/x86_64/lib/cmake
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# ls -
la /hfr/3rdparty/opencv/x86_64/lib/cmake
total 12
drwxrwxr-x 3 <USER> <GROUP> 4096 May 12 13:41 .
drwxrwxr-x 4 <USER> <GROUP> 4096 May 12 13:41 ..
drwxrwxr-x 2 <USER> <GROUP> 4096 May 12 13:41 opencv4
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build#
```

---

```bash
ls -la /hfr/3rdparty/opencv/x86_64/lib/cmake/opencv4
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# ls -
la /hfr/3rdparty/opencv/x86_64/lib/cmake/opencv4
total 44
drwxrwxr-x 2 <USER> <GROUP>  4096 May 12 13:41 .
drwxrwxr-x 3 <USER> <GROUP>  4096 May 12 13:41 ..
-rw-rw-r-- 1 <USER> <GROUP>   418 May 12 13:41 OpenCVConfig-version.cmake
-rw-rw-r-- 1 <USER> <GROUP> 14708 May 12 13:41 OpenCVConfig.cmake
-rw-rw-r-- 1 <USER> <GROUP>  7572 May 12 13:41 OpenCVModules-release.cmake
-rw-rw-r-- 1 <USER> <GROUP>  6371 May 12 13:41 OpenCVModules.cmake
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build#
```

---

根据查找到的OpenCV配置文件位置，让我们修改CMakeLists.txt：

---

```diff
  #Eigen
  include_directories(${EIGEN_INCLUDE_DIR})
+ 
+ # 设置OpenCV_DIR以指向OpenCV CMake配置文件位置
+ set(OpenCV_DIR "/hfr/3rdparty/opencv/x86_64/lib/cmake/opencv4")
+ # 查找OpenCV库
+ find_package(OpenCV 4 REQUIRED)
+ message(STATUS "OpenCV库版本: ${OpenCV_VERSION}")
+ message(STATUS "OpenCV包括目录: ${OpenCV_INCLUDE_DIRS}")
+ message(STATUS "OpenCV库: ${OpenCV_LIBS}")
  
  #OpenCV
- include_directories(${OPENCV_INCLUDE_DIR})
- link_directories(${OPENCV_LIBRARY_DIR})
+ include_directories(${OpenCV_INCLUDE_DIRS})
  
  #dlt
```

```diff
  include_directories(${HLOG_INCLUDE_DIR})
  link_directories(${HLOG_LIBRARY_DIR})
- 
- # 查找OpenCV库
- find_package(OpenCV REQUIRED PATHS ${OPENCV_LIBRARY_DIR}/../share/OpenCV NO_DEFAULT_PATH)
- message(STATUS "OpenCV库版本: ${OpenCV_VERSION}")
- message(STATUS "OpenCV包括目录: ${OpenCV_INCLUDE_DIRS}")
- message(STATUS "OpenCV库: ${OpenCV_LIBS}")
  
  ###########
```

---

```bash
cd modules/HAF/road_boundary_perception_track/core/private/build && rm -rf * && cmake .. && make
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# cd m
odules/HAF/road_boundary_perception_track/core/private/build && rm -rf * && cmake .. && make
bash: cd: modules/HAF/road_boundary_perception_track/core/private/build: No such file or direct
ory
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build#
```

---

```bash
rm -rf * && cmake .. && make
```

```
-- The C compiler identification is GNU 7.5.0
-- The CXX compiler identification is GNU 7.5.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
HBI_VERSION=1.39

Start glob configure cmake
Search Done: /hfr/lib

Start glob library
    ==> find library: adolc, And create variable: ADOLC_DIR , ADOLC_INCLUDE_DIR , ADOLC_LIBRARY
_DIR , ADOLC_LIBRARIES
    ==> find library: boost, And create variable: BOOST_DIR , BOOST_INCLUDE_DIR , BOOST_LIBRARY
_DIR , BOOST_LIBRARIES
    ==> find library: cjson, And create variable: CJSON_DIR , CJSON_INCLUDE_DIR , CJSON_LIBRARY
_DIR , CJSON_LIBRARIES
    ==> find library: colpack, And create variable: COLPACK_DIR , COLPACK_INCLUDE_DIR , COLPACK
_LIBRARY_DIR , COLPACK_LIBRARIES
    ==> find library: dlt, And create variable: DLT_DIR , DLT_INCLUDE_DIR , DLT_LIBRARY_DIR , D
LT_LIBRARIES
    ==> find library: eigen, And create variable: EIGEN_DIR , EIGEN_INCLUDE_DIR , EIGEN_FOUND 
    ==> find library: flann, And create variable: FLANN_DIR , FLANN_INCLUDE_DIR , FLANN_LIBRARY
_DIR , FLANN_LIBRARIES
    ==> find library: gflags, And create variable: GFLAGS_DIR , GFLAGS_INCLUDE_DIR , GFLAGS_LIB
RARY_DIR , GFLAGS_LIBRARIES
    ==> find library: gfortran, And create variable: GFORTRAN_DIR , GFORTRAN_INCLUDE_DIR , GFOR
TRAN_LIBRARY_DIR , GFORTRAN_LIBRARIES
    ==> find library: glog, And create variable: GLOG_DIR , GLOG_INCLUDE_DIR , GLOG_LIBRARY_DIR
 , GLOG_LIBRARIES
    ==> find library: ipopt, And create variable: IPOPT_DIR , IPOPT_INCLUDE_DIR , IPOPT_LIBRARY
_DIR , IPOPT_LIBRARIES
    ==> find library: lapack, And create variable: LAPACK_DIR , LAPACK_INCLUDE_DIR , LAPACK_LIB
RARY_DIR , LAPACK_LIBRARIES
    ==> find library: mosquitto, And create variable: MOSQUITTO_DIR , MOSQUITTO_INCLUDE_DIR , M
OSQUITTO_LIBRARY_DIR , MOSQUITTO_LIBRARIES
    ==> find library: opencv, And create variable: OPENCV_DIR , OPENCV_INCLUDE_DIR , OPENCV_LIB
RARY_DIR , OPENCV_LIBRARIES
    ==> find library: openssl, And create variable: OPENSSL_DIR , OPENSSL_INCLUDE_DIR , OPENSSL
_LIBRARY_DIR , OPENSSL_LIBRARIES
    ==> find library: osqp, And create variable: OSQP_DIR , OSQP_INCLUDE_DIR , OSQP_LIBRARY_DIR
 , OSQP_LIBRARIES
    ==> find library: pcl, And create variable: PCL_DIR , PCL_INCLUDE_DIR , PCL_LIBRARY_DIR , P
CL_LIBRARIES
    ==> find library: protobuf, And create variable: PROTOBUF_DIR , PROTOBUF_INCLUDE_DIR , PROT
OBUF_LIBRARY_DIR , PROTOBUF_LIBRARIES
    ==> find library: rs_driver, And create variable: RS_DRIVER_DIR , RS_DRIVER_INCLUDE_DIR , R
S_DRIVER_LIBRARY_DIR , RS_DRIVER_LIBRARIES
    ==> find library: yaml, And create variable: YAML_DIR , YAML_INCLUDE_DIR , YAML_LIBRARY_DIR
 , YAML_LIBRARIES
    ==> find library: autocalib, And create variable: AUTOCALIB_DIR , AUTOCALIB_INCLUDE_DIR , A
UTOCALIB_LIBRARY_DIR , AUTOCALIB_LIBRARIES
    ==> find library: components_and_data_center, And create variable: COMPONENTS_AND_DATA_CENT
ER_DIR , COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR , COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR , COMP
ONENTS_AND_DATA_CENTER_LIBRARIES
    ==> find library: control, And create variable: CONTROL_DIR , CONTROL_INCLUDE_DIR , CONTROL
_LIBRARY_DIR , CONTROL_LIBRARIES
    ==> find library: execution_manifest, And create variable: EXECUTION_MANIFEST_DIR , EXECUTI
ON_MANIFEST_INCLUDE_DIR , EXECUTION_MANIFEST_LIBRARY_DIR , EXECUTION_MANIFEST_LIBRARIES
    ==> find library: fusion_localization_cargovehicle, And create variable: FUSION_LOCALIZATIO
N_CARGOVEHICLE_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_INCLUDE_DIR , FUSION_LOCALIZATION_CARGOVE
HICLE_LIBRARY_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_LIBRARIES
    ==> find library: hlog, And create variable: HLOG_DIR , HLOG_INCLUDE_DIR , HLOG_LIBRARY_DIR
 , HLOG_LIBRARIES
    ==> find library: iceoryx, And create variable: ICEORYX_DIR , ICEORYX_INCLUDE_DIR , ICEORYX
_LIBRARY_DIR , ICEORYX_LIBRARIES
    ==> find library: lidar_obstacle_perception_detect, And create variable: LIDAR_OBSTACLE_PER
CEPTION_DETECT_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_D
ETECT_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_LIBRARIES
    ==> find library: lidar_obstacle_perception_fusion, And create variable: LIDAR_OBSTACLE_PER
CEPTION_FUSION_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_F
USION_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_LIBRARIES
    ==> find library: lidar_obstacle_perception_segment, And create variable: LIDAR_OBSTACLE_PE
RCEPTION_SEGMENT_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTIO
N_SEGMENT_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_LIBRARIES
    ==> find library: lidar_obstacle_perception_track, And create variable: LIDAR_OBSTACLE_PERC
EPTION_TRACK_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_TRAC
K_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_LIBRARIES
    ==> find library: map, And create variable: MAP_DIR , MAP_INCLUDE_DIR , MAP_LIBRARY_DIR , M
AP_LIBRARIES
    ==> find library: planning, And create variable: PLANNING_DIR , PLANNING_INCLUDE_DIR , PLAN
NING_LIBRARY_DIR , PLANNING_LIBRARIES
    ==> find library: road_boundary_perception_track, And create variable: ROAD_BOUNDARY_PERCEP
TION_TRACK_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LI
BRARY_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARIES
    ==> find library: vsomeip, And create variable: VSOMEIP_DIR , VSOMEIP_INCLUDE_DIR , VSOMEIP
_LIBRARY_DIR , VSOMEIP_LIBRARIES
CMake Warning at /hfr/3rdparty/opencv/x86_64/lib/cmake/opencv4/OpenCVConfig.cmake:116 (message)
:
  OpenCV: Include directory doesn't exist:
  '/hfr/3rdparty/opencv/x86_64_18/include/opencv4'.  OpenCV installation may
  be broken.  Skip...
Call Stack (most recent call first):
  CMakeLists.txt:26 (find_package)


-- Found OpenCV: /hfr/3rdparty/opencv/x86_64_18 (found suitable version "4.4.0", minimum requir
ed is "4")
-- OpenCV库版本: 4.4.0
-- OpenCV包括目录: 
-- OpenCV库: opencv_calib3d;opencv_core;opencv_dnn;opencv_features2d;opencv_flann;opencv_gapi;o
pencv_highgui;opencv_imgcodecs;opencv_imgproc;opencv_ml;opencv_objdetect;opencv_photo;opencv_st
itching;opencv_video;opencv_videoio
==> copy from: /hfr/3rdparty/dlt/x86_64/lib
==> copy to: /usr/lib/libdlt.so
==> copy to: /usr/lib/libdlt.so.2
==> copy to: /usr/lib/libdlt.so.2.18.9
-- SRC_DIR_LIST = /hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./kalman_fil
ter.cpp;/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./qr.cpp;/hfr/modules/
HAF/road_boundary_perception_track/core/private/RBT/./road_tracker.cpp;/hfr/modules/HAF/road_bo
undary_perception_track/core/private/RBT/./target.cpp;/hfr/modules/HAF/road_boundary_perception
_track/core/private/RBT/./tracked_road.cpp
-- Configuring done
-- Generating done
-- Build files have been written to: /hfr/modules/HAF/road_boundary_perception_track/core/priva
te/build
Scanning dependencies of target io_util_test
[ 10%] Building CXX object CMakeFiles/io_util_test.dir/test/io_util_test.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/io_util_test.cpp:9:10: fatal 
error: opencv2/opencv.hpp: No such file or directory
 #include "opencv2/opencv.hpp"
          ^~~~~~~~~~~~~~~~~~~~
compilation terminated.
CMakeFiles/io_util_test.dir/build.make:81: recipe for target 'CMakeFiles/io_util_test.dir/test/
io_util_test.cpp.o' failed
make[2]: *** [CMakeFiles/io_util_test.dir/test/io_util_test.cpp.o] Error 1
CMakeFiles/Makefile2:137: recipe for target 'CMakeFiles/io_util_test.dir/all' failed
make[1]: *** [CMakeFiles/io_util_test.dir/all] Error 2
Makefile:148: recipe for target 'all' failed
make: *** [all] Error 2

```

---

我们现在知道了问题所在，OpenCV的include目录不存在。我们需要再次修改CMakeLists.txt，使用正确的OpenCV相关路径：

---

```bash
ls -la /hfr/3rdparty/opencv/x86_64_18/include/
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# ls -
la /hfr/3rdparty/opencv/x86_64_18/include/
total 12
drwxrwxr-x  3 <USER> <GROUP> 4096 May 12 13:41 .
drwxrwxr-x  6 <USER> <GROUP> 4096 May 12 13:41 ..
drwxrwxr-x 17 <USER> <GROUP> 4096 May 12 13:41 opencv2
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build#
```

---

```diff
  include_directories(${EIGEN_INCLUDE_DIR})
  
- # 设置OpenCV_DIR以指向OpenCV CMake配置文件位置
- set(OpenCV_DIR "/hfr/3rdparty/opencv/x86_64/lib/cmake/opencv4")
- # 查找OpenCV库
- find_package(OpenCV 4 REQUIRED)
- message(STATUS "OpenCV库版本: ${OpenCV_VERSION}")
- message(STATUS "OpenCV包括目录: ${OpenCV_INCLUDE_DIRS}")
- message(STATUS "OpenCV库: ${OpenCV_LIBS}")
+ # 手动设置OpenCV相关路径
+ set(OpenCV_INCLUDE_DIRS "/hfr/3rdparty/opencv/x86_64_18/include")
+ set(OpenCV_LIBRARY_DIR "/hfr/3rdparty/opencv/x86_64_18/lib")
+ set(OpenCV_LIBS
+     opencv_calib3d
+     opencv_core
+     opencv_highgui
+     opencv_imgcodecs
+     opencv_imgproc
+     opencv_features2d
+     opencv_flann
+ )
  
- #OpenCV
+ # 包含OpenCV头文件目录
  include_directories(${OpenCV_INCLUDE_DIRS})
+ # 添加OpenCV库目录
+ link_directories(${OpenCV_LIBRARY_DIR})
  
  #dlt
```

---

```bash
rm -rf * && cmake .. && make
```

```
-- The C compiler identification is GNU 7.5.0
-- The CXX compiler identification is GNU 7.5.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
HBI_VERSION=1.39

Start glob configure cmake
Search Done: /hfr/lib

Start glob library
    ==> find library: adolc, And create variable: ADOLC_DIR , ADOLC_INCLUDE_DIR , ADOLC_LIBRARY
_DIR , ADOLC_LIBRARIES
    ==> find library: boost, And create variable: BOOST_DIR , BOOST_INCLUDE_DIR , BOOST_LIBRARY
_DIR , BOOST_LIBRARIES
    ==> find library: cjson, And create variable: CJSON_DIR , CJSON_INCLUDE_DIR , CJSON_LIBRARY
_DIR , CJSON_LIBRARIES
    ==> find library: colpack, And create variable: COLPACK_DIR , COLPACK_INCLUDE_DIR , COLPACK
_LIBRARY_DIR , COLPACK_LIBRARIES
    ==> find library: dlt, And create variable: DLT_DIR , DLT_INCLUDE_DIR , DLT_LIBRARY_DIR , D
LT_LIBRARIES
    ==> find library: eigen, And create variable: EIGEN_DIR , EIGEN_INCLUDE_DIR , EIGEN_FOUND 
    ==> find library: flann, And create variable: FLANN_DIR , FLANN_INCLUDE_DIR , FLANN_LIBRARY
_DIR , FLANN_LIBRARIES
    ==> find library: gflags, And create variable: GFLAGS_DIR , GFLAGS_INCLUDE_DIR , GFLAGS_LIB
RARY_DIR , GFLAGS_LIBRARIES
    ==> find library: gfortran, And create variable: GFORTRAN_DIR , GFORTRAN_INCLUDE_DIR , GFOR
TRAN_LIBRARY_DIR , GFORTRAN_LIBRARIES
    ==> find library: glog, And create variable: GLOG_DIR , GLOG_INCLUDE_DIR , GLOG_LIBRARY_DIR
 , GLOG_LIBRARIES
    ==> find library: ipopt, And create variable: IPOPT_DIR , IPOPT_INCLUDE_DIR , IPOPT_LIBRARY
_DIR , IPOPT_LIBRARIES
    ==> find library: lapack, And create variable: LAPACK_DIR , LAPACK_INCLUDE_DIR , LAPACK_LIB
RARY_DIR , LAPACK_LIBRARIES
    ==> find library: mosquitto, And create variable: MOSQUITTO_DIR , MOSQUITTO_INCLUDE_DIR , M
OSQUITTO_LIBRARY_DIR , MOSQUITTO_LIBRARIES
    ==> find library: opencv, And create variable: OPENCV_DIR , OPENCV_INCLUDE_DIR , OPENCV_LIB
RARY_DIR , OPENCV_LIBRARIES
    ==> find library: openssl, And create variable: OPENSSL_DIR , OPENSSL_INCLUDE_DIR , OPENSSL
_LIBRARY_DIR , OPENSSL_LIBRARIES
    ==> find library: osqp, And create variable: OSQP_DIR , OSQP_INCLUDE_DIR , OSQP_LIBRARY_DIR
 , OSQP_LIBRARIES
    ==> find library: pcl, And create variable: PCL_DIR , PCL_INCLUDE_DIR , PCL_LIBRARY_DIR , P
CL_LIBRARIES
    ==> find library: protobuf, And create variable: PROTOBUF_DIR , PROTOBUF_INCLUDE_DIR , PROT
OBUF_LIBRARY_DIR , PROTOBUF_LIBRARIES
    ==> find library: rs_driver, And create variable: RS_DRIVER_DIR , RS_DRIVER_INCLUDE_DIR , R
S_DRIVER_LIBRARY_DIR , RS_DRIVER_LIBRARIES
    ==> find library: yaml, And create variable: YAML_DIR , YAML_INCLUDE_DIR , YAML_LIBRARY_DIR
 , YAML_LIBRARIES
    ==> find library: autocalib, And create variable: AUTOCALIB_DIR , AUTOCALIB_INCLUDE_DIR , A
UTOCALIB_LIBRARY_DIR , AUTOCALIB_LIBRARIES
    ==> find library: components_and_data_center, And create variable: COMPONENTS_AND_DATA_CENT
ER_DIR , COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR , COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR , COMP
ONENTS_AND_DATA_CENTER_LIBRARIES
    ==> find library: control, And create variable: CONTROL_DIR , CONTROL_INCLUDE_DIR , CONTROL
_LIBRARY_DIR , CONTROL_LIBRARIES
    ==> find library: execution_manifest, And create variable: EXECUTION_MANIFEST_DIR , EXECUTI
ON_MANIFEST_INCLUDE_DIR , EXECUTION_MANIFEST_LIBRARY_DIR , EXECUTION_MANIFEST_LIBRARIES
    ==> find library: fusion_localization_cargovehicle, And create variable: FUSION_LOCALIZATIO
N_CARGOVEHICLE_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_INCLUDE_DIR , FUSION_LOCALIZATION_CARGOVE
HICLE_LIBRARY_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_LIBRARIES
    ==> find library: hlog, And create variable: HLOG_DIR , HLOG_INCLUDE_DIR , HLOG_LIBRARY_DIR
 , HLOG_LIBRARIES
    ==> find library: iceoryx, And create variable: ICEORYX_DIR , ICEORYX_INCLUDE_DIR , ICEORYX
_LIBRARY_DIR , ICEORYX_LIBRARIES
    ==> find library: lidar_obstacle_perception_detect, And create variable: LIDAR_OBSTACLE_PER
CEPTION_DETECT_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_D
ETECT_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_LIBRARIES
    ==> find library: lidar_obstacle_perception_fusion, And create variable: LIDAR_OBSTACLE_PER
CEPTION_FUSION_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_F
USION_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_LIBRARIES
    ==> find library: lidar_obstacle_perception_segment, And create variable: LIDAR_OBSTACLE_PE
RCEPTION_SEGMENT_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTIO
N_SEGMENT_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_LIBRARIES
    ==> find library: lidar_obstacle_perception_track, And create variable: LIDAR_OBSTACLE_PERC
EPTION_TRACK_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_TRAC
K_LIBRARY_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_LIBRARIES
    ==> find library: map, And create variable: MAP_DIR , MAP_INCLUDE_DIR , MAP_LIBRARY_DIR , M
AP_LIBRARIES
    ==> find library: planning, And create variable: PLANNING_DIR , PLANNING_INCLUDE_DIR , PLAN
NING_LIBRARY_DIR , PLANNING_LIBRARIES
    ==> find library: road_boundary_perception_track, And create variable: ROAD_BOUNDARY_PERCEP
TION_TRACK_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LI
BRARY_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARIES
    ==> find library: vsomeip, And create variable: VSOMEIP_DIR , VSOMEIP_INCLUDE_DIR , VSOMEIP
_LIBRARY_DIR , VSOMEIP_LIBRARIES
==> copy from: /hfr/3rdparty/dlt/x86_64/lib
==> copy to: /usr/lib/libdlt.so
==> copy to: /usr/lib/libdlt.so.2
==> copy to: /usr/lib/libdlt.so.2.18.9
-- SRC_DIR_LIST = /hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./kalman_fil
ter.cpp;/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./qr.cpp;/hfr/modules/
HAF/road_boundary_perception_track/core/private/RBT/./road_tracker.cpp;/hfr/modules/HAF/road_bo
undary_perception_track/core/private/RBT/./target.cpp;/hfr/modules/HAF/road_boundary_perception
_track/core/private/RBT/./tracked_road.cpp
-- Configuring done
-- Generating done
-- Build files have been written to: /hfr/modules/HAF/road_boundary_perception_track/core/priva
te/build
Scanning dependencies of target io_util_test
[ 10%] Building CXX object CMakeFiles/io_util_test.dir/test/io_util_test.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/io_util_test.cpp: In function
 ‘double GetTimestampByFilename(const string&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/io_util_test.cpp:80:23: warni
ng: comparison between signed and unsigned integer expressions [-Wsign-compare]
     if (underline_pos != file_name_without_extension.npos) {
[ 20%] Linking CXX executable io_util_test
[ 20%] Built target io_util_test
Scanning dependencies of target RBT
[ 30%] Building CXX object RBT/CMakeFiles/RBT.dir/kalman_filter.cpp.o
[ 40%] Building CXX object RBT/CMakeFiles/RBT.dir/qr.cpp.o
[ 50%] Building CXX object RBT/CMakeFiles/RBT.dir/road_tracker.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp: In static me
mber function ‘static void RoadTracker::ComputeHistogram(const PBI::RoadInfo*, double, std::map
<double, int>&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp:22:23: warnin
g: comparison between signed and unsigned integer expressions [-Wsign-compare]
     for (int i = 0; i < det->guardrail.num; ++i) {
                     ~~^~~~~~~~~~~~~~~~~~~~
[ 60%] Building CXX object RBT/CMakeFiles/RBT.dir/target.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp: In member function
 ‘bool Target::SortPoint(PBI::Guardrail&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp:275:1: warning: no 
return statement in function returning non-void [-Wreturn-type]
 }
 ^
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp: In member function
 ‘bool Target::InitKalmanFilter(const std::vector<Point2D>&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp:292:1: warning: no 
return statement in function returning non-void [-Wreturn-type]
 }
 ^
[ 70%] Building CXX object RBT/CMakeFiles/RBT.dir/tracked_road.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/tracked_road.cpp: In member fu
nction ‘void TrackedRoad::ComputeShapeFeatures(int, double, double)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/tracked_road.cpp:92:23: warnin
g: comparison between signed and unsigned integer expressions [-Wsign-compare]
     for (int i = 0; i < road_info.guardrail.num; ++i) {
                     ~~^~~~~~~~~~~~~~~~~~~~~~~~~
[ 80%] Linking CXX shared library libRBT.so
[ 80%] Built target RBT
Scanning dependencies of target rbt_test_node
[ 90%] Building CXX object CMakeFiles/rbt_test_node.dir/test/rbt_test.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘do
uble GetTimestampByFilename(const string&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:109:23: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
     if (underline_pos != file_name_without_extension.npos) {
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘vo
id SaveGuardrail(const string&, const PBI::RoadInfoArray*, const PBI::RoadInfoArray*)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:126:31: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:140:31: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘vo
id VisualizeGuardrail(const string&, const PBI::RoadInfoArray*, const PBI::RoadInfoArray*, cons
t PBI::LocalizationEstimate*)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:342:31: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:374:31: warning:
 comparison between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
[100%] Linking CXX executable rbt_test_node
[100%] Built target rbt_test_node

```

---

```bash
ls -la
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# ls -
la
total 1872
drwxr-xr-x  5 <USER>    <GROUP>       4096 May 14 15:51 .
drwxrwxr-x 12 <USER> <GROUP>    4096 May 14 15:48 ..
-rw-r--r--  1 <USER>    <GROUP>      17853 May 14 15:51 CMakeCache.txt
drwxr-xr-x  6 <USER>    <GROUP>       4096 May 14 15:51 CMakeFiles
-rw-r--r--  1 <USER>    <GROUP>       9477 May 14 15:51 Makefile
drwxr-xr-x  3 <USER>    <GROUP>       4096 May 14 15:51 RBT
-rw-r--r--  1 <USER>    <GROUP>       2380 May 14 15:51 cmake_install.cmake
drwxr-xr-x  3 <USER>    <GROUP>       4096 May 14 15:51 common
-rw-r--r--  1 <USER>    <GROUP>       4935 May 14 15:51 compile_commands.json
-rwxr-xr-x  1 <USER>    <GROUP>     553760 May 14 15:51 io_util_test
-rwxr-xr-x  1 <USER>    <GROUP>    1292328 May 14 15:51 rbt_test_node
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build#
```

---

```diff
  int start_frame = 0;    //start at a middle frame for debug
  int end_frame = 10000;    //end at a middle frame for debug
+ 
+ // 添加enable_predict_road到RoadTrackerInitOptions类
+ struct RoadTrackerInitOptions {
+     bool enable_predict_road = false;
+     // 其他现有成员保持不变
+     TrackedTargetParam target_param;
+ };
  
  bool PathExists(const std::string &path) {
```

---

```bash
rm -rf * && cmake .. && make
```

```
-- The C compiler identification is GNU 7.5.0
-- The CXX compiler identification is GNU 7.5.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
HBI_VERSION=1.39

Start glob configure cmake
Search Done: /hfr/lib

Start glob library
    ==> find library: adolc, And create variable: ADOLC_DIR , ADOLC_INCLUDE_DIR , ADOLC_LIBRARY_DIR , A
DOLC_LIBRARIES
    ==> find library: boost, And create variable: BOOST_DIR , BOOST_INCLUDE_DIR , BOOST_LIBRARY_DIR , B
OOST_LIBRARIES
    ==> find library: cjson, And create variable: CJSON_DIR , CJSON_INCLUDE_DIR , CJSON_LIBRARY_DIR , C
JSON_LIBRARIES
    ==> find library: colpack, And create variable: COLPACK_DIR , COLPACK_INCLUDE_DIR , COLPACK_LIBRARY
_DIR , COLPACK_LIBRARIES
    ==> find library: dlt, And create variable: DLT_DIR , DLT_INCLUDE_DIR , DLT_LIBRARY_DIR , DLT_LIBRA
RIES
    ==> find library: eigen, And create variable: EIGEN_DIR , EIGEN_INCLUDE_DIR , EIGEN_FOUND 
    ==> find library: flann, And create variable: FLANN_DIR , FLANN_INCLUDE_DIR , FLANN_LIBRARY_DIR , F
LANN_LIBRARIES
    ==> find library: gflags, And create variable: GFLAGS_DIR , GFLAGS_INCLUDE_DIR , GFLAGS_LIBRARY_DIR
 , GFLAGS_LIBRARIES
    ==> find library: gfortran, And create variable: GFORTRAN_DIR , GFORTRAN_INCLUDE_DIR , GFORTRAN_LIB
RARY_DIR , GFORTRAN_LIBRARIES
    ==> find library: glog, And create variable: GLOG_DIR , GLOG_INCLUDE_DIR , GLOG_LIBRARY_DIR , GLOG_
LIBRARIES
    ==> find library: ipopt, And create variable: IPOPT_DIR , IPOPT_INCLUDE_DIR , IPOPT_LIBRARY_DIR , I
POPT_LIBRARIES
    ==> find library: lapack, And create variable: LAPACK_DIR , LAPACK_INCLUDE_DIR , LAPACK_LIBRARY_DIR
 , LAPACK_LIBRARIES
    ==> find library: mosquitto, And create variable: MOSQUITTO_DIR , MOSQUITTO_INCLUDE_DIR , MOSQUITTO
_LIBRARY_DIR , MOSQUITTO_LIBRARIES
    ==> find library: opencv, And create variable: OPENCV_DIR , OPENCV_INCLUDE_DIR , OPENCV_LIBRARY_DIR
 , OPENCV_LIBRARIES
    ==> find library: openssl, And create variable: OPENSSL_DIR , OPENSSL_INCLUDE_DIR , OPENSSL_LIBRARY
_DIR , OPENSSL_LIBRARIES
    ==> find library: osqp, And create variable: OSQP_DIR , OSQP_INCLUDE_DIR , OSQP_LIBRARY_DIR , OSQP_
LIBRARIES
    ==> find library: pcl, And create variable: PCL_DIR , PCL_INCLUDE_DIR , PCL_LIBRARY_DIR , PCL_LIBRA
RIES
    ==> find library: protobuf, And create variable: PROTOBUF_DIR , PROTOBUF_INCLUDE_DIR , PROTOBUF_LIB
RARY_DIR , PROTOBUF_LIBRARIES
    ==> find library: rs_driver, And create variable: RS_DRIVER_DIR , RS_DRIVER_INCLUDE_DIR , RS_DRIVER
_LIBRARY_DIR , RS_DRIVER_LIBRARIES
    ==> find library: yaml, And create variable: YAML_DIR , YAML_INCLUDE_DIR , YAML_LIBRARY_DIR , YAML_
LIBRARIES
    ==> find library: autocalib, And create variable: AUTOCALIB_DIR , AUTOCALIB_INCLUDE_DIR , AUTOCALIB
_LIBRARY_DIR , AUTOCALIB_LIBRARIES
    ==> find library: components_and_data_center, And create variable: COMPONENTS_AND_DATA_CENTER_DIR ,
 COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR , COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR , COMPONENTS_AND_DATA_
CENTER_LIBRARIES
    ==> find library: control, And create variable: CONTROL_DIR , CONTROL_INCLUDE_DIR , CONTROL_LIBRARY
_DIR , CONTROL_LIBRARIES
    ==> find library: execution_manifest, And create variable: EXECUTION_MANIFEST_DIR , EXECUTION_MANIF
EST_INCLUDE_DIR , EXECUTION_MANIFEST_LIBRARY_DIR , EXECUTION_MANIFEST_LIBRARIES
    ==> find library: fusion_localization_cargovehicle, And create variable: FUSION_LOCALIZATION_CARGOV
EHICLE_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_INCLUDE_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_LIBRARY_DI
R , FUSION_LOCALIZATION_CARGOVEHICLE_LIBRARIES
    ==> find library: hlog, And create variable: HLOG_DIR , HLOG_INCLUDE_DIR , HLOG_LIBRARY_DIR , HLOG_
LIBRARIES
    ==> find library: iceoryx, And create variable: ICEORYX_DIR , ICEORYX_INCLUDE_DIR , ICEORYX_LIBRARY
_DIR , ICEORYX_LIBRARIES
    ==> find library: lidar_obstacle_perception_detect, And create variable: LIDAR_OBSTACLE_PERCEPTION_
DETECT_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_LIBRARY_DI
R , LIDAR_OBSTACLE_PERCEPTION_DETECT_LIBRARIES
    ==> find library: lidar_obstacle_perception_fusion, And create variable: LIDAR_OBSTACLE_PERCEPTION_
FUSION_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_LIBRARY_DI
R , LIDAR_OBSTACLE_PERCEPTION_FUSION_LIBRARIES
    ==> find library: lidar_obstacle_perception_segment, And create variable: LIDAR_OBSTACLE_PERCEPTION
_SEGMENT_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_LIBRAR
Y_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_LIBRARIES
    ==> find library: lidar_obstacle_perception_track, And create variable: LIDAR_OBSTACLE_PERCEPTION_T
RACK_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_LIBRARY_DIR ,
LIDAR_OBSTACLE_PERCEPTION_TRACK_LIBRARIES
    ==> find library: map, And create variable: MAP_DIR , MAP_INCLUDE_DIR , MAP_LIBRARY_DIR , MAP_LIBRA
RIES
    ==> find library: planning, And create variable: PLANNING_DIR , PLANNING_INCLUDE_DIR , PLANNING_LIB
RARY_DIR , PLANNING_LIBRARIES
    ==> find library: road_boundary_perception_track, And create variable: ROAD_BOUNDARY_PERCEPTION_TRA
CK_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARY_DIR , ROAD
_BOUNDARY_PERCEPTION_TRACK_LIBRARIES
    ==> find library: vsomeip, And create variable: VSOMEIP_DIR , VSOMEIP_INCLUDE_DIR , VSOMEIP_LIBRARY
_DIR , VSOMEIP_LIBRARIES
==> copy from: /hfr/3rdparty/dlt/x86_64/lib
==> copy to: /usr/lib/libdlt.so
==> copy to: /usr/lib/libdlt.so.2
==> copy to: /usr/lib/libdlt.so.2.18.9
-- SRC_DIR_LIST = /hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./kalman_filter.cpp;
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./qr.cpp;/hfr/modules/HAF/road_boundar
y_perception_track/core/private/RBT/./road_tracker.cpp;/hfr/modules/HAF/road_boundary_perception_track/
core/private/RBT/./target.cpp;/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./tracke
d_road.cpp
-- Configuring done
-- Generating done
-- Build files have been written to: /hfr/modules/HAF/road_boundary_perception_track/core/private/build
Scanning dependencies of target io_util_test
[ 10%] Building CXX object CMakeFiles/io_util_test.dir/test/io_util_test.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/io_util_test.cpp: In function ‘double
 GetTimestampByFilename(const string&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/io_util_test.cpp:80:23: warning: comp
arison between signed and unsigned integer expressions [-Wsign-compare]
     if (underline_pos != file_name_without_extension.npos) {
[ 20%] Linking CXX executable io_util_test
[ 20%] Built target io_util_test
Scanning dependencies of target RBT
[ 30%] Building CXX object RBT/CMakeFiles/RBT.dir/kalman_filter.cpp.o
[ 40%] Building CXX object RBT/CMakeFiles/RBT.dir/qr.cpp.o
[ 50%] Building CXX object RBT/CMakeFiles/RBT.dir/road_tracker.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp: In static member fun
ction ‘static void RoadTracker::ComputeHistogram(const PBI::RoadInfo*, double, std::map<double, int>&)’
:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp:22:23: warning: compa
rison between signed and unsigned integer expressions [-Wsign-compare]
     for (int i = 0; i < det->guardrail.num; ++i) {
                     ~~^~~~~~~~~~~~~~~~~~~~
[ 60%] Building CXX object RBT/CMakeFiles/RBT.dir/target.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp: In member function ‘bool T
arget::SortPoint(PBI::Guardrail&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp:275:1: warning: no return s
tatement in function returning non-void [-Wreturn-type]
 }
 ^
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp: In member function ‘bool T
arget::InitKalmanFilter(const std::vector<Point2D>&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp:292:1: warning: no return s
tatement in function returning non-void [-Wreturn-type]
 }
 ^
[ 70%] Building CXX object RBT/CMakeFiles/RBT.dir/tracked_road.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/tracked_road.cpp: In member function ‘
void TrackedRoad::ComputeShapeFeatures(int, double, double)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/tracked_road.cpp:92:23: warning: compa
rison between signed and unsigned integer expressions [-Wsign-compare]
     for (int i = 0; i < road_info.guardrail.num; ++i) {
                     ~~^~~~~~~~~~~~~~~~~~~~~~~~~
[ 80%] Linking CXX shared library libRBT.so
[ 80%] Built target RBT
Scanning dependencies of target rbt_test_node
[ 90%] Building CXX object CMakeFiles/rbt_test_node.dir/test/rbt_test.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:27:8: error: redefinitio
n of ‘struct RoadTrackerInitOptions’
 struct RoadTrackerInitOptions {
        ^~~~~~~~~~~~~~~~~~~~~~
In file included from /hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:13
:0:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.h:11:20: note: previous d
efinition of ‘struct RoadTrackerInitOptions’
 struct alignas(16) RoadTrackerInitOptions {
                    ^~~~~~~~~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘double Get
TimestampByFilename(const string&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:116:23: warning: compari
son between signed and unsigned integer expressions [-Wsign-compare]
     if (underline_pos != file_name_without_extension.npos) {
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘void SaveG
uardrail(const string&, const PBI::RoadInfoArray*, const PBI::RoadInfoArray*)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:133:31: warning: compari
son between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:147:31: warning: compari
son between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘void Visua
lizeGuardrail(const string&, const PBI::RoadInfoArray*, const PBI::RoadInfoArray*, const PBI::Localizat
ionEstimate*)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:349:31: warning: compari
son between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:381:31: warning: compari
son between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
CMakeFiles/rbt_test_node.dir/build.make:81: recipe for target 'CMakeFiles/rbt_test_node.dir/test/rbt_te
st.cpp.o' failed
make[2]: *** [CMakeFiles/rbt_test_node.dir/test/rbt_test.cpp.o] Error 1
CMakeFiles/Makefile2:164: recipe for target 'CMakeFiles/rbt_test_node.dir/all' failed
make[1]: *** [CMakeFiles/rbt_test_node.dir/all] Error 2
Makefile:148: recipe for target 'all' failed
make: *** [all] Error 2

```

---

看起来RoadTrackerInitOptions已经在road_tracker.h中定义了，但是缺少enable_predict_road成员变量。我们需要检查road_tracker.h文件，然后修改它。

---

Read file: modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.h

---



---

我需要使用更具体的方式来修改文件：

---



---

```bash
rm -rf * && cmake .. && make
```

```
-- The C compiler identification is GNU 7.5.0
-- The CXX compiler identification is GNU 7.5.0
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
HBI_VERSION=1.39

Start glob configure cmake
Search Done: /hfr/lib

Start glob library
    ==> find library: adolc, And create variable: ADOLC_DIR , ADOLC_INCLUDE_DIR , ADOLC_LIBRARY_DIR , A
DOLC_LIBRARIES
    ==> find library: boost, And create variable: BOOST_DIR , BOOST_INCLUDE_DIR , BOOST_LIBRARY_DIR , B
OOST_LIBRARIES
    ==> find library: cjson, And create variable: CJSON_DIR , CJSON_INCLUDE_DIR , CJSON_LIBRARY_DIR , C
JSON_LIBRARIES
    ==> find library: colpack, And create variable: COLPACK_DIR , COLPACK_INCLUDE_DIR , COLPACK_LIBRARY
_DIR , COLPACK_LIBRARIES
    ==> find library: dlt, And create variable: DLT_DIR , DLT_INCLUDE_DIR , DLT_LIBRARY_DIR , DLT_LIBRA
RIES
    ==> find library: eigen, And create variable: EIGEN_DIR , EIGEN_INCLUDE_DIR , EIGEN_FOUND 
    ==> find library: flann, And create variable: FLANN_DIR , FLANN_INCLUDE_DIR , FLANN_LIBRARY_DIR , F
LANN_LIBRARIES
    ==> find library: gflags, And create variable: GFLAGS_DIR , GFLAGS_INCLUDE_DIR , GFLAGS_LIBRARY_DIR
 , GFLAGS_LIBRARIES
    ==> find library: gfortran, And create variable: GFORTRAN_DIR , GFORTRAN_INCLUDE_DIR , GFORTRAN_LIB
RARY_DIR , GFORTRAN_LIBRARIES
    ==> find library: glog, And create variable: GLOG_DIR , GLOG_INCLUDE_DIR , GLOG_LIBRARY_DIR , GLOG_
LIBRARIES
    ==> find library: ipopt, And create variable: IPOPT_DIR , IPOPT_INCLUDE_DIR , IPOPT_LIBRARY_DIR , I
POPT_LIBRARIES
    ==> find library: lapack, And create variable: LAPACK_DIR , LAPACK_INCLUDE_DIR , LAPACK_LIBRARY_DIR
 , LAPACK_LIBRARIES
    ==> find library: mosquitto, And create variable: MOSQUITTO_DIR , MOSQUITTO_INCLUDE_DIR , MOSQUITTO
_LIBRARY_DIR , MOSQUITTO_LIBRARIES
    ==> find library: opencv, And create variable: OPENCV_DIR , OPENCV_INCLUDE_DIR , OPENCV_LIBRARY_DIR
 , OPENCV_LIBRARIES
    ==> find library: openssl, And create variable: OPENSSL_DIR , OPENSSL_INCLUDE_DIR , OPENSSL_LIBRARY
_DIR , OPENSSL_LIBRARIES
    ==> find library: osqp, And create variable: OSQP_DIR , OSQP_INCLUDE_DIR , OSQP_LIBRARY_DIR , OSQP_
LIBRARIES
    ==> find library: pcl, And create variable: PCL_DIR , PCL_INCLUDE_DIR , PCL_LIBRARY_DIR , PCL_LIBRA
RIES
    ==> find library: protobuf, And create variable: PROTOBUF_DIR , PROTOBUF_INCLUDE_DIR , PROTOBUF_LIB
RARY_DIR , PROTOBUF_LIBRARIES
    ==> find library: rs_driver, And create variable: RS_DRIVER_DIR , RS_DRIVER_INCLUDE_DIR , RS_DRIVER
_LIBRARY_DIR , RS_DRIVER_LIBRARIES
    ==> find library: yaml, And create variable: YAML_DIR , YAML_INCLUDE_DIR , YAML_LIBRARY_DIR , YAML_
LIBRARIES
    ==> find library: autocalib, And create variable: AUTOCALIB_DIR , AUTOCALIB_INCLUDE_DIR , AUTOCALIB
_LIBRARY_DIR , AUTOCALIB_LIBRARIES
    ==> find library: components_and_data_center, And create variable: COMPONENTS_AND_DATA_CENTER_DIR ,
 COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR , COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR , COMPONENTS_AND_DATA_
CENTER_LIBRARIES
    ==> find library: control, And create variable: CONTROL_DIR , CONTROL_INCLUDE_DIR , CONTROL_LIBRARY
_DIR , CONTROL_LIBRARIES
    ==> find library: execution_manifest, And create variable: EXECUTION_MANIFEST_DIR , EXECUTION_MANIF
EST_INCLUDE_DIR , EXECUTION_MANIFEST_LIBRARY_DIR , EXECUTION_MANIFEST_LIBRARIES
    ==> find library: fusion_localization_cargovehicle, And create variable: FUSION_LOCALIZATION_CARGOV
EHICLE_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_INCLUDE_DIR , FUSION_LOCALIZATION_CARGOVEHICLE_LIBRARY_DI
R , FUSION_LOCALIZATION_CARGOVEHICLE_LIBRARIES
    ==> find library: hlog, And create variable: HLOG_DIR , HLOG_INCLUDE_DIR , HLOG_LIBRARY_DIR , HLOG_
LIBRARIES
    ==> find library: iceoryx, And create variable: ICEORYX_DIR , ICEORYX_INCLUDE_DIR , ICEORYX_LIBRARY
_DIR , ICEORYX_LIBRARIES
    ==> find library: lidar_obstacle_perception_detect, And create variable: LIDAR_OBSTACLE_PERCEPTION_
DETECT_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_DETECT_LIBRARY_DI
R , LIDAR_OBSTACLE_PERCEPTION_DETECT_LIBRARIES
    ==> find library: lidar_obstacle_perception_fusion, And create variable: LIDAR_OBSTACLE_PERCEPTION_
FUSION_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_FUSION_LIBRARY_DI
R , LIDAR_OBSTACLE_PERCEPTION_FUSION_LIBRARIES
    ==> find library: lidar_obstacle_perception_segment, And create variable: LIDAR_OBSTACLE_PERCEPTION
_SEGMENT_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_LIBRAR
Y_DIR , LIDAR_OBSTACLE_PERCEPTION_SEGMENT_LIBRARIES
    ==> find library: lidar_obstacle_perception_track, And create variable: LIDAR_OBSTACLE_PERCEPTION_T
RACK_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_INCLUDE_DIR , LIDAR_OBSTACLE_PERCEPTION_TRACK_LIBRARY_DIR ,
LIDAR_OBSTACLE_PERCEPTION_TRACK_LIBRARIES
    ==> find library: map, And create variable: MAP_DIR , MAP_INCLUDE_DIR , MAP_LIBRARY_DIR , MAP_LIBRA
RIES
    ==> find library: planning, And create variable: PLANNING_DIR , PLANNING_INCLUDE_DIR , PLANNING_LIB
RARY_DIR , PLANNING_LIBRARIES
    ==> find library: road_boundary_perception_track, And create variable: ROAD_BOUNDARY_PERCEPTION_TRA
CK_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_INCLUDE_DIR , ROAD_BOUNDARY_PERCEPTION_TRACK_LIBRARY_DIR , ROAD
_BOUNDARY_PERCEPTION_TRACK_LIBRARIES
    ==> find library: vsomeip, And create variable: VSOMEIP_DIR , VSOMEIP_INCLUDE_DIR , VSOMEIP_LIBRARY
_DIR , VSOMEIP_LIBRARIES
==> copy from: /hfr/3rdparty/dlt/x86_64/lib
==> copy to: /usr/lib/libdlt.so
==> copy to: /usr/lib/libdlt.so.2
==> copy to: /usr/lib/libdlt.so.2.18.9
-- SRC_DIR_LIST = /hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./kalman_filter.cpp;
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./qr.cpp;/hfr/modules/HAF/road_boundar
y_perception_track/core/private/RBT/./road_tracker.cpp;/hfr/modules/HAF/road_boundary_perception_track/
core/private/RBT/./target.cpp;/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/./tracke
d_road.cpp
-- Configuring done
-- Generating done
-- Build files have been written to: /hfr/modules/HAF/road_boundary_perception_track/core/private/build
Scanning dependencies of target io_util_test
[ 10%] Building CXX object CMakeFiles/io_util_test.dir/test/io_util_test.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/io_util_test.cpp: In function ‘double
 GetTimestampByFilename(const string&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/io_util_test.cpp:80:23: warning: comp
arison between signed and unsigned integer expressions [-Wsign-compare]
     if (underline_pos != file_name_without_extension.npos) {
[ 20%] Linking CXX executable io_util_test
[ 20%] Built target io_util_test
Scanning dependencies of target RBT
[ 30%] Building CXX object RBT/CMakeFiles/RBT.dir/kalman_filter.cpp.o
[ 40%] Building CXX object RBT/CMakeFiles/RBT.dir/qr.cpp.o
[ 50%] Building CXX object RBT/CMakeFiles/RBT.dir/road_tracker.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp: In static member fun
ction ‘static void RoadTracker::ComputeHistogram(const PBI::RoadInfo*, double, std::map<double, int>&)’
:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/road_tracker.cpp:22:23: warning: compa
rison between signed and unsigned integer expressions [-Wsign-compare]
     for (int i = 0; i < det->guardrail.num; ++i) {
                     ~~^~~~~~~~~~~~~~~~~~~~
[ 60%] Building CXX object RBT/CMakeFiles/RBT.dir/target.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp: In member function ‘bool T
arget::SortPoint(PBI::Guardrail&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp:275:1: warning: no return s
tatement in function returning non-void [-Wreturn-type]
 }
 ^
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp: In member function ‘bool T
arget::InitKalmanFilter(const std::vector<Point2D>&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/target.cpp:292:1: warning: no return s
tatement in function returning non-void [-Wreturn-type]
 }
 ^
[ 70%] Building CXX object RBT/CMakeFiles/RBT.dir/tracked_road.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/tracked_road.cpp: In member function ‘
void TrackedRoad::ComputeShapeFeatures(int, double, double)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/RBT/tracked_road.cpp:92:23: warning: compa
rison between signed and unsigned integer expressions [-Wsign-compare]
     for (int i = 0; i < road_info.guardrail.num; ++i) {
                     ~~^~~~~~~~~~~~~~~~~~~~~~~~~
[ 80%] Linking CXX shared library libRBT.so
[ 80%] Built target RBT
Scanning dependencies of target rbt_test_node
[ 90%] Building CXX object CMakeFiles/rbt_test_node.dir/test/rbt_test.cpp.o
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘double Get
TimestampByFilename(const string&)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:109:23: warning: compari
son between signed and unsigned integer expressions [-Wsign-compare]
     if (underline_pos != file_name_without_extension.npos) {
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘void SaveG
uardrail(const string&, const PBI::RoadInfoArray*, const PBI::RoadInfoArray*)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:126:31: warning: compari
son between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:140:31: warning: compari
son between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp: In function ‘void Visua
lizeGuardrail(const string&, const PBI::RoadInfoArray*, const PBI::RoadInfoArray*, const PBI::Localizat
ionEstimate*)’:
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:342:31: warning: compari
son between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
/hfr/modules/HAF/road_boundary_perception_track/core/private/test/rbt_test.cpp:374:31: warning: compari
son between signed and unsigned integer expressions [-Wsign-compare]
             for (int j = 0; j < guardrail.num; ++j) {
                             ~~^~~~~~~~~~~~~~~
[100%] Linking CXX executable rbt_test_node
[100%] Built target rbt_test_node

```

---

```bash
./rbt_test_node
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# 

Index:1355 File:1708844514_516262.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844514_516262.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844514_516262.png

Index:1356 File:1708844514_588585.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844514_588585.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844514_588585.png

Index:1357 File:1708844514_660074.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844514_660074.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844514_660074.png

Index:1358 File:1708844514_732340.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844514_732340.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844514_732340.png

Index:1359 File:1708844514_802987.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844514_802987.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844514_802987.png

Index:1360 File:1708844514_876952.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844514_876952.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844514_876952.png

Index:1361 File:1708844514_948911.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844514_948911.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844514_948911.png

Index:1362 File:1708844515_020123.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_020123.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_020123.png

Index:1363 File:1708844515_092130.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_092130.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_092130.png

Index:1364 File:1708844515_164398.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_164398.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_164398.png

Index:1365 File:1708844515_237471.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_237471.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_237471.png

Index:1366 File:1708844515_309115.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_309115.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_309115.png

Index:1367 File:1708844515_381566.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_381566.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_381566.png

Index:1368 File:1708844515_453049.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_453049.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_453049.png

Index:1369 File:1708844515_525053.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_525053.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_525053.png

Index:1370 File:1708844515_598079.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_598079.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_598079.png

Index:1371 File:1708844515_670964.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_670964.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_670964.png

Index:1372 File:1708844515_742513.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_742513.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_742513.png

Index:1373 File:1708844515_815433.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_815433.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_815433.png

Index:1374 File:1708844515_886800.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_886800.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_886800.png

Index:1375 File:1708844515_958506.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844515_958506.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844515_958506.png

Index:1376 File:1708844516_030510.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844516_030510.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844516_030510.png

Index:1377 File:1708844516_103624.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844516_103624.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844516_103624.png

Index:1378 File:1708844516_176163.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844516_176163.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844516_176163.png

Index:1379 File:1708844516_247749.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844516_247749.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844516_247749.png

Index:1380 File:1708844516_464539.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844516_464539.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844516_464539.png

Index:1381 File:1708844516_537350.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844516_537350.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844516_537350.png

Index:1382 File:1708844516_609355.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844516_609355.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844516_609355.png

Index:1383 File:1708844516_680549.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844516_680549.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844516_680549.png

Index:1384 File:1708844516_752553.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844516_752553.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844516_752553.png

Index:1385 File:1708844516_825726.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844516_825726.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844516_825726.png

Index:1386 File:1708844516_898013.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844516_898013.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844516_898013.png

Index:1387 File:1708844516_968396.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844516_968396.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844516_968396.png

Index:1388 File:1708844517_040849.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_040849.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_040849.png

Index:1389 File:1708844517_112919.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_112919.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_112919.png

Index:1390 File:1708844517_185637.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_185637.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_185637.png

Index:1391 File:1708844517_257347.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_257347.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_257347.png

Index:1392 File:1708844517_331113.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_331113.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_331113.png

Index:1393 File:1708844517_401483.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_401483.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_401483.png

Index:1394 File:1708844517_473139.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_473139.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_473139.png

Index:1395 File:1708844517_545928.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_545928.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_545928.png

Index:1396 File:1708844517_617912.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_617912.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_617912.png

Index:1397 File:1708844517_691084.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_691084.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_691084.png

Index:1398 File:1708844517_763965.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_763965.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_763965.png

Index:1399 File:1708844517_835345.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_835345.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_835345.png

Index:1400 File:1708844517_906742.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_906742.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_906742.png

Index:1401 File:1708844517_980129.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844517_980129.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844517_980129.png

Index:1402 File:1708844518_051572.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_051572.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_051572.png

Index:1403 File:1708844518_124446.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_124446.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_124446.png

Index:1404 File:1708844518_196327.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_196327.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_196327.png

Index:1405 File:1708844518_267683.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_267683.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_267683.png

Index:1406 File:1708844518_339825.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_339825.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_339825.png

Index:1407 File:1708844518_411807.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_411807.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_411807.png

Index:1408 File:1708844518_484711.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_484711.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_484711.png

Index:1409 File:1708844518_556690.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_556690.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_556690.png

Index:1410 File:1708844518_629810.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_629810.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_629810.png

Index:1411 File:1708844518_700595.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_700595.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_700595.png

Index:1412 File:1708844518_774150.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_774150.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_774150.png

Index:1413 File:1708844518_845211.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_845211.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_845211.png

Index:1414 File:1708844518_917981.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_917981.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_917981.png

Index:1415 File:1708844518_989110.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844518_989110.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844518_989110.png

Index:1416 File:1708844519_060264.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_060264.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_060264.png

Index:1417 File:1708844519_134125.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_134125.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_134125.png

Index:1418 File:1708844519_206419.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_206419.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_206419.png

Index:1419 File:1708844519_279008.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_279008.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_279008.png

Index:1420 File:1708844519_350868.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_350868.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_350868.png

Index:1421 File:1708844519_421717.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_421717.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_421717.png

Index:1422 File:1708844519_493667.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_493667.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_493667.png

Index:1423 File:1708844519_567749.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_567749.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_567749.png

Index:1424 File:1708844519_639924.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_639924.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_639924.png

Index:1425 File:1708844519_710956.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_710956.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_710956.png

Index:1426 File:1708844519_784523.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_784523.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_784523.png

Index:1427 File:1708844519_855198.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_855198.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_855198.png

Index:1428 File:1708844519_928062.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844519_928062.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844519_928062.png

Index:1429 File:1708844520_000795.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_000795.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_000795.png

Index:1430 File:1708844520_072008.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_072008.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_072008.png

Index:1431 File:1708844520_145078.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_145078.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_145078.png

Index:1432 File:1708844520_216208.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_216208.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_216208.png

Index:1433 File:1708844520_288008.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_288008.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_288008.png

Index:1434 File:1708844520_361002.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_361002.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_361002.png

Index:1435 File:1708844520_433328.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_433328.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_433328.png

Index:1436 File:1708844520_505464.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_505464.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_505464.png

Index:1437 File:1708844520_577180.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_577180.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_577180.png

Index:1438 File:1708844520_648081.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_648081.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_648081.png

Index:1439 File:1708844520_719923.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_719923.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_719923.png

Index:1440 File:1708844520_794088.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_794088.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_794088.png

Index:1441 File:1708844520_867114.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_867114.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_867114.png

Index:1442 File:1708844520_937942.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844520_937942.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844520_937942.png

Index:1443 File:1708844521_011395.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_011395.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_011395.png

Index:1444 File:1708844521_081314.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_081314.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_081314.png

Index:1445 File:1708844521_154897.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_154897.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_154897.png

Index:1446 File:1708844521_227302.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_227302.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_227302.png

Index:1447 File:1708844521_299534.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_299534.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_299534.png

Index:1448 File:1708844521_371237.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_371237.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_371237.png

Index:1449 File:1708844521_442255.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_442255.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_442255.png

Index:1450 File:1708844521_517516.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_517516.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_517516.png

Index:1451 File:1708844521_587808.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_587808.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_587808.png

Index:1452 File:1708844521_661391.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_661391.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_661391.png

Index:1453 File:1708844521_732911.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_732911.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_732911.png

Index:1454 File:1708844521_803729.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_803729.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_803729.png

Index:1455 File:1708844521_876777.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_876777.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_876777.png

Index:1456 File:1708844521_949244.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844521_949244.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844521_949244.png

Index:1457 File:1708844522_021839.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_021839.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_021839.png

Index:1458 File:1708844522_093960.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_093960.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_093960.png

Index:1459 File:1708844522_238560.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_238560.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_238560.png

Index:1460 File:1708844522_310200.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_310200.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_310200.png

Index:1461 File:1708844522_382648.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_382648.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_382648.png

Index:1462 File:1708844522_454292.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_454292.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_454292.png

Index:1463 File:1708844522_525468.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_525468.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_525468.png

Index:1464 File:1708844522_598049.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_598049.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_598049.png

Index:1465 File:1708844522_670943.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_670943.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_670943.png

Index:1466 File:1708844522_742543.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_742543.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_742543.png

Index:1467 File:1708844522_815332.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_815332.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_815332.png

Index:1468 File:1708844522_887207.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_887207.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_887207.png

Index:1469 File:1708844522_959691.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844522_959691.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844522_959691.png

Index:1470 File:1708844523_031389.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_031389.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_031389.png

Index:1471 File:1708844523_103530.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_103530.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_103530.png

Index:1472 File:1708844523_175102.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_175102.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_175102.png

Index:1473 File:1708844523_247510.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_247510.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_247510.png

Index:1474 File:1708844523_320721.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_320721.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_320721.png

Index:1475 File:1708844523_392127.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_392127.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_392127.png

Index:1476 File:1708844523_464378.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_464378.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_464378.png

Index:1477 File:1708844523_537061.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_537061.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_537061.png

Index:1478 File:1708844523_609028.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_609028.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_609028.png

Index:1479 File:1708844523_681678.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_681678.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_681678.png

Index:1480 File:1708844523_752833.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_752833.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_752833.png

Index:1481 File:1708844523_826850.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_826850.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_826850.png

Index:1482 File:1708844523_897767.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_897767.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_897767.png

Index:1483 File:1708844523_969558.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844523_969558.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844523_969558.png

Index:1484 File:1708844524_043440.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_043440.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_043440.png

Index:1485 File:1708844524_114701.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_114701.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_114701.png

Index:1486 File:1708844524_187161.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_187161.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_187161.png

Index:1487 File:1708844524_258249.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_258249.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_258249.png

Index:1488 File:1708844524_329906.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_329906.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_329906.png

Index:1489 File:1708844524_402259.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_402259.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_402259.png

Index:1490 File:1708844524_474491.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_474491.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_474491.png

Index:1491 File:1708844524_619948.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_619948.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_619948.png

Index:1492 File:1708844524_692581.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_692581.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_692581.png

Index:1493 File:1708844524_763995.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_763995.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_763995.png

Index:1494 File:1708844524_836633.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_836633.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_836633.png

Index:1495 File:1708844524_909192.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_909192.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_909192.png

Index:1496 File:1708844524_979780.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844524_979780.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844524_979780.png

Index:1497 File:1708844525_052314.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_052314.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_052314.png

Index:1498 File:1708844525_124914.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_124914.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_124914.png

Index:1499 File:1708844525_196748.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_196748.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_196748.png

Index:1500 File:1708844525_268094.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_268094.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_268094.png

Index:1501 File:1708844525_340353.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_340353.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_340353.png

Index:1502 File:1708844525_412601.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_412601.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_412601.png

Index:1503 File:1708844525_485456.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_485456.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_485456.png

Index:1504 File:1708844525_557483.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_557483.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_557483.png

Index:1505 File:1708844525_629629.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_629629.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_629629.png

Index:1506 File:1708844525_702289.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_702289.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_702289.png

Index:1507 File:1708844525_773867.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_773867.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_773867.png

Index:1508 File:1708844525_846684.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_846684.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_846684.png

Index:1509 File:1708844525_919141.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_919141.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_919141.png

Index:1510 File:1708844525_990844.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844525_990844.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844525_990844.png

Index:1511 File:1708844526_062678.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_062678.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_062678.png

Index:1512 File:1708844526_136318.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_136318.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_136318.png

Index:1513 File:1708844526_206056.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_206056.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_206056.png

Index:1514 File:1708844526_280626.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_280626.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_280626.png

Index:1515 File:1708844526_352009.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_352009.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_352009.png

Index:1516 File:1708844526_423339.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_423339.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_423339.png

Index:1517 File:1708844526_493832.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_493832.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_493832.png

Index:1518 File:1708844526_568163.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_568163.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_568163.png

Index:1519 File:1708844526_641240.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_641240.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_641240.png

Index:1520 File:1708844526_713105.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_713105.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_713105.png

Index:1521 File:1708844526_786243.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_786243.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_786243.png

Index:1522 File:1708844526_856940.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_856940.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_856940.png

Index:1523 File:1708844526_929395.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844526_929395.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844526_929395.png

Index:1524 File:1708844527_001900.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_001900.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_001900.png

Index:1525 File:1708844527_072841.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_072841.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_072841.png

Index:1526 File:1708844527_146927.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_146927.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_146927.png

Index:1527 File:1708844527_219055.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_219055.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_219055.png

Index:1528 File:1708844527_291022.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_291022.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_291022.png

Index:1529 File:1708844527_360142.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_360142.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_360142.png

Index:1530 File:1708844527_434439.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_434439.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_434439.png

Index:1531 File:1708844527_506071.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_506071.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_506071.png

Index:1532 File:1708844527_578395.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_578395.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_578395.png

Index:1533 File:1708844527_650308.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_650308.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_650308.png

Index:1534 File:1708844527_723347.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_723347.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_723347.png

Index:1535 File:1708844527_795553.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_795553.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_795553.png

Index:1536 File:1708844527_868050.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_868050.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_868050.png

Index:1537 File:1708844527_939341.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844527_939341.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844527_939341.png

Index:1538 File:1708844528_010856.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_010856.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_010856.png

Index:1539 File:1708844528_083394.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_083394.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_083394.png

Index:1540 File:1708844528_156048.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_156048.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_156048.png

Index:1541 File:1708844528_228766.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_228766.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_228766.png

Index:1542 File:1708844528_300619.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_300619.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_300619.png

Index:1543 File:1708844528_373554.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_373554.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_373554.png

Index:1544 File:1708844528_443971.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_443971.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_443971.png

Index:1545 File:1708844528_518035.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_518035.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_518035.png

Index:1546 File:1708844528_589051.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_589051.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_589051.png

Index:1547 File:1708844528_661399.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_661399.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_661399.png

Index:1548 File:1708844528_733747.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_733747.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_733747.png

Index:1549 File:1708844528_805260.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_805260.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_805260.png

Index:1550 File:1708844528_877742.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_877742.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_877742.png

Index:1551 File:1708844528_949467.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844528_949467.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844528_949467.png

Index:1552 File:1708844529_021386.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_021386.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_021386.png

Index:1553 File:1708844529_094162.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_094162.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_094162.png

Index:1554 File:1708844529_167227.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_167227.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_167227.png

Index:1555 File:1708844529_238626.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_238626.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_238626.png

Index:1556 File:1708844529_308031.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_308031.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_308031.png

Index:1557 File:1708844529_382129.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_382129.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_382129.png

Index:1558 File:1708844529_455428.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_455428.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_455428.png

Index:1559 File:1708844529_527034.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_527034.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_527034.png

Index:1560 File:1708844529_599029.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_599029.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_599029.png

Index:1561 File:1708844529_670925.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_670925.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_670925.png

Index:1562 File:1708844529_743555.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_743555.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_743555.png

Index:1563 File:1708844529_815516.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_815516.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_815516.png

Index:1564 File:1708844529_888125.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_888125.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_888125.png

Index:1565 File:1708844529_960017.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844529_960017.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844529_960017.png

Index:1566 File:1708844530_032107.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_032107.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_032107.png

Index:1567 File:1708844530_104092.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_104092.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_104092.png

Index:1568 File:1708844530_176322.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_176322.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_176322.png

Index:1569 File:1708844530_248017.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_248017.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_248017.png

Index:1570 File:1708844530_321415.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_321415.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_321415.png

Index:1571 File:1708844530_393320.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_393320.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_393320.png

Index:1572 File:1708844530_464912.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_464912.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_464912.png

Index:1573 File:1708844530_537056.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_537056.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_537056.png

Index:1574 File:1708844530_609066.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_609066.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_609066.png

Index:1575 File:1708844530_681334.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_681334.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_681334.png

Index:1576 File:1708844530_754383.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_754383.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_754383.png

Index:1577 File:1708844530_826224.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_826224.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_826224.png

Index:1578 File:1708844530_898230.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_898230.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_898230.png

Index:1579 File:1708844530_971541.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844530_971541.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844530_971541.png

Index:1580 File:1708844531_042026.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_042026.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_042026.png

Index:1581 File:1708844531_113956.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_113956.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_113956.png

Index:1582 File:1708844531_187739.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_187739.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_187739.png

Index:1583 File:1708844531_258693.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_258693.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_258693.png

Index:1584 File:1708844531_331643.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_331643.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_331643.png

Index:1585 File:1708844531_404153.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_404153.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_404153.png

Index:1586 File:1708844531_474652.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_474652.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_474652.png

Index:1587 File:1708844531_548187.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_548187.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_548187.png

Index:1588 File:1708844531_620011.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_620011.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_620011.png

Index:1589 File:1708844531_692002.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_692002.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_692002.png

Index:1590 File:1708844531_764706.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_764706.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_764706.png

Index:1591 File:1708844531_835053.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_835053.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_835053.png

Index:1592 File:1708844531_908043.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_908043.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_908043.png

Index:1593 File:1708844531_979767.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844531_979767.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844531_979767.png

Index:1594 File:1708844532_053659.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_053659.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_053659.png

Index:1595 File:1708844532_125156.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_125156.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_125156.png

Index:1596 File:1708844532_198313.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_198313.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_198313.png

Index:1597 File:1708844532_268924.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_268924.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_268924.png

Index:1598 File:1708844532_342230.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_342230.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_342230.png

Index:1599 File:1708844532_413986.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_413986.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_413986.png

Index:1600 File:1708844532_485988.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_485988.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_485988.png

Index:1601 File:1708844532_558052.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_558052.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_558052.png

Index:1602 File:1708844532_629857.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_629857.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_629857.png

Index:1603 File:1708844532_702908.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_702908.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_702908.png

Index:1604 File:1708844532_775203.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_775203.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_775203.png

Index:1605 File:1708844532_846254.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_846254.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_846254.png

Index:1606 File:1708844532_917751.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_917751.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_917751.png

Index:1607 File:1708844532_990917.bin
护栏点已保存: ../data/1708844416_1708844532/target_management/1708844532_990917.pntbin
可视化图像已保存: ../data/1708844416_1708844532/visualization/1708844532_990917.png
Done!
[26472.048801]~DLT~192466~WARNING  ~Lost log messages in user buffer when exiting: 3662

```

---

```bash
ls -la ../data/1708844416_1708844532/visualization/
```

```
root@in-x86-dev-docker:/hfr/modules/HAF/road_boundary_perception_track/core/private/build# 
-rw-r--r-- 1 <USER>    <GROUP>    52900 May 14 15:52 1708844459_302069.png
-rw-r--r-- 1 <USER>    <GROUP>    52819 May 14 15:52 1708844459_373697.png
-rw-r--r-- 1 <USER>    <GROUP>    52871 May 14 15:52 1708844459_446394.png
-rw-r--r-- 1 <USER>    <GROUP>    52832 May 14 15:52 1708844459_516745.png
-rw-r--r-- 1 <USER>    <GROUP>    52927 May 14 15:52 1708844459_588490.png
-rw-r--r-- 1 <USER>    <GROUP>    52844 May 14 15:52 1708844459_663134.png
-rw-r--r-- 1 <USER>    <GROUP>    52788 May 14 15:52 1708844459_732743.png
-rw-r--r-- 1 <USER>    <GROUP>    52929 May 14 15:52 1708844459_808456.png
-rw-r--r-- 1 <USER>    <GROUP>    52894 May 14 15:52 1708844459_878290.png
-rw-r--r-- 1 <USER>    <GROUP>    52819 May 14 15:52 1708844459_952174.png
-rw-r--r-- 1 <USER>    <GROUP>    52912 May 14 15:52 1708844460_024286.png
-rw-r--r-- 1 <USER>    <GROUP>    52957 May 14 15:52 1708844460_095089.png
-rw-r--r-- 1 <USER>    <GROUP>    52881 May 14 15:52 1708844460_167468.png
-rw-r--r-- 1 <USER>    <GROUP>    52869 May 14 15:52 1708844460_239697.png
-rw-r--r-- 1 <USER>    <GROUP>    52900 May 14 15:52 1708844460_309967.png
-rw-r--r-- 1 <USER>    <GROUP>    52916 May 14 15:52 1708844460_382840.png
-rw-r--r-- 1 <USER>    <GROUP>    52858 May 14 15:52 1708844460_457624.png
-rw-r--r-- 1 <USER>    <GROUP>    52880 May 14 15:52 1708844460_527064.png
-rw-r--r-- 1 <USER>    <GROUP>    52894 May 14 15:52 1708844460_601330.png
-rw-r--r-- 1 <USER>    <GROUP>    52838 May 14 15:52 1708844460_671107.png
-rw-r--r-- 1 <USER>    <GROUP>    52837 May 14 15:52 1708844460_746437.png
-rw-r--r-- 1 <USER>    <GROUP>    52868 May 14 15:52 1708844460_817483.png
-rw-r--r-- 1 <USER>    <GROUP>    52937 May 14 15:52 1708844460_890789.png
-rw-r--r-- 1 <USER>    <GROUP>    52923 May 14 15:52 1708844460_960952.png
-rw-r--r-- 1 <USER>    <GROUP>    52788 May 14 15:52 1708844461_032771.png
-rw-r--r-- 1 <USER>    <GROUP>    52845 May 14 15:52 1708844461_105815.png
-rw-r--r-- 1 <USER>    <GROUP>    52793 May 14 15:52 1708844461_177954.png
-rw-r--r-- 1 <USER>    <GROUP>    52811 May 14 15:52 1708844461_251615.png
-rw-r--r-- 1 <USER>    <GROUP>    52833 May 14 15:52 1708844461_322100.png
-rw-r--r-- 1 <USER>    <GROUP>    52839 May 14 15:52 1708844461_395561.png
-rw-r--r-- 1 <USER>    <GROUP>    52859 May 14 15:52 1708844461_465269.png
-rw-r--r-- 1 <USER>    <GROUP>    52781 May 14 15:52 1708844461_537337.png
-rw-r--r-- 1 <USER>    <GROUP>    52883 May 14 15:52 1708844461_610586.png
-rw-r--r-- 1 <USER>    <GROUP>    52864 May 14 15:52 1708844461_684461.png
-rw-r--r-- 1 <USER>    <GROUP>    52795 May 14 15:52 1708844461_756723.png
-rw-r--r-- 1 <USER>    <GROUP>    52816 May 14 15:52 1708844461_827728.png
-rw-r--r-- 1 <USER>    <GROUP>    52886 May 14 15:52 1708844461_900750.png
-rw-r--r-- 1 <USER>    <GROUP>    52802 May 14 15:52 1708844461_973547.png
-rw-r--r-- 1 <USER>    <GROUP>    52840 May 14 15:52 1708844462_043793.png
-rw-r--r-- 1 <USER>    <GROUP>    52788 May 14 15:52 1708844462_117543.png
-rw-r--r-- 1 <USER>    <GROUP>    52868 May 14 15:52 1708844462_188047.png
-rw-r--r-- 1 <USER>    <GROUP>    52869 May 14 15:52 1708844462_260202.png
-rw-r--r-- 1 <USER>    <GROUP>    52843 May 14 15:52 1708844462_331200.png
-rw-r--r-- 1 <USER>    <GROUP>    52832 May 14 15:52 1708844462_405231.png
-rw-r--r-- 1 <USER>    <GROUP>    52885 May 14 15:52 1708844462_478668.png
-rw-r--r-- 1 <USER>    <GROUP>    52883 May 14 15:52 1708844462_550494.png
-rw-r--r-- 1 <USER>    <GROUP>    52863 May 14 15:52 1708844462_622466.png
-rw-r--r-- 1 <USER>    <GROUP>    52831 May 14 15:52 1708844462_691778.png
-rw-r--r-- 1 <USER>    <GROUP>    52842 May 14 15:52 1708844462_763645.png
-rw-r--r-- 1 <USER>    <GROUP>    52836 May 14 15:52 1708844462_838577.png
-rw-r--r-- 1 <USER>    <GROUP>    52813 May 14 15:52 1708844462_911278.png
-rw-r--r-- 1 <USER>    <GROUP>    52879 May 14 15:52 1708844462_984563.png
-rw-r--r-- 1 <USER>    <GROUP>    52869 May 14 15:52 1708844463_055014.png
-rw-r--r-- 1 <USER>    <GROUP>    52788 May 14 15:52 1708844463_127361.png
-rw-r--r-- 1 <USER>    <GROUP>    52875 May 14 15:52 1708844463_199049.png
-rw-r--r-- 1 <USER>    <GROUP>    52842 May 14 15:52 1708844463_271096.png
-rw-r--r-- 1 <USER>    <GROUP>    52834 May 14 15:52 1708844463_341692.png
-rw-r--r-- 1 <USER>    <GROUP>    52826 May 14 15:52 1708844463_416551.png
-rw-r--r-- 1 <USER>    <GROUP>    52861 May 14 15:52 1708844463_488352.png
-rw-r--r-- 1 <USER>    <GROUP>    52887 May 14 15:52 1708844463_560595.png
-rw-r--r-- 1 <USER>    <GROUP>    52827 May 14 15:52 1708844463_633937.png
-rw-r--r-- 1 <USER>    <GROUP>    52864 May 14 15:52 1708844463_704801.png
-rw-r--r-- 1 <USER>    <GROUP>    52828 May 14 15:52 1708844463_776350.png
-rw-r--r-- 1 <USER>    <GROUP>    52931 May 14 15:52 1708844463_849008.png
-rw-r--r-- 1 <USER>    <GROUP>    52859 May 14 15:52 1708844463_921490.png
-rw-r--r-- 1 <USER>    <GROUP>    52842 May 14 15:52 1708844463_992811.png
-rw-r--r-- 1 <USER>    <GROUP>    52863 May 14 15:52 1708844464_064342.png
-rw-r--r-- 1 <USER>    <GROUP>    52873 May 14 15:52 1708844464_138684.png
-rw-r--r-- 1 <USER>    <GROUP>    52872 May 14 15:52 1708844464_209967.png
-rw-r--r-- 1 <USER>    <GROUP>    52882 May 14 15:52 1708844464_282065.png
-rw-r--r-- 1 <USER>    <GROUP>    52813 May 14 15:52 1708844464_353421.png
-rw-r--r-- 1 <USER>    <GROUP>    52823 May 14 15:52 1708844464_424457.png
-rw-r--r-- 1 <USER>    <GROUP>    52815 May 14 15:52 1708844464_497743.png
-rw-r--r-- 1 <USER>    <GROUP>    52830 May 14 15:52 1708844464_571644.png
-rw-r--r-- 1 <USER>    <GROUP>    52861 May 14 15:52 1708844464_642052.png
-rw-r--r-- 1 <USER>    <GROUP>    52773 May 14 15:52 1708844464_712751.png
-rw-r--r-- 1 <USER>    <GROUP>    52853 May 14 15:52 1708844464_785104.png
-rw-r--r-- 1 <USER>    <GROUP>    52870 May 14 15:52 1708844464_860313.png
-rw-r--r-- 1 <USER>    <GROUP>    53724 May 14 15:52 1708844464_932460.png
-rw-r--r-- 1 <USER>    <GROUP>    52895 May 14 15:52 1708844465_001016.png
-rw-r--r-- 1 <USER>    <GROUP>    52872 May 14 15:52 1708844465_075458.png
-rw-r--r-- 1 <USER>    <GROUP>    52819 May 14 15:52 1708844465_147833.png
-rw-r--r-- 1 <USER>    <GROUP>    52866 May 14 15:52 1708844465_218209.png
-rw-r--r-- 1 <USER>    <GROUP>    52819 May 14 15:52 1708844465_291912.png
-rw-r--r-- 1 <USER>    <GROUP>    52815 May 14 15:52 1708844465_365121.png
-rw-r--r-- 1 <USER>    <GROUP>    53945 May 14 15:52 1708844465_437351.png
-rw-r--r-- 1 <USER>    <GROUP>    52887 May 14 15:52 1708844465_506953.png
-rw-r--r-- 1 <USER>    <GROUP>    54237 May 14 15:52 1708844465_581033.png
-rw-r--r-- 1 <USER>    <GROUP>    53822 May 14 15:52 1708844465_652172.png
-rw-r--r-- 1 <USER>    <GROUP>    52813 May 14 15:52 1708844465_725738.png
-rw-r--r-- 1 <USER>    <GROUP>    53604 May 14 15:52 1708844465_797721.png
-rw-r--r-- 1 <USER>    <GROUP>    53546 May 14 15:52 1708844465_868436.png
-rw-r--r-- 1 <USER>    <GROUP>    53646 May 14 15:52 1708844465_941834.png
-rw-r--r-- 1 <USER>    <GROUP>    53752 May 14 15:52 1708844466_012232.png
-rw-r--r-- 1 <USER>    <GROUP>    54456 May 14 15:52 1708844466_085317.png
-rw-r--r-- 1 <USER>    <GROUP>    54368 May 14 15:52 1708844466_158842.png
-rw-r--r-- 1 <USER>    <GROUP>    53810 May 14 15:52 1708844466_231393.png
-rw-r--r-- 1 <USER>    <GROUP>    54365 May 14 15:52 1708844466_302125.png
-rw-r--r-- 1 <USER>    <GROUP>    52783 May 14 15:52 1708844466_373111.png
-rw-r--r-- 1 <USER>    <GROUP>    54044 May 14 15:52 1708844466_446862.png
-rw-r--r-- 1 <USER>    <GROUP>    52809 May 14 15:52 1708844466_519377.png
-rw-r--r-- 1 <USER>    <GROUP>    54751 May 14 15:52 1708844466_590805.png
-rw-r--r-- 1 <USER>    <GROUP>    54453 May 14 15:52 1708844466_662205.png
-rw-r--r-- 1 <USER>    <GROUP>    54637 May 14 15:52 1708844466_735633.png
-rw-r--r-- 1 <USER>    <GROUP>    54467 May 14 15:52 1708844466_806817.png
-rw-r--r-- 1 <USER>    <GROUP>    53491 May 14 15:52 1708844466_879487.png
-rw-r--r-- 1 <USER>    <GROUP>    54415 May 14 15:52 1708844466_950948.png
-rw-r--r-- 1 <USER>    <GROUP>    54020 May 14 15:52 1708844467_024891.png
-rw-r--r-- 1 <USER>    <GROUP>    54165 May 14 15:52 1708844467_095240.png
-rw-r--r-- 1 <USER>    <GROUP>    53853 May 14 15:52 1708844467_168382.png
-rw-r--r-- 1 <USER>    <GROUP>    54320 May 14 15:52 1708844467_242226.png
-rw-r--r-- 1 <USER>    <GROUP>    54906 May 14 15:52 1708844467_312984.png
-rw-r--r-- 1 <USER>    <GROUP>    54570 May 14 15:52 1708844467_385166.png
-rw-r--r-- 1 <USER>    <GROUP>    54601 May 14 15:52 1708844467_457551.png
-rw-r--r-- 1 <USER>    <GROUP>    53688 May 14 15:52 1708844467_527923.png
-rw-r--r-- 1 <USER>    <GROUP>    53886 May 14 15:52 1708844467_601757.png
-rw-r--r-- 1 <USER>    <GROUP>    54424 May 14 15:52 1708844467_674091.png
-rw-r--r-- 1 <USER>    <GROUP>    53812 May 14 15:52 1708844467_747057.png
-rw-r--r-- 1 <USER>    <GROUP>    54031 May 14 15:52 1708844467_818303.png
-rw-r--r-- 1 <USER>    <GROUP>    54287 May 14 15:52 1708844467_891506.png
-rw-r--r-- 1 <USER>    <GROUP>    54230 May 14 15:52 1708844467_962368.png
-rw-r--r-- 1 <USER>    <GROUP>    54264 May 14 15:52 1708844468_033538.png
-rw-r--r-- 1 <USER>    <GROUP>    54332 May 14 15:52 1708844468_106505.png
-rw-r--r-- 1 <USER>    <GROUP>    53906 May 14 15:52 1708844468_177704.png
-rw-r--r-- 1 <USER>    <GROUP>    54022 May 14 15:52 1708844468_250212.png
-rw-r--r-- 1 <USER>    <GROUP>    53903 May 14 15:52 1708844468_322626.png
-rw-r--r-- 1 <USER>    <GROUP>    52852 May 14 15:52 1708844468_396715.png
-rw-r--r-- 1 <USER>    <GROUP>    52860 May 14 15:52 1708844468_467516.png
-rw-r--r-- 1 <USER>    <GROUP>    52862 May 14 15:52 1708844468_539335.png
-rw-r--r-- 1 <USER>    <GROUP>    52902 May 14 15:52 1708844468_610420.png
-rw-r--r-- 1 <USER>    <GROUP>    52902 May 14 15:52 1708844468_683852.png
-rw-r--r-- 1 <USER>    <GROUP>    54254 May 14 15:52 1708844468_756320.png
-rw-r--r-- 1 <USER>    <GROUP>    54316 May 14 15:52 1708844468_828133.png
-rw-r--r-- 1 <USER>    <GROUP>    52890 May 14 15:52 1708844468_901249.png
-rw-r--r-- 1 <USER>    <GROUP>    52816 May 14 15:52 1708844468_973112.png
-rw-r--r-- 1 <USER>    <GROUP>    52890 May 14 15:52 1708844469_044478.png
-rw-r--r-- 1 <USER>    <GROUP>    52787 May 14 15:52 1708844469_117142.png
-rw-r--r-- 1 <USER>    <GROUP>    54232 May 14 15:52 1708844469_189871.png
-rw-r--r-- 1 <USER>    <GROUP>    53903 May 14 15:52 1708844469_262141.png
-rw-r--r-- 1 <USER>    <GROUP>    54200 May 14 15:52 1708844469_333183.png
-rw-r--r-- 1 <USER>    <GROUP>    54204 May 14 15:52 1708844469_406761.png
-rw-r--r-- 1 <USER>    <GROUP>    53813 May 14 15:52 1708844469_478510.png
-rw-r--r-- 1 <USER>    <GROUP>    54024 May 14 15:52 1708844469_551034.png
-rw-r--r-- 1 <USER>    <GROUP>    54156 May 14 15:52 1708844469_623452.png
-rw-r--r-- 1 <USER>    <GROUP>    54541 May 14 15:52 1708844469_694599.png
-rw-r--r-- 1 <USER>    <GROUP>    54447 May 14 15:52 1708844469_766659.png
-rw-r--r-- 1 <USER>    <GROUP>    54564 May 14 15:52 1708844469_838456.png
-rw-r--r-- 1 <USER>    <GROUP>    54542 May 14 15:52 1708844469_910829.png
-rw-r--r-- 1 <USER>    <GROUP>    54656 May 14 15:52 1708844469_984396.png
-rw-r--r-- 1 <USER>    <GROUP>    54487 May 14 15:52 1708844470_055786.png
-rw-r--r-- 1 <USER>    <GROUP>    54759 May 14 15:52 1708844470_126795.png
-rw-r--r-- 1 <USER>    <GROUP>    54985 May 14 15:52 1708844470_199694.png
-rw-r--r-- 1 <USER>    <GROUP>    54738 May 14 15:52 1708844470_272467.png
-rw-r--r-- 1 <USER>    <GROUP>    54273 May 14 15:52 1708844470_344159.png
-rw-r--r-- 1 <USER>    <GROUP>    54749 May 14 15:52 1708844470_417449.png
-rw-r--r-- 1 <USER>    <GROUP>    53691 May 14 15:52 1708844470_489017.png
-rw-r--r-- 1 <USER>    <GROUP>    55286 May 14 15:52 1708844470_559287.png
-rw-r--r-- 1 <USER>    <GROUP>    55009 May 14 15:52 1708844470_632863.png
-rw-r--r-- 1 <USER>    <GROUP>    54507 May 14 15:52 1708844470_704897.png
-rw-r--r-- 1 <USER>    <GROUP>    54920 May 14 15:52 1708844470_777899.png
-rw-r--r-- 1 <USER>    <GROUP>    54875 May 14 15:52 1708844470_849162.png
-rw-r--r-- 1 <USER>    <GROUP>    54808 May 14 15:52 1708844470_921638.png
-rw-r--r-- 1 <USER>    <GROUP>    55169 May 14 15:52 1708844470_993596.png
-rw-r--r-- 1 <USER>    <GROUP>    54998 May 14 15:52 1708844471_064426.png
-rw-r--r-- 1 <USER>    <GROUP>    54643 May 14 15:52 1708844471_138992.png
-rw-r--r-- 1 <USER>    <GROUP>    54775 May 14 15:52 1708844471_212192.png
-rw-r--r-- 1 <USER>    <GROUP>    54330 May 14 15:52 1708844471_283195.png
-rw-r--r-- 1 <USER>    <GROUP>    54634 May 14 15:52 1708844471_355068.png
-rw-r--r-- 1 <USER>    <GROUP>    54536 May 14 15:52 1708844471_427135.png
-rw-r--r-- 1 <USER>    <GROUP>    54733 May 14 15:52 1708844471_499615.png
-rw-r--r-- 1 <USER>    <GROUP>    54530 May 14 15:52 1708844471_572020.png
-rw-r--r-- 1 <USER>    <GROUP>    54975 May 14 15:52 1708844471_643614.png
-rw-r--r-- 1 <USER>    <GROUP>    54230 May 14 15:52 1708844471_716460.png
-rw-r--r-- 1 <USER>    <GROUP>    54505 May 14 15:52 1708844471_787870.png
-rw-r--r-- 1 <USER>    <GROUP>    54491 May 14 15:52 1708844471_860232.png
-rw-r--r-- 1 <USER>    <GROUP>    54419 May 14 15:52 1708844471_932710.png
-rw-r--r-- 1 <USER>    <GROUP>    54377 May 14 15:52 1708844472_003412.png
-rw-r--r-- 1 <USER>    <GROUP>    54358 May 14 15:52 1708844472_076319.png
-rw-r--r-- 1 <USER>    <GROUP>    54448 May 14 15:52 1708844472_148507.png
-rw-r--r-- 1 <USER>    <GROUP>    54755 May 14 15:52 1708844472_220757.png
-rw-r--r-- 1 <USER>    <GROUP>    54078 May 14 15:52 1708844472_293558.png
-rw-r--r-- 1 <USER>    <GROUP>    54114 May 14 15:52 1708844472_363562.png
-rw-r--r-- 1 <USER>    <GROUP>    54019 May 14 15:52 1708844472_437365.png
-rw-r--r-- 1 <USER>    <GROUP>    53954 May 14 15:52 1708844472_510433.png
-rw-r--r-- 1 <USER>    <GROUP>    54432 May 14 15:52 1708844472_580667.png
-rw-r--r-- 1 <USER>    <GROUP>    54228 May 14 15:52 1708844472_653923.png
-rw-r--r-- 1 <USER>    <GROUP>    54326 May 14 15:52 1708844472_726459.png
-rw-r--r-- 1 <USER>    <GROUP>    54570 May 14 15:52 1708844472_799428.png
-rw-r--r-- 1 <USER>    <GROUP>    54335 May 14 15:52 1708844472_869817.png
-rw-r--r-- 1 <USER>    <GROUP>    54198 May 14 15:52 1708844472_941067.png
-rw-r--r-- 1 <USER>    <GROUP>    54397 May 14 15:52 1708844473_014608.png
-rw-r--r-- 1 <USER>    <GROUP>    54271 May 14 15:52 1708844473_087266.png
-rw-r--r-- 1 <USER>    <GROUP>    53723 May 14 15:52 1708844473_159445.png
-rw-r--r-- 1 <USER>    <GROUP>    54303 May 14 15:52 1708844473_231090.png
-rw-r--r-- 1 <USER>    <GROUP>    54004 May 14 15:52 1708844473_303412.png
-rw-r--r-- 1 <USER>    <GROUP>    52803 May 14 15:52 1708844473_374901.png
-rw-r--r-- 1 <USER>    <GROUP>    54087 May 14 15:52 1708844473_448020.png
-rw-r--r-- 1 <USER>    <GROUP>    54204 May 14 15:52 1708844473_520098.png
-rw-r--r-- 1 <USER>    <GROUP>    53834 May 14 15:52 1708844473_591777.png
-rw-r--r-- 1 <USER>    <GROUP>    54621 May 14 15:52 1708844473_664984.png
-rw-r--r-- 1 <USER>    <GROUP>    53735 May 14 15:52 1708844473_736823.png
-rw-r--r-- 1 <USER>    <GROUP>    53909 May 14 15:52 1708844473_808703.png
-rw-r--r-- 1 <USER>    <GROUP>    53903 May 14 15:52 1708844473_880639.png
-rw-r--r-- 1 <USER>    <GROUP>    53917 May 14 15:52 1708844473_953874.png
-rw-r--r-- 1 <USER>    <GROUP>    53756 May 14 15:52 1708844474_025420.png
-rw-r--r-- 1 <USER>    <GROUP>    53945 May 14 15:52 1708844474_098327.png
-rw-r--r-- 1 <USER>    <GROUP>    53692 May 14 15:52 1708844474_168764.png
-rw-r--r-- 1 <USER>    <GROUP>    52785 May 14 15:52 1708844474_241319.png
-rw-r--r-- 1 <USER>    <GROUP>    52805 May 14 15:52 1708844474_314395.png
-rw-r--r-- 1 <USER>    <GROUP>    52846 May 14 15:52 1708844474_385446.png
-rw-r--r-- 1 <USER>    <GROUP>    52801 May 14 15:52 1708844474_457979.png
-rw-r--r-- 1 <USER>    <GROUP>    52791 May 14 15:52 1708844474_530217.png
-rw-r--r-- 1 <USER>    <GROUP>    52863 May 14 15:52 1708844474_603106.png
-rw-r--r-- 1 <USER>    <GROUP>    52835 May 14 15:52 1708844474_674628.png
-rw-r--r-- 1 <USER>    <GROUP>    52825 May 14 15:52 1708844474_746582.png
-rw-r--r-- 1 <USER>    <GROUP>    52831 May 14 15:52 1708844474_819997.png
-rw-r--r-- 1 <USER>    <GROUP>    52832 May 14 15:52 1708844474_892201.png
-rw-r--r-- 1 <USER>    <GROUP>    52866 May 14 15:52 1708844474_963969.png
-rw-r--r-- 1 <USER>    <GROUP>    52864 May 14 15:52 1708844475_035460.png
-rw-r--r-- 1 <USER>    <GROUP>    54159 May 14 15:52 1708844475_107519.png
-rw-r--r-- 1 <USER>    <GROUP>    52801 May 14 15:52 1708844475_179538.png
-rw-r--r-- 1 <USER>    <GROUP>    52772 May 14 15:52 1708844475_252141.png
-rw-r--r-- 1 <USER>    <GROUP>    52824 May 14 15:52 1708844475_324469.png
-rw-r--r-- 1 <USER>    <GROUP>    52863 May 14 15:52 1708844475_396859.png
-rw-r--r-- 1 <USER>    <GROUP>    52828 May 14 15:52 1708844475_468547.png
-rw-r--r-- 1 <USER>    <GROUP>    52800 May 14 15:52 1708844475_541435.png
-rw-r--r-- 1 <USER>    <GROUP>    54264 May 14 15:52 1708844475_612612.png
-rw-r--r-- 1 <USER>    <GROUP>    53958 May 14 15:52 1708844475_686270.png
-rw-r--r-- 1 <USER>    <GROUP>    53886 May 14 15:52 1708844475_756061.png
-rw-r--r-- 1 <USER>    <GROUP>    53989 May 14 15:52 1708844475_827894.png
-rw-r--r-- 1 <USER>    <GROUP>    54154 May 14 15:52 1708844475_902250.png
-rw-r--r-- 1 <USER>    <GROUP>    54247 May 14 15:52 1708844475_974126.png
-rw-r--r-- 1 <USER>    <GROUP>    53964 May 14 15:52 1708844476_044712.png
-rw-r--r-- 1 <USER>    <GROUP>    54270 May 14 15:52 1708844476_117009.png
-rw-r--r-- 1 <USER>    <GROUP>    53716 May 14 15:52 1708844476_189578.png
-rw-r--r-- 1 <USER>    <GROUP>    54280 May 14 15:52 1708844476_262322.png
-rw-r--r-- 1 <USER>    <GROUP>    53612 May 14 15:52 1708844476_335566.png
-rw-r--r-- 1 <USER>    <GROUP>    54429 May 14 15:52 1708844476_406769.png
-rw-r--r-- 1 <USER>    <GROUP>    52842 May 14 15:52 1708844476_479828.png
-rw-r--r-- 1 <USER>    <GROUP>    54727 May 14 15:52 1708844476_549741.png
-rw-r--r-- 1 <USER>    <GROUP>    54713 May 14 15:52 1708844476_623016.png
-rw-r--r-- 1 <USER>    <GROUP>    52862 May 14 15:52 1708844476_696295.png
-rw-r--r-- 1 <USER>    <GROUP>    53623 May 14 15:52 1708844476_767513.png
-rw-r--r-- 1 <USER>    <GROUP>    55001 May 14 15:52 1708844476_838823.png
-rw-r--r-- 1 <USER>    <GROUP>    54607 May 14 15:52 1708844476_911212.png
-rw-r--r-- 1 <USER>    <GROUP>    52813 May 14 15:52 1708844476_984173.png
-rw-r--r-- 1 <USER>    <GROUP>    52877 May 14 15:52 1708844477_056858.png
-rw-r--r-- 1 <USER>    <GROUP>    54647 May 14 15:52 1708844477_126727.png
-rw-r--r-- 1 <USER>    <GROUP>    54865 May 14 15:52 1708844477_198720.png
-rw-r--r-- 1 <USER>    <GROUP>    52748 May 14 15:52 1708844477_274471.png
-rw-r--r-- 1 <USER>    <GROUP>    54665 May 14 15:52 1708844477_345654.png
-rw-r--r-- 1 <USER>    <GROUP>    54506 May 14 15:52 1708844477_417508.png
-rw-r--r-- 1 <USER>    <GROUP>    54531 May 14 15:52 1708844477_490081.png
-rw-r--r-- 1 <USER>    <GROUP>    54766 May 14 15:52 1708844477_561827.png
-rw-r--r-- 1 <USER>    <GROUP>    54573 May 14 15:52 1708844477_633534.png
-rw-r--r-- 1 <USER>    <GROUP>    55442 May 14 15:52 1708844477_708183.png
-rw-r--r-- 1 <USER>    <GROUP>    54687 May 14 15:52 1708844477_777253.png
-rw-r--r-- 1 <USER>    <GROUP>    54741 May 14 15:52 1708844477_849911.png
-rw-r--r-- 1 <USER>    <GROUP>    54870 May 14 15:52 1708844477_922450.png
-rw-r--r-- 1 <USER>    <GROUP>    54561 May 14 15:52 1708844477_996019.png
-rw-r--r-- 1 <USER>    <GROUP>    54755 May 14 15:52 1708844478_066988.png
-rw-r--r-- 1 <USER>    <GROUP>    54676 May 14 15:52 1708844478_139509.png
-rw-r--r-- 1 <USER>    <GROUP>    54676 May 14 15:52 1708844478_211843.png
-rw-r--r-- 1 <USER>    <GROUP>    54791 May 14 15:52 1708844478_283272.png
-rw-r--r-- 1 <USER>    <GROUP>    54707 May 14 15:52 1708844478_355588.png
-rw-r--r-- 1 <USER>    <GROUP>    54729 May 14 15:52 1708844478_428923.png
-rw-r--r-- 1 <USER>    <GROUP>    54264 May 14 15:52 1708844478_499159.png
-rw-r--r-- 1 <USER>    <GROUP>    54599 May 14 15:52 1708844478_572672.png
-rw-r--r-- 1 <USER>    <GROUP>    54904 May 14 15:52 1708844478_644630.png
-rw-r--r-- 1 <USER>    <GROUP>    54824 May 14 15:52 1708844478_716457.png
-rw-r--r-- 1 <USER>    <GROUP>    54478 May 14 15:52 1708844478_789656.png
-rw-r--r-- 1 <USER>    <GROUP>    54599 May 14 15:52 1708844478_862357.png
-rw-r--r-- 1 <USER>    <GROUP>    54950 May 14 15:52 1708844478_932730.png
-rw-r--r-- 1 <USER>    <GROUP>    54248 May 14 15:52 1708844479_005837.png
-rw-r--r-- 1 <USER>    <GROUP>    55033 May 14 15:52 1708844479_077670.png
-rw-r--r-- 1 <USER>    <GROUP>    55009 May 14 15:52 1708844479_150149.png
-rw-r--r-- 1 <USER>    <GROUP>    55021 May 14 15:52 1708844479_223429.png
-rw-r--r-- 1 <USER>    <GROUP>    55060 May 14 15:52 1708844479_293580.png
-rw-r--r-- 1 <USER>    <GROUP>    54362 May 14 15:52 1708844479_367014.png
-rw-r--r-- 1 <USER>    <GROUP>    54615 May 14 15:52 1708844479_438093.png
-rw-r--r-- 1 <USER>    <GROUP>    54853 May 14 15:52 1708844479_511417.png
-rw-r--r-- 1 <USER>    <GROUP>    54897 May 14 15:52 1708844479_582498.png
-rw-r--r-- 1 <USER>    <GROUP>    54744 May 14 15:52 1708844479_656328.png
-rw-r--r-- 1 <USER>    <GROUP>    54381 May 14 15:52 1708844479_728353.png
-rw-r--r-- 1 <USER>    <GROUP>    54020 May 14 15:52 1708844479_799456.png
-rw-r--r-- 1 <USER>    <GROUP>    54564 May 14 15:52 1708844479_870963.png
-rw-r--r-- 1 <USER>    <GROUP>    54640 May 14 15:52 1708844479_944101.png
-rw-r--r-- 1 <USER>    <GROUP>    54435 May 14 15:52 1708844480_014973.png
-rw-r--r-- 1 <USER>    <GROUP>    54417 May 14 15:52 1708844480_087403.png
-rw-r--r-- 1 <USER>    <GROUP>    54376 May 14 15:52 1708844480_159563.png
-rw-r--r-- 1 <USER>    <GROUP>    54347 May 14 15:52 1708844480_232691.png
-rw-r--r-- 1 <USER>    <GROUP>    54449 May 14 15:52 1708844480_304639.png
-rw-r--r-- 1 <USER>    <GROUP>    54491 May 14 15:52 1708844480_377863.png
-rw-r--r-- 1 <USER>    <GROUP>    54305 May 14 15:52 1708844480_448243.png
-rw-r--r-- 1 <USER>    <GROUP>    54417 May 14 15:52 1708844480_520619.png
-rw-r--r-- 1 <USER>    <GROUP>    54411 May 14 15:52 1708844480_593503.png
-rw-r--r-- 1 <USER>    <GROUP>    54694 May 14 15:52 1708844480_666282.png
-rw-r--r-- 1 <USER>    <GROUP>    54060 May 14 15:52 1708844480_738081.png
-rw-r--r-- 1 <USER>    <GROUP>    53956 May 14 15:52 1708844480_809820.png
-rw-r--r-- 1 <USER>    <GROUP>    54558 May 14 15:52 1708844480_880222.png
-rw-r--r-- 1 <USER>    <GROUP>    54027 May 14 15:52 1708844480_954236.png
-rw-r--r-- 1 <USER>    <GROUP>    53841 May 14 15:52 1708844481_026657.png
-rw-r--r-- 1 <USER>    <GROUP>    54366 May 14 15:52 1708844481_098518.png
-rw-r--r-- 1 <USER>    <GROUP>    53529 May 14 15:52 1708844481_171321.png
-rw-r--r-- 1 <USER>    <GROUP>    54095 May 14 15:52 1708844481_243243.png
-rw-r--r-- 1 <USER>    <GROUP>    54713 May 14 15:52 1708844481_316389.png
-rw-r--r-- 1 <USER>    <GROUP>    55236 May 14 15:52 1708844481_387670.png
-rw-r--r-- 1 <USER>    <GROUP>    54738 May 14 15:52 1708844481_459791.png
-rw-r--r-- 1 <USER>    <GROUP>    55203 May 14 15:52 1708844481_532076.png
-rw-r--r-- 1 <USER>    <GROUP>    55137 May 14 15:52 1708844481_603881.png
-rw-r--r-- 1 <USER>    <GROUP>    54180 May 14 15:52 1708844481_675382.png
-rw-r--r-- 1 <USER>    <GROUP>    55017 May 14 15:52 1708844481_748103.png
-rw-r--r-- 1 <USER>    <GROUP>    55000 May 14 15:52 1708844481_820697.png
-rw-r--r-- 1 <USER>    <GROUP>    55328 May 14 15:52 1708844481_892849.png
-rw-r--r-- 1 <USER>    <GROUP>    55550 May 14 15:52 1708844481_965291.png
-rw-r--r-- 1 <USER>    <GROUP>    55406 May 14 15:52 1708844482_036625.png
-rw-r--r-- 1 <USER>    <GROUP>    55330 May 14 15:52 1708844482_108234.png
-rw-r--r-- 1 <USER>    <GROUP>    54350 May 14 15:52 1708844482_181841.png
-rw-r--r-- 1 <USER>    <GROUP>    55746 May 14 15:52 1708844482_254248.png
-rw-r--r-- 1 <USER>    <GROUP>    55998 May 14 15:52 1708844482_325742.png
-rw-r--r-- 1 <USER>    <GROUP>    55686 May 14 15:52 1708844482_397806.png
-rw-r--r-- 1 <USER>    <GROUP>    56128 May 14 15:52 1708844482_469603.png
-rw-r--r-- 1 <USER>    <GROUP>    55772 May 14 15:52 1708844482_543063.png
-rw-r--r-- 1 <USER>    <GROUP>    54657 May 14 15:52 1708844482_615401.png
-rw-r--r-- 1 <USER>    <GROUP>    55131 May 14 15:52 1708844482_686181.png
-rw-r--r-- 1 <USER>    <GROUP>    55187 May 14 15:52 1708844482_758903.png
-rw-r--r-- 1 <USER>    <GROUP>    55072 May 14 15:52 1708844482_830623.png
-rw-r--r-- 1 <USER>    <GROUP>    55593 May 14 15:52 1708844482_902772.png
-rw-r--r-- 1 <USER>    <GROUP>    55699 May 14 15:52 1708844482_975540.png
-rw-r--r-- 1 <USER>    <GROUP>    54725 May 14 15:52 1708844483_047979.png
-rw-r--r-- 1 <USER>    <GROUP>    54657 May 14 15:52 1708844483_119717.png
-rw-r--r-- 1 <USER>    <GROUP>    54745 May 14 15:52 1708844483_191518.png
-rw-r--r-- 1 <USER>    <GROUP>    55684 May 14 15:52 1708844483_263243.png
-rw-r--r-- 1 <USER>    <GROUP>    54622 May 14 15:52 1708844483_335971.png
-rw-r--r-- 1 <USER>    <GROUP>    55600 May 14 15:52 1708844483_407361.png
-rw-r--r-- 1 <USER>    <GROUP>    55807 May 14 15:52 1708844483_480662.png
-rw-r--r-- 1 <USER>    <GROUP>    55523 May 14 15:52 1708844483_552300.png
-rw-r--r-- 1 <USER>    <GROUP>    54409 May 14 15:52 1708844483_624153.png
-rw-r--r-- 1 <USER>    <GROUP>    54176 May 14 15:52 1708844483_696158.png
-rw-r--r-- 1 <USER>    <GROUP>    54908 May 14 15:52 1708844483_769444.png
-rw-r--r-- 1 <USER>    <GROUP>    54530 May 14 15:52 1708844483_841345.png
-rw-r--r-- 1 <USER>    <GROUP>    54465 May 14 15:52 1708844483_913329.png
-rw-r--r-- 1 <USER>    <GROUP>    54816 May 14 15:52 1708844483_985591.png
-rw-r--r-- 1 <USER>    <GROUP>    55727 May 14 15:52 1708844484_058132.png
-rw-r--r-- 1 <USER>    <GROUP>    55007 May 14 15:52 1708844484_129353.png
-rw-r--r-- 1 <USER>    <GROUP>    55727 May 14 15:52 1708844484_203122.png
-rw-r--r-- 1 <USER>    <GROUP>    54808 May 14 15:52 1708844484_274302.png
-rw-r--r-- 1 <USER>    <GROUP>    55396 May 14 15:52 1708844484_346080.png
-rw-r--r-- 1 <USER>    <GROUP>    54778 May 14 15:52 1708844484_418619.png
-rw-r--r-- 1 <USER>    <GROUP>    55157 May 14 15:52 1708844484_489492.png
-rw-r--r-- 1 <USER>    <GROUP>    54022 May 14 15:52 1708844484_561827.png
-rw-r--r-- 1 <USER>    <GROUP>    56084 May 14 15:52 1708844484_634806.png
-rw-r--r-- 1 <USER>    <GROUP>    55577 May 14 15:52 1708844484_707630.png
-rw-r--r-- 1 <USER>    <GROUP>    55381 May 14 15:52 1708844484_780348.png
-rw-r--r-- 1 <USER>    <GROUP>    55891 May 14 15:52 1708844484_851203.png
-rw-r--r-- 1 <USER>    <GROUP>    55259 May 14 15:52 1708844484_923138.png
-rw-r--r-- 1 <USER>    <GROUP>    55647 May 14 15:52 1708844484_995506.png
-rw-r--r-- 1 <USER>    <GROUP>    55604 May 14 15:52 1708844485_068050.png
-rw-r--r-- 1 <USER>    <GROUP>    54903 May 14 15:52 1708844485_140377.png
-rw-r--r-- 1 <USER>    <GROUP>    55050 May 14 15:52 1708844485_213619.png
-rw-r--r-- 1 <USER>    <GROUP>    55019 May 14 15:52 1708844485_283935.png
-rw-r--r-- 1 <USER>    <GROUP>    54142 May 14 15:52 1708844485_355856.png
-rw-r--r-- 1 <USER>    <GROUP>    54808 May 14 15:52 1708844485_429405.png
-rw-r--r-- 1 <USER>    <GROUP>    54815 May 14 15:52 1708844485_500900.png
-rw-r--r-- 1 <USER>    <GROUP>    54141 May 14 15:52 1708844485_573731.png
-rw-r--r-- 1 <USER>    <GROUP>    55049 May 14 15:52 1708844485_645838.png
-rw-r--r-- 1 <USER>    <GROUP>    55100 May 14 15:52 1708844485_716779.png
-rw-r--r-- 1 <USER>    <GROUP>    55187 May 14 15:52 1708844485_790402.png
-rw-r--r-- 1 <USER>    <GROUP>    55202 May 14 15:52 1708844485_861399.png
-rw-r--r-- 1 <USER>    <GROUP>    55119 May 14 15:52 1708844485_933898.png
-rw-r--r-- 1 <USER>    <GROUP>    55127 May 14 15:52 1708844486_003255.png
-rw-r--r-- 1 <USER>    <GROUP>    55693 May 14 15:52 1708844486_079339.png
-rw-r--r-- 1 <USER>    <GROUP>    54668 May 14 15:52 1708844486_150420.png
-rw-r--r-- 1 <USER>    <GROUP>    54925 May 14 15:52 1708844486_222806.png
-rw-r--r-- 1 <USER>    <GROUP>    56038 May 14 15:53 1708844486_294647.png
-rw-r--r-- 1 <USER>    <GROUP>    54455 May 14 15:53 1708844486_367955.png
-rw-r--r-- 1 <USER>    <GROUP>    54945 May 14 15:53 1708844486_439397.png
-rw-r--r-- 1 <USER>    <GROUP>    54398 May 14 15:53 1708844486_512746.png
-rw-r--r-- 1 <USER>    <GROUP>    55354 May 14 15:53 1708844486_584441.png
-rw-r--r-- 1 <USER>    <GROUP>    55552 May 14 15:53 1708844486_656629.png
-rw-r--r-- 1 <USER>    <GROUP>    53904 May 14 15:53 1708844486_728062.png
-rw-r--r-- 1 <USER>    <GROUP>    54488 May 14 15:53 1708844486_800147.png
-rw-r--r-- 1 <USER>    <GROUP>    52900 May 14 15:53 1708844486_873889.png
-rw-r--r-- 1 <USER>    <GROUP>    52870 May 14 15:53 1708844486_943997.png
-rw-r--r-- 1 <USER>    <GROUP>    54331 May 14 15:53 1708844487_017116.png
-rw-r--r-- 1 <USER>    <GROUP>    54010 May 14 15:53 1708844487_088551.png
-rw-r--r-- 1 <USER>    <GROUP>    54664 May 14 15:53 1708844487_161244.png
-rw-r--r-- 1 <USER>    <GROUP>    53896 May 14 15:53 1708844487_233260.png
-rw-r--r-- 1 <USER>    <GROUP>    54390 May 14 15:53 1708844487_306454.png
-rw-r--r-- 1 <USER>    <GROUP>    54348 May 14 15:53 1708844487_377558.png
-rw-r--r-- 1 <USER>    <GROUP>    54713 May 14 15:53 1708844487_450222.png
-rw-r--r-- 1 <USER>    <GROUP>    54677 May 14 15:53 1708844487_522585.png
-rw-r--r-- 1 <USER>    <GROUP>    54791 May 14 15:53 1708844487_593569.png
-rw-r--r-- 1 <USER>    <GROUP>    54534 May 14 15:53 1708844487_667179.png
-rw-r--r-- 1 <USER>    <GROUP>    54522 May 14 15:53 1708844487_737816.png
-rw-r--r-- 1 <USER>    <GROUP>    54613 May 14 15:53 1708844487_811521.png
-rw-r--r-- 1 <USER>    <GROUP>    54009 May 14 15:53 1708844487_881804.png
-rw-r--r-- 1 <USER>    <GROUP>    54184 May 14 15:53 1708844487_954487.png
-rw-r--r-- 1 <USER>    <GROUP>    54690 May 14 15:53 1708844488_025328.png
-rw-r--r-- 1 <USER>    <GROUP>    54627 May 14 15:53 1708844488_100754.png
-rw-r--r-- 1 <USER>    <GROUP>    54621 May 14 15:53 1708844488_171554.png
-rw-r--r-- 1 <USER>    <GROUP>    54402 May 14 15:53 1708844488_244028.png
-rw-r--r-- 1 <USER>    <GROUP>    54396 May 14 15:53 1708844488_316748.png
-rw-r--r-- 1 <USER>    <GROUP>    54251 May 14 15:53 1708844488_387426.png
-rw-r--r-- 1 <USER>    <GROUP>    54308 May 14 15:53 1708844488_461476.png
-rw-r--r-- 1 <USER>    <GROUP>    54209 May 14 15:53 1708844488_531457.png
-rw-r--r-- 1 <USER>    <GROUP>    54291 May 14 15:53 1708844488_604071.png
-rw-r--r-- 1 <USER>    <GROUP>    54265 May 14 15:53 1708844488_676734.png
-rw-r--r-- 1 <USER>    <GROUP>    54231 May 14 15:53 1708844488_749143.png
-rw-r--r-- 1 <USER>    <GROUP>    54297 May 14 15:53 1708844488_821465.png
-rw-r--r-- 1 <USER>    <GROUP>    54346 May 14 15:53 1708844488_892629.png
-rw-r--r-- 1 <USER>    <GROUP>    53886 May 14 15:53 1708844488_964093.png
-rw-r--r-- 1 <USER>    <GROUP>    53811 May 14 15:53 1708844489_037207.png
-rw-r--r-- 1 <USER>    <GROUP>    53830 May 14 15:53 1708844489_109947.png
-rw-r--r-- 1 <USER>    <GROUP>    53751 May 14 15:53 1708844489_181195.png
-rw-r--r-- 1 <USER>    <GROUP>    52813 May 14 15:53 1708844489_252732.png
-rw-r--r-- 1 <USER>    <GROUP>    52841 May 14 15:53 1708844489_326314.png
-rw-r--r-- 1 <USER>    <GROUP>    52895 May 14 15:53 1708844489_396768.png
-rw-r--r-- 1 <USER>    <GROUP>    52928 May 14 15:53 1708844489_469948.png
-rw-r--r-- 1 <USER>    <GROUP>    52854 May 14 15:53 1708844489_542378.png
-rw-r--r-- 1 <USER>    <GROUP>    52891 May 14 15:53 1708844489_615504.png
-rw-r--r-- 1 <USER>    <GROUP>    52944 May 14 15:53 1708844489_688458.png
-rw-r--r-- 1 <USER>    <GROUP>    52859 May 14 15:53 1708844489_758072.png
-rw-r--r-- 1 <USER>    <GROUP>    52816 May 14 15:53 1708844489_831727.png
-rw-r--r-- 1 <USER>    <GROUP>    52867 May 14 15:53 1708844489_903735.png
-rw-r--r-- 1 <USER>    <GROUP>    52871 May 14 15:53 1708844489_976641.png
-rw-r--r-- 1 <USER>    <GROUP>    52903 May 14 15:53 1708844490_046749.png
-rw-r--r-- 1 <USER>    <GROUP>    52908 May 14 15:53 1708844490_120500.png
-rw-r--r-- 1 <USER>    <GROUP>    52917 May 14 15:53 1708844490_192000.png
-rw-r--r-- 1 <USER>    <GROUP>    52896 May 14 15:53 1708844490_264839.png
-rw-r--r-- 1 <USER>    <GROUP>    52853 May 14 15:53 1708844490_336551.png
-rw-r--r-- 1 <USER>    <GROUP>    52879 May 14 15:53 1708844490_409114.png
-rw-r--r-- 1 <USER>    <GROUP>    52893 May 14 15:53 1708844490_481945.png
-rw-r--r-- 1 <USER>    <GROUP>    52860 May 14 15:53 1708844490_553307.png
-rw-r--r-- 1 <USER>    <GROUP>    52888 May 14 15:53 1708844490_623585.png
-rw-r--r-- 1 <USER>    <GROUP>    52939 May 14 15:53 1708844490_698070.png
-rw-r--r-- 1 <USER>    <GROUP>    52873 May 14 15:53 1708844490_768957.png
-rw-r--r-- 1 <USER>    <GROUP>    52893 May 14 15:53 1708844490_841824.png
-rw-r--r-- 1 <USER>    <GROUP>    52884 May 14 15:53 1708844490_914825.png
-rw-r--r-- 1 <USER>    <GROUP>    52910 May 14 15:53 1708844490_986255.png
-rw-r--r-- 1 <USER>    <GROUP>    52869 May 14 15:53 1708844491_058519.png
-rw-r--r-- 1 <USER>    <GROUP>    52825 May 14 15:53 1708844491_130591.png
-rw-r--r-- 1 <USER>    <GROUP>    52803 May 14 15:53 1708844491_201521.png
-rw-r--r-- 1 <USER>    <GROUP>    52790 May 14 15:53 1708844491_275441.png
-rw-r--r-- 1 <USER>    <GROUP>    52803 May 14 15:53 1708844491_347159.png
-rw-r--r-- 1 <USER>    <GROUP>    52884 May 14 15:53 1708844491_418868.png
-rw-r--r-- 1 <USER>    <GROUP>    52860 May 14 15:53 1708844491_492036.png
-rw-r--r-- 1 <USER>    <GROUP>    52863 May 14 15:53 1708844491_563440.png
-rw-r--r-- 1 <USER>    <GROUP>    52889 May 14 15:53 1708844491_636666.png
-rw-r--r-- 1 <USER>    <GROUP>    52812 May 14 15:53 1708844491_707933.png
-rw-r--r-- 1 <USER>    <GROUP>    52777 May 14 15:53 1708844491_779977.png
-rw-r--r-- 1 <USER>    <GROUP>    52869 May 14 15:53 1708844491_851969.png
-rw-r--r-- 1 <USER>    <GROUP>    52864 May 14 15:53 1708844491_923588.png
-rw-r--r-- 1 <USER>    <GROUP>    52846 May 14 15:53 1708844491_996787.png
-rw-r--r-- 1 <USER>    <GROUP>    52931 May 14 15:53 1708844492_068996.png
-rw-r--r-- 1 <USER>    <GROUP>    52867 May 14 15:53 1708844492_140446.png
-rw-r--r-- 1 <USER>    <GROUP>    52779 May 14 15:53 1708844492_211677.png
-rw-r--r-- 1 <USER>    <GROUP>    52865 May 14 15:53 1708844492_283881.png
-rw-r--r-- 1 <USER>    <GROUP>    52802 May 14 15:53 1708844492_357533.png
-rw-r--r-- 1 <USER>    <GROUP>    52835 May 14 15:53 1708844492_431926.png
-rw-r--r-- 1 <USER>    <GROUP>    52852 May 14 15:53 1708844492_501705.png
-rw-r--r-- 1 <USER>    <GROUP>    52828 May 14 15:53 1708844492_573483.png
-rw-r--r-- 1 <USER>    <GROUP>    52858 May 14 15:53 1708844492_646567.png
-rw-r--r-- 1 <USER>    <GROUP>    52849 May 14 15:53 1708844492_718360.png
-rw-r--r-- 1 <USER>    <GROUP>    52867 May 14 15:53 1708844492_789945.png
-rw-r--r-- 1 <USER>    <GROUP>    52851 May 14 15:53 1708844492_863343.png
-rw-r--r-- 1 <USER>    <GROUP>    52853 May 14 15:53 1708844492_934041.png
-rw-r--r-- 1 <USER>    <GROUP>    52857 May 14 15:53 1708844493_007183.png
-rw-r--r-- 1 <USER>    <GROUP>    52870 May 14 15:53 1708844493_078768.png
-rw-r--r-- 1 <USER>    <GROUP>    52830 May 14 15:53 1708844493_151081.png
-rw-r--r-- 1 <USER>    <GROUP>    52796 May 14 15:53 1708844493_223314.png
-rw-r--r-- 1 <USER>    <GROUP>    52872 May 14 15:53 1708844493_295958.png
-rw-r--r-- 1 <USER>    <GROUP>    52840 May 14 15:53 1708844493_366770.png
-rw-r--r-- 1 <USER>    <GROUP>    52864 May 14 15:53 1708844493_439682.png
-rw-r--r-- 1 <USER>    <GROUP>    52824 May 14 15:53 1708844493_511538.png
-rw-r--r-- 1 <USER>    <GROUP>    52843 May 14 15:53 1708844493_584447.png
-rw-r--r-- 1 <USER>    <GROUP>    52820 May 14 15:53 1708844493_654751.png
-rw-r--r-- 1 <USER>    <GROUP>    52851 May 14 15:53 1708844493_728260.png
-rw-r--r-- 1 <USER>    <GROUP>    52911 May 14 15:53 1708844493_800790.png
-rw-r--r-- 1 <USER>    <GROUP>    52825 May 14 15:53 1708844493_873597.png
-rw-r--r-- 1 <USER>    <GROUP>    52863 May 14 15:53 1708844493_944661.png
-rw-r--r-- 1 <USER>    <GROUP>    52878 May 14 15:53 1708844494_016042.png
-rw-r--r-- 1 <USER>    <GROUP>    52911 May 14 15:53 1708844494_088526.png
-rw-r--r-- 1 <USER>    <GROUP>    52896 May 14 15:53 1708844494_160598.png
-rw-r--r-- 1 <USER>    <GROUP>    52862 May 14 15:53 1708844494_234020.png
-rw-r--r-- 1 <USER>    <GROUP>    52903 May 14 15:53 1708844494_306586.png
-rw-r--r-- 1 <USER>    <GROUP>    52807 May 14 15:53 1708844494_378221.png
-rw-r--r-- 1 <USER>    <GROUP>    52866 May 14 15:53 1708844494_449463.png
-rw-r--r-- 1 <USER>    <GROUP>    52850 May 14 15:53 1708844494_522087.png
-rw-r--r-- 1 <USER>    <GROUP>    52863 May 14 15:53 1708844494_594799.png
-rw-r--r-- 1 <USER>    <GROUP>    52922 May 14 15:53 1708844494_666588.png
-rw-r--r-- 1 <USER>    <GROUP>    52834 May 14 15:53 1708844494_740107.png
-rw-r--r-- 1 <USER>    <GROUP>    52881 May 14 15:53 1708844494_882536.png
-rw-r--r-- 1 <USER>    <GROUP>    52859 May 14 15:53 1708844494_956126.png
-rw-r--r-- 1 <USER>    <GROUP>    52872 May 14 15:53 1708844495_027895.png
-rw-r--r-- 1 <USER>    <GROUP>    52856 May 14 15:53 1708844495_100744.png
-rw-r--r-- 1 <USER>    <GROUP>    52771 May 14 15:53 1708844495_172115.png
-rw-r--r-- 1 <USER>    <GROUP>    52865 May 14 15:53 1708844495_244638.png
-rw-r--r-- 1 <USER>    <GROUP>    53725 May 14 15:53 1708844495_316011.png
-rw-r--r-- 1 <USER>    <GROUP>    52881 May 14 15:53 1708844495_388150.png
-rw-r--r-- 1 <USER>    <GROUP>    52840 May 14 15:53 1708844495_461130.png
-rw-r--r-- 1 <USER>    <GROUP>    52855 May 14 15:53 1708844495_534240.png
-rw-r--r-- 1 <USER>    <GROUP>    52886 May 14 15:53 1708844495_605033.png
-rw-r--r-- 1 <USER>    <GROUP>    52843 May 14 15:53 1708844495_678423.png
-rw-r--r-- 1 <USER>    <GROUP>    52831 May 14 15:53 1708844495_749675.png
-rw-r--r-- 1 <USER>    <GROUP>    52881 May 14 15:53 1708844495_821858.png
-rw-r--r-- 1 <USER>    <GROUP>    52875 May 14 15:53 1708844495_894244.png
-rw-r--r-- 1 <USER>    <GROUP>    52934 May 14 15:53 1708844495_966889.png
-rw-r--r-- 1 <USER>    <GROUP>    52868 May 14 15:53 1708844496_038729.png
-rw-r--r-- 1 <USER>    <GROUP>    52894 May 14 15:53 1708844496_109309.png
-rw-r--r-- 1 <USER>    <GROUP>    52840 May 14 15:53 1708844496_182759.png
-rw-r--r-- 1 <USER>    <GROUP>    52818 May 14 15:53 1708844496_254257.png
-rw-r--r-- 1 <USER>    <GROUP>    52806 May 14 15:53 1708844496_326773.png
-rw-r--r-- 1 <USER>    <GROUP>    54497 May 14 15:53 1708844496_399917.png
-rw-r--r-- 1 <USER>    <GROUP>    52877 May 14 15:53 1708844496_470920.png
-rw-r--r-- 1 <USER>    <GROUP>    52864 May 14 15:53 1708844496_544439.png
-rw-r--r-- 1 <USER>    <GROUP>    52839 May 14 15:53 1708844496_615541.png
-rw-r--r-- 1 <USER>    <GROUP>    52895 May 14 15:53 1708844496_688181.png
-rw-r--r-- 1 <USER>    <GROUP>    52853 May 14 15:53 1708844496_760193.png
-rw-r--r-- 1 <USER>    <GROUP>    52869 May 14 15:53 1708844496_832565.png
-rw-r--r-- 1 <USER>    <GROUP>    52923 May 14 15:53 1708844496_906368.png
-rw-r--r-- 1 <USER>    <GROUP>    52861 May 14 15:53 1708844496_976320.png
-rw-r--r-- 1 <USER>    <GROUP>    52892 May 14 15:53 1708844497_048655.png
-rw-r--r-- 1 <USER>    <GROUP>    52783 May 14 15:53 1708844497_121367.png
-rw-r--r-- 1 <USER>    <GROUP>    52804 May 14 15:53 1708844497_192574.png
-rw-r--r-- 1 <USER>    <GROUP>    52830 May 14 15:53 1708844497_266119.png
-rw-r--r-- 1 <USER>    <GROUP>    52799 May 14 15:53 1708844497_339751.png
-rw-r--r-- 1 <USER>    <GROUP>    52855 May 14 15:53 1708844497_409631.png
-rw-r--r-- 1 <USER>    <GROUP>    52832 May 14 15:53 1708844497_481674.png
-rw-r--r-- 1 <USER>    <GROUP>    52806 May 14 15:53 1708844497_554123.png
-rw-r--r-- 1 <USER>    <GROUP>    52863 May 14 15:53 1708844497_626867.png
-rw-r--r-- 1 <USER>    <GROUP>    52876 May 14 15:53 1708844497_698516.png
-rw-r--r-- 1 <USER>    <GROUP>    52835 May 14 15:53 1708844497_770626.png
-rw-r--r-- 1 <USER>    <GROUP>    52846 May 14 15:53 1708844497_842748.png
-rw-r--r-- 1 <USER>    <GROUP>    52856 May 14 15:53 1708844497_913460.png
-rw-r--r-- 1 <USER>    <GROUP>    52916 May 14 15:53 1708844497_988028.png
-rw-r--r-- 1 <USER>    <GROUP>    53711 May 14 15:53 1708844498_059739.png
-rw-r--r-- 1 <USER>    <GROUP>    52851 May 14 15:53 1708844498_131440.png
-rw-r--r-- 1 <USER>    <GROUP>    52856 May 14 15:53 1708844498_204179.png
-rw-r--r-- 1 <USER>    <GROUP>    52836 May 14 15:53 1708844498_276243.png
-rw-r--r-- 1 <USER>    <GROUP>    53637 May 14 15:53 1708844498_347696.png
-rw-r--r-- 1 <USER>    <GROUP>    52864 May 14 15:53 1708844498_419979.png
-rw-r--r-- 1 <USER>    <GROUP>    52892 May 14 15:53 1708844498_492881.png
-rw-r--r-- 1 <USER>    <GROUP>    52890 May 14 15:53 1708844498_565346.png
-rw-r--r-- 1 <USER>    <GROUP>    52866 May 14 15:53 1708844498_637269.png
-rw-r--r-- 1 <USER>    <GROUP>    52854 May 14 15:53 1708844498_709512.png
-rw-r--r-- 1 <USER>    <GROUP>    52893 May 14 15:53 1708844498_780445.png
-rw-r--r-- 1 <USER>    <GROUP>    53811 May 14 15:53 1708844498_852072.png
-rw-r--r-- 1 <USER>    <GROUP>    53994 May 14 15:53 1708844498_926419.png
-rw-r--r-- 1 <USER>    <GROUP>    54043 May 14 15:53 1708844498_998417.png
-rw-r--r-- 1 <USER>    <GROUP>    54022 May 14 15:53 1708844499_070655.png
-rw-r--r-- 1 <USER>    <GROUP>    54071 May 14 15:53 1708844499_143114.png
-rw-r--r-- 1 <USER>    <GROUP>    54033 May 14 15:53 1708844499_215051.png
-rw-r--r-- 1 <USER>    <GROUP>    54305 May 14 15:53 1708844499_287832.png
-rw-r--r-- 1 <USER>    <GROUP>    54408 May 14 15:53 1708844499_358836.png
-rw-r--r-- 1 <USER>    <GROUP>    54416 May 14 15:53 1708844499_431094.png
-rw-r--r-- 1 <USER>    <GROUP>    54386 May 14 15:53 1708844499_502775.png
-rw-r--r-- 1 <USER>    <GROUP>    54898 May 14 15:53 1708844499_575834.png
-rw-r--r-- 1 <USER>    <GROUP>    54778 May 14 15:53 1708844499_648231.png
-rw-r--r-- 1 <USER>    <GROUP>    54507 May 14 15:53 1708844499_719744.png
-rw-r--r-- 1 <USER>    <GROUP>    54241 May 14 15:53 1708844499_791162.png
-rw-r--r-- 1 <USER>    <GROUP>    54678 May 14 15:53 1708844499_862728.png
-rw-r--r-- 1 <USER>    <GROUP>    54601 May 14 15:53 1708844499_936744.png
-rw-r--r-- 1 <USER>    <GROUP>    54708 May 14 15:53 1708844500_007390.png
-rw-r--r-- 1 <USER>    <GROUP>    53686 May 14 15:53 1708844500_079915.png
-rw-r--r-- 1 <USER>    <GROUP>    54674 May 14 15:53 1708844500_151713.png
-rw-r--r-- 1 <USER>    <GROUP>    54831 May 14 15:53 1708844500_224992.png
-rw-r--r-- 1 <USER>    <GROUP>    55036 May 14 15:53 1708844500_296841.png
-rw-r--r-- 1 <USER>    <GROUP>    53914 May 14 15:53 1708844500_368278.png
-rw-r--r-- 1 <USER>    <GROUP>    53928 May 14 15:53 1708844500_442386.png
-rw-r--r-- 1 <USER>    <GROUP>    54574 May 14 15:53 1708844500_513517.png
-rw-r--r-- 1 <USER>    <GROUP>    54417 May 14 15:53 1708844500_586729.png
-rw-r--r-- 1 <USER>    <GROUP>    54605 May 14 15:53 1708844500_658882.png
-rw-r--r-- 1 <USER>    <GROUP>    54342 May 14 15:53 1708844500_729114.png
-rw-r--r-- 1 <USER>    <GROUP>    53515 May 14 15:53 1708844500_801614.png
-rw-r--r-- 1 <USER>    <GROUP>    53417 May 14 15:53 1708844500_874971.png
-rw-r--r-- 1 <USER>    <GROUP>    53783 May 14 15:53 1708844500_947074.png
-rw-r--r-- 1 <USER>    <GROUP>    54364 May 14 15:53 1708844501_018674.png
-rw-r--r-- 1 <USER>    <GROUP>    54610 May 14 15:53 1708844501_090189.png
-rw-r--r-- 1 <USER>    <GROUP>    55128 May 14 15:53 1708844501_162345.png
-rw-r--r-- 1 <USER>    <GROUP>    54912 May 14 15:53 1708844501_235727.png
-rw-r--r-- 1 <USER>    <GROUP>    54918 May 14 15:53 1708844501_306275.png
-rw-r--r-- 1 <USER>    <GROUP>    55431 May 14 15:53 1708844501_378510.png
-rw-r--r-- 1 <USER>    <GROUP>    55378 May 14 15:53 1708844501_449401.png
-rw-r--r-- 1 <USER>    <GROUP>    55047 May 14 15:53 1708844501_523870.png
-rw-r--r-- 1 <USER>    <GROUP>    55438 May 14 15:53 1708844501_595749.png
-rw-r--r-- 1 <USER>    <GROUP>    55559 May 14 15:53 1708844501_668421.png
-rw-r--r-- 1 <USER>    <GROUP>    55244 May 14 15:53 1708844501_740742.png
-rw-r--r-- 1 <USER>    <GROUP>    54756 May 14 15:53 1708844501_811313.png
-rw-r--r-- 1 <USER>    <GROUP>    54795 May 14 15:53 1708844501_883730.png
-rw-r--r-- 1 <USER>    <GROUP>    54756 May 14 15:53 1708844501_956946.png
-rw-r--r-- 1 <USER>    <GROUP>    54710 May 14 15:53 1708844502_028286.png
-rw-r--r-- 1 <USER>    <GROUP>    54650 May 14 15:53 1708844502_101495.png
-rw-r--r-- 1 <USER>    <GROUP>    54845 May 14 15:53 1708844502_172437.png
-rw-r--r-- 1 <USER>    <GROUP>    54865 May 14 15:53 1708844502_246358.png
-rw-r--r-- 1 <USER>    <GROUP>    54864 May 14 15:53 1708844502_317366.png
-rw-r--r-- 1 <USER>    <GROUP>    54320 May 14 15:53 1708844502_389651.png
-rw-r--r-- 1 <USER>    <GROUP>    54699 May 14 15:53 1708844502_462719.png
-rw-r--r-- 1 <USER>    <GROUP>    54581 May 14 15:53 1708844502_533490.png
-rw-r--r-- 1 <USER>    <GROUP>    55044 May 14 15:53 1708844502_606163.png
-rw-r--r-- 1 <USER>    <GROUP>    54775 May 14 15:53 1708844502_679788.png
-rw-r--r-- 1 <USER>    <GROUP>    54414 May 14 15:53 1708844502_750074.png
-rw-r--r-- 1 <USER>    <GROUP>    54259 May 14 15:53 1708844502_823039.png
-rw-r--r-- 1 <USER>    <GROUP>    54852 May 14 15:53 1708844502_895518.png
-rw-r--r-- 1 <USER>    <GROUP>    54772 May 14 15:53 1708844502_966727.png
-rw-r--r-- 1 <USER>    <GROUP>    53383 May 14 15:53 1708844503_039171.png
-rw-r--r-- 1 <USER>    <GROUP>    54531 May 14 15:53 1708844503_112472.png
-rw-r--r-- 1 <USER>    <GROUP>    53415 May 14 15:53 1708844503_183287.png
-rw-r--r-- 1 <USER>    <GROUP>    53798 May 14 15:53 1708844503_256545.png
-rw-r--r-- 1 <USER>    <GROUP>    54413 May 14 15:53 1708844503_327393.png
-rw-r--r-- 1 <USER>    <GROUP>    54023 May 14 15:53 1708844503_401577.png
-rw-r--r-- 1 <USER>    <GROUP>    54353 May 14 15:53 1708844503_472603.png
-rw-r--r-- 1 <USER>    <GROUP>    54935 May 14 15:53 1708844503_544294.png
-rw-r--r-- 1 <USER>    <GROUP>    55231 May 14 15:53 1708844503_617127.png
-rw-r--r-- 1 <USER>    <GROUP>    54636 May 14 15:53 1708844503_689037.png
-rw-r--r-- 1 <USER>    <GROUP>    53933 May 14 15:53 1708844503_760877.png
-rw-r--r-- 1 <USER>    <GROUP>    53893 May 14 15:53 1708844503_832977.png
-rw-r--r-- 1 <USER>    <GROUP>    54368 May 14 15:53 1708844503_905950.png
-rw-r--r-- 1 <USER>    <GROUP>    54735 May 14 15:53 1708844503_977732.png
-rw-r--r-- 1 <USER>    <GROUP>    54964 May 14 15:53 1708844504_049344.png
-rw-r--r-- 1 <USER>    <GROUP>    54823 May 14 15:53 1708844504_123323.png
-rw-r--r-- 1 <USER>    <GROUP>    54729 May 14 15:53 1708844504_193759.png
-rw-r--r-- 1 <USER>    <GROUP>    54805 May 14 15:53 1708844504_264731.png
-rw-r--r-- 1 <USER>    <GROUP>    55315 May 14 15:53 1708844504_339164.png
-rw-r--r-- 1 <USER>    <GROUP>    54302 May 14 15:53 1708844504_410087.png
-rw-r--r-- 1 <USER>    <GROUP>    54733 May 14 15:53 1708844504_482741.png
-rw-r--r-- 1 <USER>    <GROUP>    54847 May 14 15:53 1708844504_555909.png
-rw-r--r-- 1 <USER>    <GROUP>    54565 May 14 15:53 1708844504_628159.png
-rw-r--r-- 1 <USER>    <GROUP>    55220 May 14 15:53 1708844504_699851.png
-rw-r--r-- 1 <USER>    <GROUP>    54915 May 14 15:53 1708844504_771676.png
-rw-r--r-- 1 <USER>    <GROUP>    55060 May 14 15:53 1708844504_845035.png
-rw-r--r-- 1 <USER>    <GROUP>    54960 May 14 15:53 1708844504_917022.png
-rw-r--r-- 1 <USER>    <GROUP>    55208 May 14 15:53 1708844504_987984.png
-rw-r--r-- 1 <USER>    <GROUP>    54991 May 14 15:53 1708844505_060451.png
-rw-r--r-- 1 <USER>    <GROUP>    55351 May 14 15:53 1708844505_132063.png
-rw-r--r-- 1 <USER>    <GROUP>    55037 May 14 15:53 1708844505_203928.png
-rw-r--r-- 1 <USER>    <GROUP>    55562 May 14 15:53 1708844505_277067.png
-rw-r--r-- 1 <USER>    <GROUP>    55046 May 14 15:53 1708844505_348545.png
-rw-r--r-- 1 <USER>    <GROUP>    54926 May 14 15:53 1708844505_422047.png
-rw-r--r-- 1 <USER>    <GROUP>    54771 May 14 15:53 1708844505_492814.png
-rw-r--r-- 1 <USER>    <GROUP>    54974 May 14 15:53 1708844505_709440.png
-rw-r--r-- 1 <USER>    <GROUP>    54867 May 14 15:53 1708844505_782485.png
-rw-r--r-- 1 <USER>    <GROUP>    54748 May 14 15:53 1708844505_854558.png
-rw-r--r-- 1 <USER>    <GROUP>    54797 May 14 15:53 1708844505_926500.png
-rw-r--r-- 1 <USER>    <GROUP>    54835 May 14 15:53 1708844505_998897.png
-rw-r--r-- 1 <USER>    <GROUP>    54511 May 14 15:53 1708844506_071388.png
-rw-r--r-- 1 <USER>    <GROUP>    54381 May 14 15:53 1708844506_143099.png
-rw-r--r-- 1 <USER>    <GROUP>    54722 May 14 15:53 1708844506_215210.png
-rw-r--r-- 1 <USER>    <GROUP>    55077 May 14 15:53 1708844506_287872.png
-rw-r--r-- 1 <USER>    <GROUP>    55119 May 14 15:53 1708844506_359205.png
-rw-r--r-- 1 <USER>    <GROUP>    55020 May 14 15:53 1708844506_430480.png
-rw-r--r-- 1 <USER>    <GROUP>    54971 May 14 15:53 1708844506_504385.png
-rw-r--r-- 1 <USER>    <GROUP>    54695 May 14 15:53 1708844506_576891.png
-rw-r--r-- 1 <USER>    <GROUP>    54835 May 14 15:53 1708844506_648984.png
-rw-r--r-- 1 <USER>    <GROUP>    55128 May 14 15:53 1708844506_719365.png
-rw-r--r-- 1 <USER>    <GROUP>    55239 May 14 15:53 1708844506_791373.png
-rw-r--r-- 1 <USER>    <GROUP>    55272 May 14 15:53 1708844506_865777.png
-rw-r--r-- 1 <USER>    <GROUP>    54941 May 14 15:53 1708844506_937799.png
-rw-r--r-- 1 <USER>    <GROUP>    54998 May 14 15:53 1708844507_010204.png
-rw-r--r-- 1 <USER>    <GROUP>    54729 May 14 15:53 1708844507_080783.png
-rw-r--r-- 1 <USER>    <GROUP>    54745 May 14 15:53 1708844507_153035.png
-rw-r--r-- 1 <USER>    <GROUP>    54516 May 14 15:53 1708844507_225248.png
-rw-r--r-- 1 <USER>    <GROUP>    54432 May 14 15:53 1708844507_298235.png
-rw-r--r-- 1 <USER>    <GROUP>    54793 May 14 15:53 1708844507_370406.png
-rw-r--r-- 1 <USER>    <GROUP>    54613 May 14 15:53 1708844507_442104.png
-rw-r--r-- 1 <USER>    <GROUP>    54602 May 14 15:53 1708844507_514324.png
-rw-r--r-- 1 <USER>    <GROUP>    54224 May 14 15:53 1708844507_586134.png
-rw-r--r-- 1 <USER>    <GROUP>    54580 May 14 15:53 1708844507_658541.png
-rw-r--r-- 1 <USER>    <GROUP>    54184 May 14 15:53 1708844507_731841.png
-rw-r--r-- 1 <USER>    <GROUP>    54548 May 14 15:53 1708844507_804250.png
-rw-r--r-- 1 <USER>    <GROUP>    54250 May 14 15:53 1708844507_874555.png
-rw-r--r-- 1 <USER>    <GROUP>    54434 May 14 15:53 1708844507_946823.png
-rw-r--r-- 1 <USER>    <GROUP>    53903 May 14 15:53 1708844508_018773.png
-rw-r--r-- 1 <USER>    <GROUP>    53832 May 14 15:53 1708844508_091864.png
-rw-r--r-- 1 <USER>    <GROUP>    53604 May 14 15:53 1708844508_165220.png
-rw-r--r-- 1 <USER>    <GROUP>    53629 May 14 15:53 1708844508_236330.png
-rw-r--r-- 1 <USER>    <GROUP>    53643 May 14 15:53 1708844508_308957.png
-rw-r--r-- 1 <USER>    <GROUP>    53633 May 14 15:53 1708844508_379635.png
-rw-r--r-- 1 <USER>    <GROUP>    52859 May 14 15:53 1708844508_452262.png
-rw-r--r-- 1 <USER>    <GROUP>    52894 May 14 15:53 1708844508_598219.png
-rw-r--r-- 1 <USER>    <GROUP>    52937 May 14 15:53 1708844508_669664.png
-rw-r--r-- 1 <USER>    <GROUP>    52857 May 14 15:53 1708844508_741644.png
-rw-r--r-- 1 <USER>    <GROUP>    52870 May 14 15:53 1708844508_812874.png
-rw-r--r-- 1 <USER>    <GROUP>    52928 May 14 15:53 1708844508_885520.png
-rw-r--r-- 1 <USER>    <GROUP>    52845 May 14 15:53 1708844508_957251.png
-rw-r--r-- 1 <USER>    <GROUP>    53820 May 14 15:53 1708844509_030753.png
-rw-r--r-- 1 <USER>    <GROUP>    52932 May 14 15:53 1708844509_100669.png
-rw-r--r-- 1 <USER>    <GROUP>    52840 May 14 15:53 1708844509_173816.png
-rw-r--r-- 1 <USER>    <GROUP>    53836 May 14 15:53 1708844509_246845.png
-rw-r--r-- 1 <USER>    <GROUP>    53522 May 14 15:53 1708844509_318616.png
-rw-r--r-- 1 <USER>    <GROUP>    53595 May 14 15:53 1708844509_392061.png
-rw-r--r-- 1 <USER>    <GROUP>    52878 May 14 15:53 1708844509_463545.png
-rw-r--r-- 1 <USER>    <GROUP>    52870 May 14 15:53 1708844509_534365.png
-rw-r--r-- 1 <USER>    <GROUP>    52957 May 14 15:53 1708844509_606848.png
-rw-r--r-- 1 <USER>    <GROUP>    52837 May 14 15:53 1708844509_678173.png
-rw-r--r-- 1 <USER>    <GROUP>    52820 May 14 15:53 1708844509_752547.png
-rw-r--r-- 1 <USER>    <GROUP>    52864 May 14 15:53 1708844509_822957.png
-rw-r--r-- 1 <USER>    <GROUP>    52897 May 14 15:53 1708844509_896342.png
-rw-r--r-- 1 <USER>    <GROUP>    52875 May 14 15:53 1708844509_967832.png
-rw-r--r-- 1 <USER>    <GROUP>    52851 May 14 15:53 1708844510_041156.png
-rw-r--r-- 1 <USER>    <GROUP>    52813 May 14 15:53 1708844510_112992.png
-rw-r--r-- 1 <USER>    <GROUP>    52849 May 14 15:53 1708844510_184570.png
-rw-r--r-- 1 <USER>    <GROUP>    52852 May 14 15:53 1708844510_256667.png
-rw-r--r-- 1 <USER>    <GROUP>    52811 May 14 15:53 1708844510_328311.png
-rw-r--r-- 1 <USER>    <GROUP>    52935 May 14 15:53 1708844510_400890.png
-rw-r--r-- 1 <USER>    <GROUP>    52836 May 14 15:53 1708844510_472289.png
-rw-r--r-- 1 <USER>    <GROUP>    52842 May 14 15:53 1708844510_544525.png
-rw-r--r-- 1 <USER>    <GROUP>    52906 May 14 15:53 1708844510_618660.png
-rw-r--r-- 1 <USER>    <GROUP>    52872 May 14 15:53 1708844510_690379.png
-rw-r--r-- 1 <USER>    <GROUP>    52859 May 14 15:53 1708844510_761488.png
-rw-r--r-- 1 <USER>    <GROUP>    52871 May 14 15:53 1708844510_834299.png
-rw-r--r-- 1 <USER>    <GROUP>    52844 May 14 15:53 1708844510_907541.png
-rw-r--r-- 1 <USER>    <GROUP>    52880 May 14 15:53 1708844510_978484.png
-rw-r--r-- 1 <USER>    <GROUP>    52788 May 14 15:53 1708844511_051437.png
-rw-r--r-- 1 <USER>    <GROUP>    52769 May 14 15:53 1708844511_123525.png
-rw-r--r-- 1 <USER>    <GROUP>    53623 May 14 15:53 1708844511_196467.png
-rw-r--r-- 1 <USER>    <GROUP>    52774 May 14 15:53 1708844511_267575.png
-rw-r--r-- 1 <USER>    <GROUP>    52813 May 14 15:53 1708844511_338799.png
-rw-r--r-- 1 <USER>    <GROUP>    52831 May 14 15:53 1708844511_412806.png
-rw-r--r-- 1 <USER>    <GROUP>    52765 May 14 15:53 1708844511_483177.png
-rw-r--r-- 1 <USER>    <GROUP>    52851 May 14 15:53 1708844511_556598.png
-rw-r--r-- 1 <USER>    <GROUP>    52788 May 14 15:53 1708844511_628137.png
-rw-r--r-- 1 <USER>    <GROUP>    52819 May 14 15:53 1708844511_701063.png
-rw-r--r-- 1 <USER>    <GROUP>    52791 May 14 15:53 1708844511_772094.png
-rw-r--r-- 1 <USER>    <GROUP>    53487 May 14 15:53 1708844511_844683.png
-rw-r--r-- 1 <USER>    <GROUP>    52781 May 14 15:53 1708844511_917318.png
-rw-r--r-- 1 <USER>    <GROUP>    52845 May 14 15:53 1708844511_988764.png
-rw-r--r-- 1 <USER>    <GROUP>    52834 May 14 15:53 1708844512_062476.png
-rw-r--r-- 1 <USER>    <GROUP>    53305 May 14 15:53 1708844512_132426.png
-rw-r--r-- 1 <USER>    <GROUP>    53471 May 14 15:53 1708844512_204909.png
-rw-r--r-- 1 <USER>    <GROUP>    53355 May 14 15:53 1708844512_277813.png
-rw-r--r-- 1 <USER>    <GROUP>    53389 May 14 15:53 1708844512_350219.png
-rw-r--r-- 1 <USER>    <GROUP>    52799 May 14 15:53 1708844512_421987.png
-rw-r--r-- 1 <USER>    <GROUP>    52814 May 14 15:53 1708844512_494415.png
-rw-r--r-- 1 <USER>    <GROUP>    53849 May 14 15:53 1708844512_565892.png
-rw-r--r-- 1 <USER>    <GROUP>    52790 May 14 15:53 1708844512_639137.png
-rw-r--r-- 1 <USER>    <GROUP>    52827 May 14 15:53 1708844512_710836.png
-rw-r--r-- 1 <USER>    <GROUP>    52807 May 14 15:53 1708844512_782454.png
-rw-r--r-- 1 <USER>    <GROUP>    52871 May 14 15:53 1708844512_855996.png
-rw-r--r-- 1 <USER>    <GROUP>    52809 May 14 15:53 1708844512_927304.png
-rw-r--r-- 1 <USER>    <GROUP>    52880 May 14 15:53 1708844512_999859.png
-rw-r--r-- 1 <USER>    <GROUP>    52786 May 14 15:53 1708844513_071352.png
-rw-r--r-- 1 <USER>    <GROUP>    53977 May 14 15:53 1708844513_143733.png
-rw-r--r-- 1 <USER>    <GROUP>    53443 May 14 15:53 1708844513_215440.png
-rw-r--r-- 1 <USER>    <GROUP>    52810 May 14 15:53 1708844513_289214.png
-rw-r--r-- 1 <USER>    <GROUP>    53557 May 14 15:53 1708844513_359755.png
-rw-r--r-- 1 <USER>    <GROUP>    53419 May 14 15:53 1708844513_432639.png
-rw-r--r-- 1 <USER>    <GROUP>    53563 May 14 15:53 1708844513_504711.png
-rw-r--r-- 1 <USER>    <GROUP>    52806 May 14 15:53 1708844513_576535.png
-rw-r--r-- 1 <USER>    <GROUP>    52868 May 14 15:53 1708844513_649486.png
-rw-r--r-- 1 <USER>    <GROUP>    52816 May 14 15:53 1708844513_720661.png
-rw-r--r-- 1 <USER>    <GROUP>    53967 May 14 15:53 1708844513_792673.png
-rw-r--r-- 1 <USER>    <GROUP>    53533 May 14 15:53 1708844513_865997.png
-rw-r--r-- 1 <USER>    <GROUP>    54122 May 14 15:53 1708844513_938798.png
-rw-r--r-- 1 <USER>    <GROUP>    53545 May 14 15:53 1708844514_010417.png
-rw-r--r-- 1 <USER>    <GROUP>    53585 May 14 15:53 1708844514_080721.png
-rw-r--r-- 1 <USER>    <GROUP>    54395 May 14 15:53 1708844514_154315.png
-rw-r--r-- 1 <USER>    <GROUP>    54609 May 14 15:53 1708844514_225366.png
-rw-r--r-- 1 <USER>    <GROUP>    53694 May 14 15:53 1708844514_298683.png
-rw-r--r-- 1 <USER>    <GROUP>    53548 May 14 15:53 1708844514_371918.png
-rw-r--r-- 1 <USER>    <GROUP>    52807 May 14 15:53 1708844514_442783.png
-rw-r--r-- 1 <USER>    <GROUP>    53727 May 14 15:53 1708844514_516262.png
-rw-r--r-- 1 <USER>    <GROUP>    52880 May 14 15:53 1708844514_588585.png
-rw-r--r-- 1 <USER>    <GROUP>    52865 May 14 15:53 1708844514_660074.png
-rw-r--r-- 1 <USER>    <GROUP>    53945 May 14 15:53 1708844514_732340.png
-rw-r--r-- 1 <USER>    <GROUP>    53854 May 14 15:53 1708844514_802987.png
-rw-r--r-- 1 <USER>    <GROUP>    54096 May 14 15:53 1708844514_876952.png
-rw-r--r-- 1 <USER>    <GROUP>    54431 May 14 15:53 1708844514_948911.png
-rw-r--r-- 1 <USER>    <GROUP>    54286 May 14 15:53 1708844515_020123.png
-rw-r--r-- 1 <USER>    <GROUP>    54248 May 14 15:53 1708844515_092130.png
-rw-r--r-- 1 <USER>    <GROUP>    54209 May 14 15:53 1708844515_164398.png
-rw-r--r-- 1 <USER>    <GROUP>    54283 May 14 15:53 1708844515_237471.png
-rw-r--r-- 1 <USER>    <GROUP>    54464 May 14 15:53 1708844515_309115.png
-rw-r--r-- 1 <USER>    <GROUP>    54690 May 14 15:53 1708844515_381566.png
-rw-r--r-- 1 <USER>    <GROUP>    54825 May 14 15:53 1708844515_453049.png
-rw-r--r-- 1 <USER>    <GROUP>    54778 May 14 15:53 1708844515_525053.png
-rw-r--r-- 1 <USER>    <GROUP>    54831 May 14 15:53 1708844515_598079.png
-rw-r--r-- 1 <USER>    <GROUP>    54889 May 14 15:53 1708844515_670964.png
-rw-r--r-- 1 <USER>    <GROUP>    54733 May 14 15:53 1708844515_742513.png
-rw-r--r-- 1 <USER>    <GROUP>    54659 May 14 15:53 1708844515_815433.png
-rw-r--r-- 1 <USER>    <GROUP>    54996 May 14 15:53 1708844515_886800.png
-rw-r--r-- 1 <USER>    <GROUP>    54855 May 14 15:53 1708844515_958506.png
-rw-r--r-- 1 <USER>    <GROUP>    54951 May 14 15:53 1708844516_030510.png
-rw-r--r-- 1 <USER>    <GROUP>    54913 May 14 15:53 1708844516_103624.png
-rw-r--r-- 1 <USER>    <GROUP>    54787 May 14 15:53 1708844516_176163.png
-rw-r--r-- 1 <USER>    <GROUP>    54832 May 14 15:53 1708844516_247749.png
-rw-r--r-- 1 <USER>    <GROUP>    54262 May 14 15:53 1708844516_464539.png
-rw-r--r-- 1 <USER>    <GROUP>    54013 May 14 15:53 1708844516_537350.png
-rw-r--r-- 1 <USER>    <GROUP>    54177 May 14 15:53 1708844516_609355.png
-rw-r--r-- 1 <USER>    <GROUP>    54599 May 14 15:53 1708844516_680549.png
-rw-r--r-- 1 <USER>    <GROUP>    54426 May 14 15:53 1708844516_752553.png
-rw-r--r-- 1 <USER>    <GROUP>    54251 May 14 15:53 1708844516_825726.png
-rw-r--r-- 1 <USER>    <GROUP>    54346 May 14 15:53 1708844516_898013.png
-rw-r--r-- 1 <USER>    <GROUP>    54245 May 14 15:53 1708844516_968396.png
-rw-r--r-- 1 <USER>    <GROUP>    54091 May 14 15:53 1708844517_040849.png
-rw-r--r-- 1 <USER>    <GROUP>    54004 May 14 15:53 1708844517_112919.png
-rw-r--r-- 1 <USER>    <GROUP>    54054 May 14 15:53 1708844517_185637.png
-rw-r--r-- 1 <USER>    <GROUP>    53823 May 14 15:53 1708844517_257347.png
-rw-r--r-- 1 <USER>    <GROUP>    54072 May 14 15:53 1708844517_331113.png
-rw-r--r-- 1 <USER>    <GROUP>    53717 May 14 15:53 1708844517_401483.png
-rw-r--r-- 1 <USER>    <GROUP>    53737 May 14 15:53 1708844517_473139.png
-rw-r--r-- 1 <USER>    <GROUP>    53543 May 14 15:53 1708844517_545928.png
-rw-r--r-- 1 <USER>    <GROUP>    54041 May 14 15:53 1708844517_617912.png
-rw-r--r-- 1 <USER>    <GROUP>    52858 May 14 15:53 1708844517_691084.png
-rw-r--r-- 1 <USER>    <GROUP>    54444 May 14 15:53 1708844517_763965.png
-rw-r--r-- 1 <USER>    <GROUP>    54717 May 14 15:53 1708844517_835345.png
-rw-r--r-- 1 <USER>    <GROUP>    54594 May 14 15:53 1708844517_906742.png
-rw-r--r-- 1 <USER>    <GROUP>    54437 May 14 15:53 1708844517_980129.png
-rw-r--r-- 1 <USER>    <GROUP>    52819 May 14 15:53 1708844518_051572.png
-rw-r--r-- 1 <USER>    <GROUP>    54197 May 14 15:53 1708844518_124446.png
-rw-r--r-- 1 <USER>    <GROUP>    54185 May 14 15:53 1708844518_196327.png
-rw-r--r-- 1 <USER>    <GROUP>    54773 May 14 15:53 1708844518_267683.png
-rw-r--r-- 1 <USER>    <GROUP>    54656 May 14 15:53 1708844518_339825.png
-rw-r--r-- 1 <USER>    <GROUP>    54506 May 14 15:53 1708844518_411807.png
-rw-r--r-- 1 <USER>    <GROUP>    54474 May 14 15:53 1708844518_484711.png
-rw-r--r-- 1 <USER>    <GROUP>    54545 May 14 15:53 1708844518_556690.png
-rw-r--r-- 1 <USER>    <GROUP>    54555 May 14 15:53 1708844518_629810.png
-rw-r--r-- 1 <USER>    <GROUP>    54494 May 14 15:53 1708844518_700595.png
-rw-r--r-- 1 <USER>    <GROUP>    54382 May 14 15:53 1708844518_774150.png
-rw-r--r-- 1 <USER>    <GROUP>    54297 May 14 15:53 1708844518_845211.png
-rw-r--r-- 1 <USER>    <GROUP>    54286 May 14 15:53 1708844518_917981.png
-rw-r--r-- 1 <USER>    <GROUP>    54393 May 14 15:53 1708844518_989110.png
-rw-r--r-- 1 <USER>    <GROUP>    54279 May 14 15:53 1708844519_060264.png
-rw-r--r-- 1 <USER>    <GROUP>    52785 May 14 15:53 1708844519_134125.png
-rw-r--r-- 1 <USER>    <GROUP>    54214 May 14 15:53 1708844519_206419.png
-rw-r--r-- 1 <USER>    <GROUP>    54116 May 14 15:53 1708844519_279008.png
-rw-r--r-- 1 <USER>    <GROUP>    54603 May 14 15:53 1708844519_350868.png
-rw-r--r-- 1 <USER>    <GROUP>    52753 May 14 15:53 1708844519_421717.png
-rw-r--r-- 1 <USER>    <GROUP>    52840 May 14 15:53 1708844519_493667.png
-rw-r--r-- 1 <USER>    <GROUP>    52819 May 14 15:53 1708844519_567749.png
-rw-r--r-- 1 <USER>    <GROUP>    52852 May 14 15:53 1708844519_639924.png
-rw-r--r-- 1 <USER>    <GROUP>    52842 May 14 15:53 1708844519_710956.png
-rw-r--r-- 1 <USER>    <GROUP>    52817 May 14 15:53 1708844519_784523.png
-rw-r--r-- 1 <USER>    <GROUP>    52869 May 14 15:53 1708844519_855198.png
-rw-r--r-- 1 <USER>    <GROUP>    52879 May 14 15:53 1708844519_928062.png
-rw-r--r-- 1 <USER>    <GROUP>    53666 May 14 15:53 1708844520_000795.png
-rw-r--r-- 1 <USER>    <GROUP>    53788 May 14 15:53 1708844520_072008.png
-rw-r--r-- 1 <USER>    <GROUP>    53736 May 14 15:53 1708844520_145078.png
-rw-r--r-- 1 <USER>    <GROUP>    52870 May 14 15:53 1708844520_216208.png
-rw-r--r-- 1 <USER>    <GROUP>    53627 May 14 15:53 1708844520_288008.png
-rw-r--r-- 1 <USER>    <GROUP>    53843 May 14 15:53 1708844520_361002.png
-rw-r--r-- 1 <USER>    <GROUP>    52837 May 14 15:53 1708844520_433328.png
-rw-r--r-- 1 <USER>    <GROUP>    53762 May 14 15:53 1708844520_505464.png
-rw-r--r-- 1 <USER>    <GROUP>    53548 May 14 15:53 1708844520_577180.png
-rw-r--r-- 1 <USER>    <GROUP>    53610 May 14 15:53 1708844520_648081.png
-rw-r--r-- 1 <USER>    <GROUP>    52816 May 14 15:53 1708844520_719923.png
-rw-r--r-- 1 <USER>    <GROUP>    53774 May 14 15:53 1708844520_794088.png
-rw-r--r-- 1 <USER>    <GROUP>    53693 May 14 15:53 1708844520_867114.png
-rw-r--r-- 1 <USER>    <GROUP>    53631 May 14 15:53 1708844520_937942.png
-rw-r--r-- 1 <USER>    <GROUP>    52807 May 14 15:53 1708844521_011395.png
-rw-r--r-- 1 <USER>    <GROUP>    53914 May 14 15:53 1708844521_081314.png
-rw-r--r-- 1 <USER>    <GROUP>    53971 May 14 15:53 1708844521_154897.png
-rw-r--r-- 1 <USER>    <GROUP>    53826 May 14 15:53 1708844521_227302.png
-rw-r--r-- 1 <USER>    <GROUP>    52817 May 14 15:53 1708844521_299534.png
-rw-r--r-- 1 <USER>    <GROUP>    53454 May 14 15:53 1708844521_371237.png
-rw-r--r-- 1 <USER>    <GROUP>    52797 May 14 15:53 1708844521_442255.png
-rw-r--r-- 1 <USER>    <GROUP>    52781 May 14 15:53 1708844521_517516.png
-rw-r--r-- 1 <USER>    <GROUP>    52870 May 14 15:53 1708844521_587808.png
-rw-r--r-- 1 <USER>    <GROUP>    52811 May 14 15:53 1708844521_661391.png
-rw-r--r-- 1 <USER>    <GROUP>    52759 May 14 15:53 1708844521_732911.png
-rw-r--r-- 1 <USER>    <GROUP>    52824 May 14 15:53 1708844521_803729.png
-rw-r--r-- 1 <USER>    <GROUP>    52770 May 14 15:53 1708844521_876777.png
-rw-r--r-- 1 <USER>    <GROUP>    52828 May 14 15:53 1708844521_949244.png
-rw-r--r-- 1 <USER>    <GROUP>    52839 May 14 15:53 1708844522_021839.png
-rw-r--r-- 1 <USER>    <GROUP>    52888 May 14 15:53 1708844522_093960.png
-rw-r--r-- 1 <USER>    <GROUP>    52853 May 14 15:53 1708844522_238560.png
-rw-r--r-- 1 <USER>    <GROUP>    52851 May 14 15:53 1708844522_310200.png
-rw-r--r-- 1 <USER>    <GROUP>    52852 May 14 15:53 1708844522_382648.png
-rw-r--r-- 1 <USER>    <GROUP>    52813 May 14 15:53 1708844522_454292.png
-rw-r--r-- 1 <USER>    <GROUP>    52845 May 14 15:53 1708844522_525468.png
-rw-r--r-- 1 <USER>    <GROUP>    52887 May 14 15:53 1708844522_598049.png
-rw-r--r-- 1 <USER>    <GROUP>    52838 May 14 15:53 1708844522_670943.png
-rw-r--r-- 1 <USER>    <GROUP>    52789 May 14 15:53 1708844522_742543.png
-rw-r--r-- 1 <USER>    <GROUP>    52803 May 14 15:53 1708844522_815332.png
-rw-r--r-- 1 <USER>    <GROUP>    52830 May 14 15:53 1708844522_887207.png
-rw-r--r-- 1 <USER>    <GROUP>    52851 May 14 15:53 1708844522_959691.png
-rw-r--r-- 1 <USER>    <GROUP>    52840 May 14 15:53 1708844523_031389.png
-rw-r--r-- 1 <USER>    <GROUP>    52829 May 14 15:53 1708844523_103530.png
-rw-r--r-- 1 <USER>    <GROUP>    52780 May 14 15:53 1708844523_175102.png
-rw-r--r-- 1 <USER>    <GROUP>    52799 May 14 15:53 1708844523_247510.png
-rw-r--r-- 1 <USER>    <GROUP>    52784 May 14 15:53 1708844523_320721.png
-rw-r--r-- 1 <USER>    <GROUP>    52772 May 14 15:53 1708844523_392127.png
-rw-r--r-- 1 <USER>    <GROUP>    52827 May 14 15:53 1708844523_464378.png
-rw-r--r-- 1 <USER>    <GROUP>    52816 May 14 15:53 1708844523_537061.png
-rw-r--r-- 1 <USER>    <GROUP>    52893 May 14 15:53 1708844523_609028.png
-rw-r--r-- 1 <USER>    <GROUP>    52852 May 14 15:53 1708844523_681678.png
-rw-r--r-- 1 <USER>    <GROUP>    52798 May 14 15:53 1708844523_752833.png
-rw-r--r-- 1 <USER>    <GROUP>    52879 May 14 15:53 1708844523_826850.png
-rw-r--r-- 1 <USER>    <GROUP>    52807 May 14 15:53 1708844523_897767.png
-rw-r--r-- 1 <USER>    <GROUP>    52873 May 14 15:53 1708844523_969558.png
-rw-r--r-- 1 <USER>    <GROUP>    52865 May 14 15:53 1708844524_043440.png
-rw-r--r-- 1 <USER>    <GROUP>    52787 May 14 15:53 1708844524_114701.png
-rw-r--r-- 1 <USER>    <GROUP>    52796 May 14 15:53 1708844524_187161.png
-rw-r--r-- 1 <USER>    <GROUP>    52839 May 14 15:53 1708844524_258249.png
-rw-r--r-- 1 <USER>    <GROUP>    52862 May 14 15:53 1708844524_329906.png
-rw-r--r-- 1 <USER>    <GROUP>    52838 May 14 15:53 1708844524_402259.png
-rw-r--r-- 1 <USER>    <GROUP>    52805 May 14 15:53 1708844524_474491.png
-rw-r--r-- 1 <USER>    <GROUP>    52864 May 14 15:53 1708844524_619948.png
-rw-r--r-- 1 <USER>    <GROUP>    52843 May 14 15:53 1708844524_692581.png
-rw-r--r-- 1 <USER>    <GROUP>    52834 May 14 15:53 1708844524_763995.png
-rw-r--r-- 1 <USER>    <GROUP>    52852 May 14 15:53 1708844524_836633.png
-rw-r--r-- 1 <USER>    <GROUP>    52855 May 14 15:53 1708844524_909192.png
-rw-r--r-- 1 <USER>    <GROUP>    52846 May 14 15:53 1708844524_979780.png
-rw-r--r-- 1 <USER>    <GROUP>    52818 May 14 15:53 1708844525_052314.png
-rw-r--r-- 1 <USER>    <GROUP>    52797 May 14 15:53 1708844525_124914.png
-rw-r--r-- 1 <USER>    <GROUP>    52831 May 14 15:53 1708844525_196748.png
-rw-r--r-- 1 <USER>    <GROUP>    52882 May 14 15:53 1708844525_268094.png
-rw-r--r-- 1 <USER>    <GROUP>    52827 May 14 15:53 1708844525_340353.png
-rw-r--r-- 1 <USER>    <GROUP>    54265 May 14 15:53 1708844525_412601.png
-rw-r--r-- 1 <USER>    <GROUP>    53837 May 14 15:53 1708844525_485456.png
-rw-r--r-- 1 <USER>    <GROUP>    53532 May 14 15:53 1708844525_557483.png
-rw-r--r-- 1 <USER>    <GROUP>    52851 May 14 15:53 1708844525_629629.png
-rw-r--r-- 1 <USER>    <GROUP>    52837 May 14 15:53 1708844525_702289.png
-rw-r--r-- 1 <USER>    <GROUP>    52793 May 14 15:53 1708844525_773867.png
-rw-r--r-- 1 <USER>    <GROUP>    52892 May 14 15:53 1708844525_846684.png
-rw-r--r-- 1 <USER>    <GROUP>    52804 May 14 15:53 1708844525_919141.png
-rw-r--r-- 1 <USER>    <GROUP>    52895 May 14 15:53 1708844525_990844.png
-rw-r--r-- 1 <USER>    <GROUP>    52866 May 14 15:53 1708844526_062678.png
-rw-r--r-- 1 <USER>    <GROUP>    52822 May 14 15:53 1708844526_136318.png
-rw-r--r-- 1 <USER>    <GROUP>    52891 May 14 15:53 1708844526_206056.png
-rw-r--r-- 1 <USER>    <GROUP>    54316 May 14 15:53 1708844526_280626.png
-rw-r--r-- 1 <USER>    <GROUP>    52869 May 14 15:53 1708844526_352009.png
-rw-r--r-- 1 <USER>    <GROUP>    52814 May 14 15:53 1708844526_423339.png
-rw-r--r-- 1 <USER>    <GROUP>    52835 May 14 15:53 1708844526_493832.png
-rw-r--r-- 1 <USER>    <GROUP>    52859 May 14 15:53 1708844526_568163.png
-rw-r--r-- 1 <USER>    <GROUP>    52842 May 14 15:53 1708844526_641240.png
-rw-r--r-- 1 <USER>    <GROUP>    52800 May 14 15:53 1708844526_713105.png
-rw-r--r-- 1 <USER>    <GROUP>    52831 May 14 15:53 1708844526_786243.png
-rw-r--r-- 1 <USER>    <GROUP>    52897 May 14 15:53 1708844526_856940.png
-rw-r--r-- 1 <USER>    <GROUP>    52851 May 14 15:53 1708844526_929395.png
-rw-r--r-- 1 <USER>    <GROUP>    52892 May 14 15:53 1708844527_001900.png
-rw-r--r-- 1 <USER>    <GROUP>    52813 May 14 15:53 1708844527_072841.png
-rw-r--r-- 1 <USER>    <GROUP>    52798 May 14 15:53 1708844527_146927.png
-rw-r--r-- 1 <USER>    <GROUP>    52822 May 14 15:53 1708844527_219055.png
-rw-r--r-- 1 <USER>    <GROUP>    52802 May 14 15:53 1708844527_291022.png
-rw-r--r-- 1 <USER>    <GROUP>    52820 May 14 15:53 1708844527_360142.png
-rw-r--r-- 1 <USER>    <GROUP>    52812 May 14 15:53 1708844527_434439.png
-rw-r--r-- 1 <USER>    <GROUP>    52835 May 14 15:53 1708844527_506071.png
-rw-r--r-- 1 <USER>    <GROUP>    52823 May 14 15:53 1708844527_578395.png
-rw-r--r-- 1 <USER>    <GROUP>    52879 May 14 15:53 1708844527_650308.png
-rw-r--r-- 1 <USER>    <GROUP>    52758 May 14 15:53 1708844527_723347.png
-rw-r--r-- 1 <USER>    <GROUP>    52803 May 14 15:53 1708844527_795553.png
-rw-r--r-- 1 <USER>    <GROUP>    52904 May 14 15:53 1708844527_868050.png
-rw-r--r-- 1 <USER>    <GROUP>    52809 May 14 15:53 1708844527_939341.png
-rw-r--r-- 1 <USER>    <GROUP>    52908 May 14 15:53 1708844528_010856.png
-rw-r--r-- 1 <USER>    <GROUP>    52884 May 14 15:53 1708844528_083394.png
-rw-r--r-- 1 <USER>    <GROUP>    52883 May 14 15:53 1708844528_156048.png
-rw-r--r-- 1 <USER>    <GROUP>    52855 May 14 15:53 1708844528_228766.png
-rw-r--r-- 1 <USER>    <GROUP>    52885 May 14 15:53 1708844528_300619.png
-rw-r--r-- 1 <USER>    <GROUP>    52816 May 14 15:53 1708844528_373554.png
-rw-r--r-- 1 <USER>    <GROUP>    52816 May 14 15:53 1708844528_443971.png
-rw-r--r-- 1 <USER>    <GROUP>    52866 May 14 15:53 1708844528_518035.png
-rw-r--r-- 1 <USER>    <GROUP>    52885 May 14 15:53 1708844528_589051.png
-rw-r--r-- 1 <USER>    <GROUP>    52869 May 14 15:53 1708844528_661399.png
-rw-r--r-- 1 <USER>    <GROUP>    52780 May 14 15:53 1708844528_733747.png
-rw-r--r-- 1 <USER>    <GROUP>    52910 May 14 15:53 1708844528_805260.png
-rw-r--r-- 1 <USER>    <GROUP>    52801 May 14 15:53 1708844528_877742.png
-rw-r--r-- 1 <USER>    <GROUP>    52865 May 14 15:53 1708844528_949467.png
-rw-r--r-- 1 <USER>    <GROUP>    52859 May 14 15:53 1708844529_021386.png
-rw-r--r-- 1 <USER>    <GROUP>    52862 May 14 15:53 1708844529_094162.png
-rw-r--r-- 1 <USER>    <GROUP>    52781 May 14 15:53 1708844529_167227.png
-rw-r--r-- 1 <USER>    <GROUP>    52856 May 14 15:53 1708844529_238626.png
-rw-r--r-- 1 <USER>    <GROUP>    52868 May 14 15:53 1708844529_308031.png
-rw-r--r-- 1 <USER>    <GROUP>    52826 May 14 15:53 1708844529_382129.png
-rw-r--r-- 1 <USER>    <GROUP>    52854 May 14 15:53 1708844529_455428.png
-rw-r--r-- 1 <USER>    <GROUP>    52827 May 14 15:53 1708844529_527034.png
-rw-r--r-- 1 <USER>    <GROUP>    52890 May 14 15:53 1708844529_599029.png
-rw-r--r-- 1 <USER>    <GROUP>    53680 May 14 15:53 1708844529_670925.png
-rw-r--r-- 1 <USER>    <GROUP>    52815 May 14 15:53 1708844529_743555.png
-rw-r--r-- 1 <USER>    <GROUP>    52844 May 14 15:53 1708844529_815516.png
-rw-r--r-- 1 <USER>    <GROUP>    52874 May 14 15:53 1708844529_888125.png
-rw-r--r-- 1 <USER>    <GROUP>    52870 May 14 15:53 1708844529_960017.png
-rw-r--r-- 1 <USER>    <GROUP>    54257 May 14 15:53 1708844530_032107.png
-rw-r--r-- 1 <USER>    <GROUP>    54090 May 14 15:53 1708844530_104092.png
-rw-r--r-- 1 <USER>    <GROUP>    52800 May 14 15:53 1708844530_176322.png
-rw-r--r-- 1 <USER>    <GROUP>    52845 May 14 15:53 1708844530_248017.png
-rw-r--r-- 1 <USER>    <GROUP>    52806 May 14 15:53 1708844530_321415.png
-rw-r--r-- 1 <USER>    <GROUP>    52848 May 14 15:53 1708844530_393320.png
-rw-r--r-- 1 <USER>    <GROUP>    52848 May 14 15:53 1708844530_464912.png
-rw-r--r-- 1 <USER>    <GROUP>    54220 May 14 15:53 1708844530_537056.png
-rw-r--r-- 1 <USER>    <GROUP>    54163 May 14 15:53 1708844530_609066.png
-rw-r--r-- 1 <USER>    <GROUP>    54651 May 14 15:53 1708844530_681334.png
-rw-r--r-- 1 <USER>    <GROUP>    54385 May 14 15:53 1708844530_754383.png
-rw-r--r-- 1 <USER>    <GROUP>    54329 May 14 15:53 1708844530_826224.png
-rw-r--r-- 1 <USER>    <GROUP>    54223 May 14 15:53 1708844530_898230.png
-rw-r--r-- 1 <USER>    <GROUP>    54726 May 14 15:53 1708844530_971541.png
-rw-r--r-- 1 <USER>    <GROUP>    54305 May 14 15:53 1708844531_042026.png
-rw-r--r-- 1 <USER>    <GROUP>    54274 May 14 15:53 1708844531_113956.png
-rw-r--r-- 1 <USER>    <GROUP>    54211 May 14 15:53 1708844531_187739.png
-rw-r--r-- 1 <USER>    <GROUP>    54314 May 14 15:53 1708844531_258693.png
-rw-r--r-- 1 <USER>    <GROUP>    54520 May 14 15:53 1708844531_331643.png
-rw-r--r-- 1 <USER>    <GROUP>    52813 May 14 15:53 1708844531_404153.png
-rw-r--r-- 1 <USER>    <GROUP>    54219 May 14 15:53 1708844531_474652.png
-rw-r--r-- 1 <USER>    <GROUP>    54277 May 14 15:53 1708844531_548187.png
-rw-r--r-- 1 <USER>    <GROUP>    54483 May 14 15:53 1708844531_620011.png
-rw-r--r-- 1 <USER>    <GROUP>    52858 May 14 15:53 1708844531_692002.png
-rw-r--r-- 1 <USER>    <GROUP>    54288 May 14 15:53 1708844531_764706.png
-rw-r--r-- 1 <USER>    <GROUP>    54622 May 14 15:53 1708844531_835053.png
-rw-r--r-- 1 <USER>    <GROUP>    54683 May 14 15:53 1708844531_908043.png
-rw-r--r-- 1 <USER>    <GROUP>    54240 May 14 15:53 1708844531_979767.png
-rw-r--r-- 1 <USER>    <GROUP>    54501 May 14 15:53 1708844532_053659.png
-rw-r--r-- 1 <USER>    <GROUP>    54448 May 14 15:53 1708844532_125156.png
-rw-r--r-- 1 <USER>    <GROUP>    54507 May 14 15:53 1708844532_198313.png
-rw-r--r-- 1 <USER>    <GROUP>    54154 May 14 15:53 1708844532_268924.png
-rw-r--r-- 1 <USER>    <GROUP>    54746 May 14 15:53 1708844532_342230.png
-rw-r--r-- 1 <USER>    <GROUP>    54207 May 14 15:53 1708844532_413986.png
-rw-r--r-- 1 <USER>    <GROUP>    54585 May 14 15:53 1708844532_485988.png
-rw-r--r-- 1 <USER>    <GROUP>    52855 May 14 15:53 1708844532_558052.png
-rw-r--r-- 1 <USER>    <GROUP>    54497 May 14 15:53 1708844532_629857.png
-rw-r--r-- 1 <USER>    <GROUP>    54182 May 14 15:53 1708844532_702908.png
-rw-r--r-- 1 <USER>    <GROUP>    54657 May 14 15:53 1708844532_775203.png
-rw-r--r-- 1 <USER>    <GROUP>    54601 May 14 15:53 1708844532_846254.png
-rw-r--r-- 1 <USER>    <GROUP>    54237 May 14 15:53 1708844532_917751.png
-rw-r--r-- 1 <USER>    <GROUP>    54613 May 14 15:53 1708844532_990917.png

```

---

让我们查看其中一个可视化的图像：

---

```bash
display ../data/1708844416_1708844532/visualization/1708844470_055786.png || echo "无法显示图像"
```

```
bash: display: command not found
无法显示图像

```

---

我们已经成功实现了护栏点和线的可视化功能。通过分析和修改，我们成功地：

1. 添加了一个完整的可视化函数`VisualizeGuardrail`，该函数具有以下特点：
   - 基于OpenCV创建鸟瞰图显示护栏点和连线
   - 使用不同颜色区分检测点（红色）和跟踪点（绿色）
   - 添加了坐标系、网格线和图例标注
   - 包含时间戳信息

2. 修复了编译错误：
   - 解决了OpenCV库链接问题，通过正确配置CMakeLists.txt
   - 避免了RoadTrackerInitOptions重定义问题

3. 生成了可视化结果：
   - 每个帧的护栏点都被可视化为PNG图像
   - 图像保存在visualization目录中

可视化结果成功保存到了指定的文件夹中，虽然在容器中无法直接显示图像，但我们已经成功地将检测到的护栏点和跟踪的护栏点以不同颜色显示在BEV视图中，并添加了必要的图例和坐标信息。

这个可视化功能对于分析护栏检测和跟踪算法的效果非常有价值，可以直观地观察护栏点在VCS自车坐标系中的分布情况。

---

