<!-- Generated by SpecStory -->

# 抽取公共文件路径为变量 (2025-07-03 03:00Z)

_**User**_

将 代码中涉及到文件目录路径的公共部分（目前看下来应该是/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los）抽取出来，定义成一个变量，并且该变量通过 argv[1] 传递，执行 test  [path] 传递。

例如传入的是 path/data/input/，则提取出path/data 作为后续的directory的前缀。例如xxxx/workspace则变成 path/data + workspace

---

_**Assistant**_

我来分析代码中的文件路径，并帮您实现路径前缀的抽取和参数化。

首先让我查看代码中涉及的文件路径：

---

<details>
            <summary>Grep search for "/hfr/modules/HAF/lidar_obstacle_perception_segment" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L32 | `std::cout << "Usage:\n" << "  ./xxx_test /hfr/modules/HAF/lidar_obstacle_perception_segment/data/test/ FCTB IM_LS6" << std::endl;` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3788 | `std::string pcd_input_dir = "/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/V1.43_20250624_175450_manul_output/PointCloud_Hasco_pcd/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3789 | `std::string pose_input_dir = "/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/V1.43_20250624_175450_manul_output/LocalizationEstimate_txt/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3790 | `std::string polygon_input_dir = "/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/V1.43_20250624_175450_manul_output/MapPolygon_bin/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3791 | `std::string output_dir = "/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/V1.43_20250624_175450_manul_output/input/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3851 | `std::string obstacle_output_dir = "/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/workspace/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3869 | `std::string offline_grid_map_file_ = "/hfr/modules/HAF/lidar_obstacle_perception_segment/resource/segment/" + function_mode_ + "/" + car_name_ +"/grid_map.ot";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3956 | `std::string log_output_dir = "/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/workspace/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3965 | `std::string log_output_dir = "/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/workspace/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3972 | `std::string los_config_file_path = "/hfr/modules/HAF/lidar_obstacle_perception_segment/resource/segment/" + function_mode_ + "/" + car_name_ +"/los_config.json";` |

</details>

---

现在让我查看其他可能的路径：

---

<details>
            <summary>Grep search for "/hfr/data" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3802 | `std::string pcd_input_dir = "/hfr/data/SGMW/V1.43_20250616_193217-30cm_output/PointCloud_Hasco_pcd/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3803 | `std::string pose_input_dir = "/hfr/data/SGMW/V1.43_20250616_193217-30cm_output/LocalizationEstimate_txt/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3805 | `std::string output_dir = "/hfr/data/SGMW/V1.43_20250616_193217-30cm_output/input/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3816 | `std::string pcd_input_dir = "/hfr/data/SGMW/20241218/20241218_00006_acc/PointCloud_Hasco_pcd/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3817 | `std::string pose_input_dir = "/hfr/data/SGMW/20241218/20241218_00006_acc/Localization_txt/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3818 | `std::string polygon_input_dir = "/hfr/data/SGMW/20241218/20241218_00006_acc/MapPolygon_bin/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3819 | `std::string output_dir = "/hfr/data/SGMW/20241218/20241218_00006_acc/input/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3825 | `std::string pcd_input_dir = "/hfr/data/SGMW/20241211/20241211_161502_acc/PointCloud_Hasco_bin/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3826 | `std::string output_dir = "/hfr/data/SGMW/20241211/20241211_161502_acc/PointCloud_Hasco_pcd/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3832 | `std::string pcd_input_dir = "/hfr/data/20230827/01/20240827_164039_acc/PointCloud_Hasco_pcd/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3833 | `std::string output_dir = "/hfr/data/20230827/01/20240827_164039_acc/input/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3839 | `std::string pcd_input_dir = "/hfr/data/TS/20241129/01/PointCloud_Hasco_pcd/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3841 | `std::string output_dir = "/hfr/data/TS/20241129/01/input/";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3955 | `std::string log_file_path = "/hfr/data/LOW/20241018/1018_obs.txt";` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3964 | `std::string log_file_path = "/hfr/data/LOW/20241018/1018_obs.txt";` |

</details>

---

现在让我查看main函数的完整参数处理逻辑：

---

Read file: modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp

---

现在让我查看PrintUsage函数来了解当前的用法：

---

Read file: modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp

---

现在让我查看其他路径，比如hdmap相关的：

---

<details>
            <summary>Grep search for "/hfr/modules/HCR/map_resource" • **1** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3945 | `osm->open("/hfr/modules/HCR/map_resource/map/wuling/lanelet_map.osm", gps_base);` |
| `vscode-remote://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6870705f6465765f7765696c696e74227d/hfr/modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp` | L3949 | `opendrive.reset(new OpenDrive::OpenDrive("/hfr/modules/HCR/map_resource/map/hasco/hdmap.xodr"));` |

</details>

---

现在我理解了代码的结构。根据您的需求，我需要：

1. 修改参数处理逻辑，将第一个参数作为基础路径前缀
2. 从传入的路径中提取基础路径前缀
3. 将所有硬编码的路径替换为使用基础路径前缀的动态路径

让我实施这些修改：

---



---

现在修改main函数，添加基础路径提取逻辑：

---



---

现在修改offline_grid_map_file_路径：

---



---

修改hdmap相关路径：

---



---

修改log输出目录路径：

---



---

修改los_config_file_path：

---



---

现在修改那些被注释掉的测试代码段中的路径，使其也使用基础路径前缀。首先修改第一个测试段：

---



---

修改第二个测试段：

---



---

修改第三个测试段：

---



---

修改第四个测试段：

---



---

修改第五个测试段：

---



---

修改第六个测试段：

---



---

现在我需要修复一个问题。我需要确保在使用`base_path_prefix`之前，它已经被定义了。让我检查代码结构，看看是否有其他地方在main函数开始之前就使用了这些路径。

让我检查一下修改后的代码是否正确：

---

Read file: modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp

---

我发现了一个问题：在第88行，`offline_grid_map_file_`还是使用的旧的硬编码路径。让我修复这个问题：

---



---

现在还有一个问题：在注释掉的代码块中，`base_path_prefix`变量在定义之前就被使用了。我需要将这些代码块移到`base_path_prefix`定义之后。让我重新组织代码：

---



---

让我重新查看当前文件的结构：

---

Read file: modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp

---

我看到还有一些路径没有更新。让我修复剩余的硬编码路径。首先修复hdmap路径：

---



---

现在修复log相关路径：

---



---

让我分别修复这两个地方：

---

Read file: modules/HAF/lidar_obstacle_perception_segment/core/private/los/test/test.cpp

---

修复第一个log路径：

---



---

修复第二个log路径：

---



---



---

