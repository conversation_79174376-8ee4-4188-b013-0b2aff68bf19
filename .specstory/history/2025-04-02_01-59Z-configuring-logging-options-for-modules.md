<!-- Generated by SpecStory -->

# Configuring Logging Options for Modules (2025-04-02 01:59Z)

_**User**_

@msf_config.json 

在config文件中，在"log_config" 根下面增加按模块配置打印开关的选项，模块划分为：
- msf component
- sensor preprocess
- associate
- estimate
- data fusion
- track management

---

_**Assistant**_

Read file: modules/HAF/multi_sensor_obstacle_fusion/resource/msf_config.json

---

```diff
          "enable_debug_log": true,
          "enable_obstacle_info_log": false,
-         "enable_data_dump_to_file": false
+         "enable_data_dump_to_file": false,
+         "module_log_config": {
+             "msf_component": {
+                 "enable_log": true,
+                 "log_level": "INFO"
+             },
+             "sensor_preprocess": {
+                 "enable_log": true,
+                 "log_level": "INFO"
+             },
+             "associate": {
+                 "enable_log": true,
+                 "log_level": "INFO"
+             },
+             "estimate": {
+                 "enable_log": true,
+                 "log_level": "INFO"
+             },
+             "data_fusion": {
+                 "enable_log": true,
+                 "log_level": "INFO"
+             },
+             "track_management": {
+                 "enable_log": true,
+                 "log_level": "INFO"
+             }
+         }
      },
      "enable_log_to_console": false
```

---

在msf_config.json文件中已成功添加了按模块配置打印开关的选项。现在配置文件中包含了以下模块的日志配置：

1. msf_component - 多传感器融合组件
2. sensor_preprocess - 传感器预处理
3. associate - 关联算法
4. estimate - 估计算法
5. data_fusion - 数据融合
6. track_management - 跟踪管理

每个模块都有两个配置选项：
- enable_log：控制是否启用该模块的日志
- log_level：设置该模块的日志级别（INFO/DEBUG/WARN/ERROR）

所有模块的日志默认都已启用，日志级别设为INFO。您可以根据需要调整各模块的配置。

---

_**User**_

不用细分两层，只需要添加类似于：
- enable_msf_component_log
- enable_preprocess_log
- ...

---

_**Assistant**_

```diff
          "enable_obstacle_info_log": false,
          "enable_data_dump_to_file": false,
-         "module_log_config": {
-             "msf_component": {
-                 "enable_log": true,
-                 "log_level": "INFO"
-             },
-             "sensor_preprocess": {
-                 "enable_log": true,
-                 "log_level": "INFO"
-             },
-             "associate": {
-                 "enable_log": true,
-                 "log_level": "INFO"
-             },
-             "estimate": {
-                 "enable_log": true,
-                 "log_level": "INFO"
-             },
-             "data_fusion": {
-                 "enable_log": true,
-                 "log_level": "INFO"
-             },
-             "track_management": {
-                 "enable_log": true,
-                 "log_level": "INFO"
-             }
-         }
+         "enable_msf_component_log": true,
+         "enable_sensor_preprocess_log": true,
+         "enable_associate_log": true,
+         "enable_estimate_log": true,
+         "enable_data_fusion_log": true,
+         "enable_track_management_log": true
      },
      "enable_log_to_console": false
```

---

已按要求修改配置文件，将复杂的嵌套结构简化为直接的开关选项：

- enable_msf_component_log：MSF组件日志开关
- enable_sensor_preprocess_log：传感器预处理日志开关
- enable_associate_log：关联算法日志开关
- enable_estimate_log：估计算法日志开关
- enable_data_fusion_log：数据融合日志开关
- enable_track_management_log：跟踪管理日志开关

所有模块日志开关默认设置为true。这种扁平化的结构更简单直观，便于使用。

---

