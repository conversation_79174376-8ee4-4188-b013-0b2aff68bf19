<!-- Generated by SpecStory -->

# 创建带时间戳的二进制文件 (2025-06-13 03:00Z)

_**User**_

我希望每次新执行程序时，创建新的bin_file，命名规则为 rbt_data_timestamp.bin，timestamp为YYMMdd_HHmmss，获取current timestam

---

_**Assistant**_

我理解您的需求是希望每次程序执行时，创建一个新的二进制文件，文件名格式为`rbt_data_YYMMdd_HHmmss.bin`，其中时间戳表示当前的年月日和时分秒。我将修改相关代码来实现这个功能。

---

```diff
      return std::string(buffer);
  }
+ 
+ std::string getCurrentTimestampForFilename() {
+     std::time_t now = std::time(nullptr);
+     std::tm* localTime = std::localtime(&now);
+     char buffer[80];
+     // 定义文件名时间格式 YYMMdd_HHmmss
+     std::strftime(buffer, sizeof(buffer), "%y%m%d_%H%M%S", localTime);
+     return std::string(buffer);
+ }
  
  std::unique_ptr<std::ofstream> open_file_append(const std::string& file_name)
```

```diff
      location_ptr = std::shared_ptr<HBI::LocalizationEstimate>(new HBI::LocalizationEstimate);
      if (save_binary_file_enable) {
-         bin_file_name = "rbt_data.bin";
+         std::string timestamp = getCurrentTimestampForFilename();
+         bin_file_name = "rbt_data_" + timestamp + ".bin";
          bin_file_stream = open_file_append(bin_file_name);
      }
```

---

我已经修改了代码，添加了一个新的函数`getCurrentTimestampForFilename()`，用于生成符合您要求格式的时间戳字符串（YYMMdd_HHmmss）。

在`on_load()`函数中，当`save_binary_file_enable`为真时，程序会：
1. 获取当前时间戳
2. 将文件名格式化为`rbt_data_时间戳.bin`
3. 使用这个文件名创建新的二进制文件

这样，每次程序执行时都会创建一个带有当前时间戳的新文件，例如`rbt_data_240523_153045.bin`，表示2024年5月23日15点30分45秒创建的文件。

---

