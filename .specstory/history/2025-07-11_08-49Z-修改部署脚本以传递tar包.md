<!-- Generated by SpecStory -->

# 修改部署脚本以传递tar包 (2025-07-11 08:49Z)

_**User**_

修改部署脚本，当前的需求是不需要执行打包tar包的操作，这一步在前置动作中已经完成。因此tar.gz需要作为参数传递给脚本，给 $pkg_name 赋值

---

_**Assistant**_

我来帮您修改部署脚本，将tar包作为参数传递而不是在脚本中进行打包操作。

---

Read file: lcs_deploy.sh

---

现在我需要修改脚本，将tar包作为参数传递。让我修改脚本的参数检查部分和打包部分：

---



---

现在我需要删除打包相关的代码，并修改参数获取部分：

---



---

现在我需要修改后面的参数获取部分：

---



---

现在让我检查修改后的脚本是否正确：

---

Read file: lcs_deploy.sh

---



---



---



---



---



---



---



---

