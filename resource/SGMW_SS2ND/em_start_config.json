{"enable": "true", "enable_group": ["HFR", "HSF", "HAF", "HFF"], "group_priority": ["HFR", "HSF", "HAF", "HFF"], "memory_timeout": 10, "heart_timeout": 10, "cpu_timeout": 10, "disk_timeout": 10, "mainboard_path": "modules/mainboard/bin", "disk": [{"enable": "false", "path": "/hfr", "disk_load": 90}], "app_enable": [{"name": "iox-r<PERSON>i", "enable": "true"}, {"name": "hlog_service", "enable": "true"}, {"name": "main_driver_hy_default", "enable": "true"}, {"name": "config_service", "enable": "true"}, {"name": "libdata_transfer.so", "enable": "false"}, {"name": "libpointcloud_preprocess.so", "enable": "false"}, {"name": "liblidar_obstacle_perception_detect.so", "enable": "true"}, {"name": "liblidar_obstacle_perception_track.so", "enable": "true"}, {"name": "liblidar_obstacle_perception_segment.so", "enable": "true"}, {"name": "liblidar_obstacle_perception_fusion.so", "enable": "true"}, {"name": "libimu_service.so", "enable": "true"}, {"name": "libmap.so", "enable": "true"}, {"name": "libsgmw_map.so", "enable": "true"}, {"name": "libtime_service.so", "enable": "true"}, {"name": "libtraflight_perception_postprocess.so", "enable": "true"}, {"name": "libmulti_sensor_obstacle_fusion.so", "enable": "false"}, {"name": "libautocalib.so", "enable": "false"}, {"name": "libautocalib_multilidar.so", "enable": "false"}, {"name": "libfota_service.so", "enable": "false"}, {"name": "libhpp_calib_function.so", "enable": "false"}], "app": [{"name": "iox-r<PERSON>i", "type": "bin", "path": "", "parameter": "-c;roudi_config.toml", "enable_reset_notify": "true", "group": "HFR", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 2000000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "hlog_service", "type": "bin", "path": "modules/hlog_service/bin", "parameter": "modules/hlog_service/resource/config.txt", "enable_reset_notify": "true", "group": "HFR", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "main_driver_hy_default", "type": "bin", "path": "modules/3j3/bin", "parameter": "", "enable_reset_notify": "true", "group": "HFR", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "false", "cpu_load": 60}, {"name": "config_service", "type": "bin", "path": "modules/config_service/bin", "parameter": "--app=modules/config_service", "enable_reset_notify": "true", "group": "HFR", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "liblidar_service_hasco.so", "type": "lib", "path": "modules/lidar_service_hasco/bin", "parameter": "", "enable_reset_notify": "true", "group": "HSF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "run.sh", "type": "bin", "path": "modules/lidar_service_tool", "parameter": "", "enable_reset_notify": "true", "group": "HSF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libdata_transfer.so", "type": "lib", "path": "modules/data_transfer/bin", "parameter": "", "enable_reset_notify": "true", "group": "HFR", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "camera_service", "type": "bin", "path": "modules/camera_service/bin", "parameter": "", "enable_reset_notify": "true", "group": "HSF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "around_view_monitor", "type": "bin", "path": "modules/around_view_monitor/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libvehicle_service.so", "type": "lib", "path": "modules/vehicle_service/bin", "parameter": "", "enable_reset_notify": "true", "group": "HSF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libdead_reckoning.so", "type": "lib", "path": "modules/dead_reckoning/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "pointscloud_compensation", "type": "bin", "path": "modules/pointscloud_compensation/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "liblidar_obstacle_perception_detect.so", "type": "lib", "path": "modules/lidar_obstacle_perception_detect/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "liblidar_obstacle_perception_track.so", "type": "lib", "path": "modules/lidar_obstacle_perception_track/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "liblidar_obstacle_perception_segment.so", "type": "lib", "path": "modules/lidar_obstacle_perception_segment/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libpointcloud_preprocess.so", "type": "lib", "path": "modules/pointcloud_preprocess/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "liblidar_obstacle_perception_fusion.so", "type": "lib", "path": "modules/lidar_obstacle_perception_fusion/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libfusion_obstacle_perception.so", "type": "lib", "path": "modules/fusion_obstacle_perception/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "mechanical_slot_perception", "type": "bin", "path": "modules/mechanical_slot_perception/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "slot_manager", "type": "bin", "path": "modules/slot_manager/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libplanning.so", "type": "lib", "path": "modules/planning/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libhpp_calib_function.so", "type": "lib", "path": "modules/hpp_calib_function/bin", "parameter": "", "enable_reset_notify": "true", "group": "HFF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libhpp_hmi.so", "type": "lib", "path": "modules/hpp_hmi/bin", "parameter": "", "enable_reset_notify": "true", "group": "HFF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libcontrol.so", "type": "lib", "path": "modules/control/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libsgmw_map.so", "type": "lib", "path": "modules/sgmw_map/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libkey_control.so", "type": "lib", "path": "modules/key_control/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libautosweeper_service.so", "type": "lib", "path": "modules/vehicle_service_autosweeper/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libautosweeper_service_MCU.so", "type": "lib", "path": "modules/autosweeper_service_MCU/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libimu_service.so", "type": "lib", "path": "modules/imu_service/bin", "parameter": "", "enable_reset_notify": "true", "group": "HSF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libmap.so", "type": "lib", "path": "modules/map/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "liblidar_service_rs16.so", "type": "lib", "path": "modules/lidar_service_rs16/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libtime_service.so", "type": "lib", "path": "modules/time_service/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libfusion_localization.so", "type": "lib", "path": "modules/fusion_localization/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "false", "cpu_load": 60}, {"name": "libtraflight_perception_postprocess.so", "type": "lib", "path": "modules/traflight_perception_postprocess/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "false", "cpu_load": 60}, {"name": "libmulti_sensor_obstacle_fusion.so", "type": "lib", "path": "modules/multi_sensor_obstacle_fusion/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "false", "cpu_load": 60}, {"name": "libfota_service.so", "type": "lib", "path": "modules/fota_service/bin", "parameter": "", "enable_reset_notify": "true", "group": "HFR", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libautocalib.so", "type": "lib", "path": "modules/autocalib/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libautocalib_multilidar.so", "type": "lib", "path": "modules/autocalib_multilidar/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}]}