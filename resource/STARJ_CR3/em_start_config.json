{"enable": "true", "enable_group": ["HFR", "HSF", "HAF", "HFF"], "group_priority": ["HFR", "HSF", "HAF", "HFF"], "memory_timeout": 10, "heart_timeout": 10, "cpu_timeout": 10, "disk_timeout": 10, "mainboard_path": "modules/mainboard/bin", "disk": [{"enable": "false", "path": "/hfr", "disk_load": 90}], "app_enable": [{"name": "iox-r<PERSON>i", "enable": "true"}, {"name": "hlog_service", "enable": "true"}, {"name": "config_service", "enable": "true"}, {"name": "liblidar_service_hasco.so", "enable": "true"}, {"name": "libdata_transfer.so", "enable": "true"}, {"name": "libcamera_service.so", "enable": "true"}, {"name": "pointscloud_compensation", "enable": "false"}, {"name": "liblidar_obstacle_perception_detect.so", "enable": "true"}, {"name": "liblidar_obstacle_perception_track.so", "enable": "true"}, {"name": "liblidar_obstacle_perception_segment.so", "enable": "true"}, {"name": "liblidar_obstacle_perception_fusion.so", "enable": "true"}, {"name": "liblidar_curb_perception_segment.so", "enable": "true"}, {"name": "libroad_boundary_perception_track.so", "enable": "false"}, {"name": "libpointcloud_preprocess.so", "enable": "false"}, {"name": "libplanning.so", "enable": "true"}, {"name": "libhpp_function.so", "enable": "true"}, {"name": "libhpp_hmi.so", "enable": "true"}, {"name": "libcontrol.so", "enable": "true"}, {"name": "libimu_service.so", "enable": "true"}, {"name": "libtime_service.so", "enable": "true"}, {"name": "libmap.so", "enable": "false"}, {"name": "libqrcodelocalization.so", "enable": "false"}, {"name": "libfusion_localization_cargovehicle.so", "enable": "true"}, {"name": "libfusion_localization.so", "enable": "false"}, {"name": "libmqtt_service.so", "enable": "false"}, {"name": "libvideo_stream_service.so", "enable": "false"}, {"name": "libhpp_calib_function.so", "enable": "false"}, {"name": "libautocalib.so", "enable": "false"}, {"name": "libCR3Chassis_service.so", "enable": "true"}, {"name": "libautocalib_multilidar.so", "enable": "false"}, {"name": "libgRPC_service.so", "enable": "true"}], "app": [{"name": "iox-r<PERSON>i", "type": "bin", "path": "", "parameter": "-c;roudi_config.toml", "enable_reset_notify": "true", "group": "HFR", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 2000000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "hlog_service", "type": "bin", "path": "modules/hlog_service/bin", "parameter": "modules/hlog_service/resource/config.txt", "enable_reset_notify": "true", "group": "HFR", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "config_service", "type": "bin", "path": "modules/config_service/bin", "parameter": "--app=modules/config_service", "enable_reset_notify": "true", "group": "HFR", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "liblidar_service_hasco.so", "type": "lib", "path": "modules/lidar_service_hasco/bin", "parameter": "", "enable_reset_notify": "true", "group": "HSF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libdata_transfer.so", "type": "lib", "path": "modules/data_transfer/bin", "parameter": "", "enable_reset_notify": "true", "group": "HFR", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libcamera_service.so", "type": "lib", "path": "modules/camera_service/bin", "parameter": "", "enable_reset_notify": "true", "group": "HSF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libCR3Chassis_service.so", "type": "lib", "path": "modules/CR3Chassis_service/bin", "parameter": "", "enable_reset_notify": "true", "group": "HSF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "pointscloud_compensation", "type": "bin", "path": "modules/pointscloud_compensation/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "liblidar_obstacle_perception_detect.so", "type": "lib", "path": "modules/lidar_obstacle_perception_detect/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "liblidar_obstacle_perception_track.so", "type": "lib", "path": "modules/lidar_obstacle_perception_track/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "liblidar_obstacle_perception_segment.so", "type": "lib", "path": "modules/lidar_obstacle_perception_segment/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "liblidar_obstacle_perception_fusion.so", "type": "lib", "path": "modules/lidar_obstacle_perception_fusion/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libpointcloud_preprocess.so", "type": "lib", "path": "modules/pointcloud_preprocess/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libvideo_stream_service.so", "type": "lib", "path": "modules/video_streaming_service/bin", "parameter": "", "enable_reset_notify": "true", "group": "HSF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libplanning.so", "type": "lib", "path": "modules/planning/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libhpp_function.so", "type": "lib", "path": "modules/hpp_function/bin", "parameter": "", "enable_reset_notify": "true", "group": "HFF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libhpp_hmi.so", "type": "lib", "path": "modules/hpp_hmi/bin", "parameter": "", "enable_reset_notify": "true", "group": "HFF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libcontrol.so", "type": "lib", "path": "modules/control/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libhpp_calib_function.so", "type": "lib", "path": "modules/hpp_calib_function/bin", "parameter": "", "enable_reset_notify": "true", "group": "HFF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libautocalib.so", "type": "lib", "path": "modules/autocalib/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libautocalib_multilidar.so", "type": "lib", "path": "modules/autocalib_multilidar/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libimu_service.so", "type": "lib", "path": "modules/imu_service/bin", "parameter": "", "enable_reset_notify": "true", "group": "HSF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libtime_service.so", "type": "lib", "path": "modules/time_service/bin", "parameter": "", "enable_reset_notify": "true", "group": "HSF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libmap.so", "type": "lib", "path": "modules/map/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libqrcodelocalization.so", "type": "lib", "path": "modules/qrcodelocalization/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "libfusion_localization_cargovehicle.so", "type": "lib", "path": "modules/fusion_localization_cargovehicle/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "false", "cpu_load": 60}, {"name": "libfusion_localization.so", "type": "lib", "path": "modules/fusion_localization/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "false", "cpu_load": 60}, {"name": "libmqtt_service.so", "type": "lib", "path": "modules/mqtt_service/bin", "parameter": "", "enable_reset_notify": "true", "group": "HFR", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "true", "cpu_load": 60}, {"name": "liblidar_curb_perception_segment.so", "type": "lib", "path": "modules/lidar_curb_perception_segment/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "false", "cpu_load": 60}, {"name": "libroad_boundary_perception_track.so", "type": "lib", "path": "modules/road_boundary_perception_track/bin", "parameter": "", "enable_reset_notify": "true", "group": "HAF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "false", "cpu_load": 60}, {"name": "libgRPC_service.so", "type": "lib", "path": "modules/gRPC_service/bin", "parameter": "", "enable_reset_notify": "true", "group": "HSF", "enable_retry": "true", "retry_cnt": 3, "enable_memory": "false", "memory_size": 100000, "enable_heart": "false", "enable_cpu": "false", "cpu_load": 60}]}