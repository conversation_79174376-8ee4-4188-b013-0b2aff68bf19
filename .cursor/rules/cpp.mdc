---
description: 
globs: *.hpp,*.h,*.cpp,CMakeLists.txt,*.cmake,Makefile
alwaysApply: false
---
---
  description:
  globs: /*.c,/*.cpp,**/*.h,/*.hpp,/*.cxx,CMakeLists.txt,*.cmake,conanfile.txt,Makefil,**/*.cc
  alwaysApply: false
---
  C++ 编程指南

  基本原则

   - 所有代码注释都应使用英文。
   - 始终为每个变量和函数（包括参数和返回值）声明其类型。
   - 根据需要创建类型和类。
   - 使用 Doxygen 风格的注释来文档化公开的类和方法。
   - 不要在函数体内部留下不必要的空行。
   - 遵循“单一定义规则”（One-Definition Rule, ODR）。

  命名规范

   - 类与结构体：使用 PascalCase (大驼峰命名法)。
   - 变量、函数与方法：使用 snake_case (下划线命名法)。
   - 常量与宏：使用 ALL_CAPS (全大写下划线命名法)。
   - 文件与目录名：使用 snake_case (下划线命名法)。
   - 环境变量：使用 UPPERCASE (全大写)。
   - 避免使用“魔术数字”，应定义为常量。
   - 函数名应以动词开头。
   - 布尔变量名应体现其逻辑状态，例如：is_loading, has_error, can_delete 等。
   - 使用完整的单词而非缩写，并确保拼写正确。
     - 标准缩写除外，如 API, URL 等。
     - 广为人知的缩写除外：
       - i, j, k 用于循环。
       - err 用于错误。
       - ctx 用于上下文。
       - req, res 用于请求/响应参数。

  函数

   - 尽量编写简短且功能单一的函数。
   - 函数名应由一个动词和描述性名词组成。
   - 如果函数返回布尔值，名称应使用 is_x、has_x、can_x 等形式。
   - 如果函数没有返回值 (void)，名称应使用 execute_x、save_x 等形式。
   - 通过以下方式避免代码块的深度嵌套：
     - 使用前置检查并提早返回 (Early checks and returns)。
     - 将逻辑提取到工具函数中。
   - 使用标准库算法（如 std::for_each, std::transform, std::find）来简化或避免循环嵌套。
   - 对简单的操作使用 Lambda 函数。
   - 对复杂的操作使用具名函数。
   - 优先使用默认参数值，而不是在函数体内检查 null 或 nullptr。
   - 使用结构体或类来减少函数参数的数量：
     - 使用对象传递多个参数。
     - 使用对象返回多个结果。
     - 为输入参数和输出结果声明必要的类型。
   - 保持单一的抽象层级。

  数据

   - 不要滥用基本数据类型，应将数据封装在复合类型中。
   - 避免在函数中进行数据校验，应使用带有内部校验逻辑的类。
   - 优先考虑数据的不可变性。
   - 对不会改变的数据使用 const。
   - 对编译期常量使用 constexpr。
   - 对可能为空的值使用 std::optional。

  类

   - 遵循 SOLID 原则。
   - 优先使用组合而非继承。
   - 将接口声明为抽象类或概念 (Concepts)。
   - 编写小而专一的类：
     - 建议少于 500 行代码。
     - 少于 10 个公开方法。
     - 少于 10 个属性。
   - 遵循“五法则”（Rule of Five）或“零法则”（Rule of Zero）进行资源管理。
   - 将成员变量设为私有 (private)，并在必要时提供 getter/setter 方法。
   - 在成员函数中贯彻 const 正确性。

  异常

   - 使用异常来处理非预期的错误。
   - 捕获异常的目的应该是：
     - 修复一个可预期的已知问题。
     - 为异常添加更多上下文信息后重新抛出。
     - 否则，应交由全局处理器处理。
   - 对可预期的失败，应使用 std::optional、std::expected 或错误码。

  内存管理

   - 优先使用智能指针（std::unique_ptr, std::shared_ptr）而非裸指针。
   - 遵循 RAII (资源获取即初始化) 原则。
   - 通过恰当的资源管理避免内存泄漏。
   - 使用 std::vector 和其他标准容器，而不是C风格的数组。

  测试

   - 遵循 Arrange-Act-Assert (组织-执行-断言) 模式编写测试。
   - 清晰地命名测试变量。
   - 遵循命名约定：input_x, mock_x, actual_x, expected_x 等。
   - 为每个公开函数编写单元测试。
   - 使用测试替身 (Test Doubles) 来模拟依赖。
     - 执行成本不高的第三方依赖除外。
   - 为每个模块编写集成测试。
   - 遵循 Given-When-Then (给定-当-则) 模式。

  项目结构

   - 采用模块化架构。
   - 将代码组织到逻辑清晰的目录中：
     - include/ 用于头文件。
     - src/ 用于源文件。
     - test/ 用于测试文件。
     - lib/ 用于库文件。
     - doc/ 用于文档。
   - 使用 CMake 或类似的构建系统。
   - 将接口 (.h/.hpp) 与实现 (.cpp) 分离。
   - 使用命名空间 (namespaces) 来组织代码逻辑。
   - 创建一个 core 命名空间用于存放核心基础组件。
   - 创建一个 utils 命名空间用于存放工具函数。

  标准库

   - 尽可能使用 C++ 标准库，较长一段时间内都是基于C++14规范开发。
   - 优先使用 std::string 而非 C 风格字符串。
   - 使用 std::vector, std::map, std::unordered_map 等容器。
   - 使用 std::optional, std::variant, std::any 来实现现代化的类型安全。
   - 使用 std::filesystem 进行文件操作。
   - 使用 std::chrono 进行时间相关的操作。

  并发

   - 尽量不要在算法模块中引入并发编程操作，如果确实需要请和我确认。
   - 使用 std::thread, std::mutex, std::lock_guard 来保证线程安全。
   - 优先选择基于任务的并行（Task-based parallelism）而非基于线程的并行。
   - 使用 std::atomic 进行原子操作。
   - 通过恰当的同步机制避免数据竞争。
   - 在必要时使用线程安全的数据结构。
