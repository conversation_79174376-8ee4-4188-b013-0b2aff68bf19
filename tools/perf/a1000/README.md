### profile数据采集

#### 1. 准备
- step1：下载安装包

下载地址：hfr/tools/perf/a1000/profile.tar.gz

- step2：将上述下载的压缩包push到板端并解压

```bash
root@a1000:/userdata/profile# ls -l
total 7912
drwxr-xr-x 6 <USER> <GROUP>    4096 Jul 19  2024 FlameGraph
-rwxr-xr-x 1  <USER>  <GROUP>    8374 Jul 22  2024 perf_a1000.sh
-rw-rw-rw- 1 <USER> <GROUP> 7847958 Jul 29  2024 perf.tar.gz
-rw-r--r-- 1 <USER> <GROUP>  206848 Jul 19  2024 perl_lib.tar
-rwxr-xr-x 1 <USER> <GROUP>    4239 Aug  5  2024 profiling.sh
-rwxr-xr-x 1 <USER> <GROUP>     913 Aug  5  2024 uninstall.sh
root@a1000:/userdata/profile#
```

#### 2.1 手动采集
适用于明确知道需要采集哪个模块的性能数据。
- step3：运行install脚本
```
./install.sh
```
- step4：运行hfr
- step5：运行perf_a1000.sh脚本立即开始采集，采集的数据位于当前Output目录下。
```
# ./perf_a1000.sh --cpu -m {模块名} -f {采样频率(/hz)} -t {采样时间(/s)}
./perf_a1000.sh --cpu -m planning -f 99 -t 100
```
时间到了自动生成火焰图，中途Ctrl+C只会生成perf.data数据

#### 2.2 自动采集
适用于不知道系统的哪些模块什么时候会达到设定的cpu阈值

- step3：后台运行profiling.sh脚本（当达到设定的cpu/mem阈值（默认cpu：50%，mem：20%）时，会自动采集对应进程60s的性能数据（只采一次），可通过-h查看如何设置阈值）

```bash
# 后台运行profiling.sh
root@a1000:/userdata/profile# nohup ./profiling.sh &
[1] 855
nohup: ignoring input and appending output to 'nohup.out'
root@a1000:/userdata/profile#
```

```bash
root@a1000:/userdata/profile# ./profiling.sh -h
Usage: ./profiling.sh [options] ...
    -c, --cpu           specify cpu threshold(%)
    -m, --mem           specify memory threshold(%)
    -i, --interval      specify profiling interval time(s)
OPTIONS:
    -h, --help          Display this help and exit.
Example :
     ./profiling.sh or ./profiling.sh -c 60 -m 15 or ./profiling.sh -c 55 -m 25 -i 3 or ./profiling.sh -c 65 -i 2
root@a1000:/userdata/profile#
```

- step4：运行hfr

#### 3. 输出

采集的数据位于当前Output目录下，下载到本地浏览器打开*.svg火焰图。

```bash
root@a1000:/userdata/profile/Output# ls
test_99_60_cpu_cycles_perf.data  test_99_60_cpu_cycles_perf.svg
root@a1000:/userdata/profile/Output#
```

#### 4. 卸载
不再需要perf采集后，执行uninstall.sh卸载相关依赖。

