{"repositorys": [{"srcDir": "modules/HFR/components_and_data_center", "srcBranch": "dev", "libDir": "lib/components_and_data_center", "libBranch": "release"}, {"srcDir": "modules/HFR/hlog", "srcBranch": "dev", "libDir": "lib/hlog", "libBranch": "release"}, {"srcDir": "modules/HFR/em_interface", "srcBranch": "dev", "libDir": "lib/execution_manifest", "libBranch": "release"}, {"srcDir": "modules/HAF/around_view_monitor_private", "srcBranch": "dev", "libDir": "lib/around_view_monitor", "libBranch": "release"}, {"srcDir": "modules/HAF/space_slot_perception_private", "srcBranch": "dev", "libDir": "lib/space_slot_perception", "libBranch": "release"}, {"srcDir": "modules/HAF/fusion_obstacle_perception_private", "srcBranch": "dev", "libDir": "lib/fusion_obstacle_perception", "libBranch": "release"}, {"srcDir": "modules/HAF/dead_reckoning_private", "srcBranch": "release", "libDir": "lib/dead_reckoning", "libBranch": "release"}, {"srcDir": "modules/HAF/line_slot_perception_private", "srcBranch": "dev", "libDir": "lib/line_slot_perception", "libBranch": "release"}, {"srcDir": "modules/HAF/map_private", "srcBranch": "release", "libDir": "lib/map", "libBranch": "release"}, {"srcDir": "modules/HAF/fusion_localization_private", "srcBranch": "release", "libDir": "lib/fusion_localization", "libBranch": "release"}, {"srcDir": "modules/HAF/apa_planning_private", "srcBranch": "master", "libDir": "lib/planning", "libBranch": "release"}, {"srcDir": "modules/HAF/control_private", "srcBranch": "dev", "libDir": "lib/control", "libBranch": "release"}, {"srcDir": "modules/HAF/lidar_obstacle_perception_track", "srcBranch": "release", "libDir": "lib/lidar_obstacle_perception_track", "libBranch": "release"}, {"srcDir": "modules/HAF/lidar_obstacle_perception_detect", "srcBranch": "release", "libDir": "lib/lidar_obstacle_perception_detect", "libBranch": "release"}, {"srcDir": "modules/HAF/lidar_obstacle_perception_segment", "srcBranch": "release", "libDir": "lib/lidar_obstacle_perception_segment", "libBranch": "release"}, {"srcDir": "modules/HAF/perception_stereoparkingslot_core", "srcBranch": "dev", "libDir": "lib/stereoparkingslot", "libBranch": "release"}]}