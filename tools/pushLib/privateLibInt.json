{"repositorys": [{"srcDir": "modules/HFR/components_and_data_center", "srcBranch": "release", "libDir": "lib/components_and_data_center", "libBranch": "release"}, {"srcDir": "modules/HFR/hlog", "srcBranch": "release", "libDir": "lib/hlog", "libBranch": "release"}, {"srcDir": "modules/HFR/em_interface", "srcBranch": "release", "libDir": "lib/execution_manifest", "libBranch": "release"}, {"srcDir": "modules/HAF/private_core_lidar_obstacle_perception_detect", "srcBranch": "release", "libDir": "lib/lidar_obstacle_perception_detect", "libBranch": "release"}, {"srcDir": "modules/HAF/private_core_lidar_obstacle_perception_segment", "srcBranch": "release", "libDir": "lib/lidar_obstacle_perception_segment", "libBranch": "release"}, {"srcDir": "modules/HAF/private_core_lidar_obstacle_perception_track", "srcBranch": "release", "libDir": "lib/lidar_obstacle_perception_track", "libBranch": "release"}, {"srcDir": "modules/HAF/private_core_fusion_obstacle_perception", "srcBranch": "release", "libDir": "lib/fusion_obstacle_perception", "libBranch": "release"}, {"srcDir": "modules/HAF/private_core_multi_sensor_obstacle_fusion", "srcBranch": "release", "libDir": "lib/multi_sensor_obstacle_fusion", "libBranch": "release"}, {"srcDir": "modules/HAF/private_core_traflight_perception_postprocess", "srcBranch": "release", "libDir": "lib/traflight_perception_postprocess", "libBranch": "release"}, {"srcDir": "modules/HAF/private_agent_prediction", "srcBranch": "release", "libDir": "lib/agent_prediction", "libBranch": "release"}, {"srcDir": "modules/HAF/private_stereoparkingslotscanning", "srcBranch": "release", "libDir": "lib/stereoparkingslot", "libBranch": "release"}, {"srcDir": "modules/HAF/planning", "srcBranch": "release", "libDir": "lib/planning", "libBranch": "release"}, {"srcDir": "modules/HSF/private_core_calibration", "srcBranch": "release", "libDir": "lib/autocalib", "libBranch": "release"}, {"srcDir": "modules/HSF/dead_reckoning_private", "srcBranch": "release", "libDir": "lib/dead_reckoning", "libBranch": "release"}, {"srcDir": "modules/HSF/map_private", "srcBranch": "release", "libDir": "lib/map", "libBranch": "release"}, {"srcDir": "modules/HSF/sgmwMap_private", "srcBranch": "release", "libDir": "lib/sgmwMap", "libBranch": "release"}, {"srcDir": "modules/HSF/fusion_localization_private", "srcBranch": "release", "libDir": "lib/fusion_localization", "libBranch": "release"}, {"srcDir": "modules/HSF/fusion_localization_cargovehicle_private", "srcBranch": "release", "libDir": "lib/fusion_localization_cargovehicle", "libBranch": "release"}, {"srcDir": "modules/HSF/qrcodelocalization_private", "srcBranch": "release", "libDir": "lib/qrcode", "libBranch": "release"}, {"srcDir": "modules/HAF/control_private", "srcBranch": "release", "libDir": "lib/control", "libBranch": "release"}]}