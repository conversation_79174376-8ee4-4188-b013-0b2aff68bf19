import subprocess  
import json  
import sys
 


# 定义变量存储解析信息
class interfaceInfo:
    def __init__(self,dir,branch,udt,upLog):
        self.dir = dir  
        self.branch = branch  
        self.udt = udt  
        self.upLog = upLog       

interfaceItem=interfaceInfo("undef","unkonw",0,"unkonw")
def printInterfaceItem():
    print("Interface dir:%s, branch %s, uptime %s, log %s" %(interfaceItem.dir,interfaceItem.branch,interfaceItem.udt,interfaceItem.upLog) )

class repoItem:  
    def __init__(self, srcDir,srcBranch,srcUpdt,srcUpLog,libDir,libBranch,libUpdt,libUpLog,updateSts):  
        self.srcDir = srcDir  
        self.srcBranch = srcBranch  
        self.srcUpdt = srcUpdt  
        self.srcUpLog = srcUpLog          
        self.libDir = libDir  
        self.libBranch = libBranch  
        self.libUpdt = libUpdt  
        self.libUpLog = libUpLog
        self.updateSts=updateSts  

repoItemArr = []
def printRepoItemArr(): 
   count = 0
   print("REPO INFO：")
   print("%s  %-50s %-15s %-20s %-10s %-70s" %("No.", "DIR", "BRANCH","GIT_UPTIME","UPDATE","GIT_LOG" ) )
   for tmpRepoItem in repoItemArr:
      print("%-3d  %-50s %-15s %-20s %-10s %-70s" %(count, tmpRepoItem.srcDir,tmpRepoItem.srcBranch,tmpRepoItem.srcUpdt,tmpRepoItem.updateSts,tmpRepoItem.srcUpLog ) )
      print("     %-50s %-15s %-20s %-10s %-70s" %( tmpRepoItem.libDir,tmpRepoItem.libBranch,tmpRepoItem.libUpdt,tmpRepoItem.updateSts,tmpRepoItem.libUpLog ) )
      count = count + 1
   print("\n")
   return
        
class gitLog:  
    def __init__(self, upDate,comLog):  
        self.upDate = upDate  
        self.comLog = comLog  



 
#Json相关操作函数
def parse_json_file(file_path,repoItemArr):  
    # 打开文件并读取内容  
    with open(file_path, 'r', encoding='utf-8') as json_file:  
        # 使用json.load()解析文件内容  
        data = json.load(json_file)

        for item in data['repositorys']:
            repoItemArr.append(repoItem(item['srcDir'],item['srcBranch'],0,"NONE",item['libDir'],item['libBranch'],0,"NONE","no"))             
    return   

  
# Git相关操作
def get_latest_git_log(repo_path,origbranch,gitLog):  
    # 更改当前工作目录到指定的Git仓库  
    #os.chdir(repo_path)  
    branch="origin/"+origbranch
    # 执行git log命令获取最新的日志的时间
    # 使用--pretty=format选项来格式化输出  
    command = ['git','log', branch,'-1', '--pretty=format:%at"']  
    result = subprocess.run(command, stdout=subprocess.PIPE, text=True, cwd=repo_path)
    # 检查命令是否成功执行  
    if result.returncode == 0:          
        log_entry = result.stdout.strip('"')
        gitLog.upDate = log_entry
    else:  
        print("Error executing git log command for %s %s" %(repo_path,branch)) 


    # 执行git log命令获取最新的日志的内容
    # 使用--pretty=format选项来格式化输出  
    command = ['git','log',branch, '-1', '--pretty=format:%s"']  
    result = subprocess.run(command, stdout=subprocess.PIPE, text=True, cwd=repo_path)
    # 检查命令是否成功执行  
    if result.returncode == 0:          
        log_entry = result.stdout.strip('"')
        gitLog.comLog = log_entry
    else:
        print("Error executing git log command") 
    return
    
def git_add(repo_path):  
    command = ['git', 'add', './']  
    result = subprocess.run(command, stdout=subprocess.PIPE, text=True, cwd=repo_path)  

    # 检查命令是否成功执行  
    if result.returncode != 0:  
        print("Error executing git add")  
        return 1  

    return 0  

def git_commit(repo_path,commitMsg):  
    command = ['git', 'commit', '-m', commitMsg ]  
    result = subprocess.run(command, stdout=subprocess.PIPE, text=True, cwd=repo_path)  

    # 检查命令是否成功执行  
    if result.returncode != 0:  
       print("Error executing git commit")
       return 1

    return 0  

def git_push(repo_path, destbranch):  
    headBranch="HEAD:"+destbranch
    command = ['git', 'push', 'origin', headBranch]  
    result = subprocess.run(command, stdout=subprocess.PIPE, text=True, cwd=repo_path)  

    # 检查命令是否成功执行  
    if result.returncode != 0:  
        print("Error executing git push")
        return 1  
    return 0


def parse_and_assign_defaults():
    # 定义默认值
    defConfig = {
        'configFile': 'default_config.txt',
        'forceUpdate': 'no',
        'commit': '0000'
    }
    
    # 初始化用户配置字典
    userConfig = defConfig.copy()
    
    # 解析命令行参数
    for arg in sys.argv[1:]:
        if '=' in arg:
            key, value = arg.split('=', 1)
            if key in userConfig:
                userConfig[key] = value
    
    # 返回解析后的用户配置
    return userConfig


def autoUpdateByTime():    
    tmpGitLog=gitLog(0,"none")
    #获取Interface 信息
    interfaceItem.dir = current_work_path + "interface"
    interfaceItem.branch   = "dev"
    get_latest_git_log(interfaceItem.dir,interfaceItem.branch,tmpGitLog)
    interfaceItem.udt = tmpGitLog.upDate
    interfaceItem.upLog = tmpGitLog.comLog
    
    #printInterfaceItem()
    
    #通过Git获取各个目录最新的更新时间和日期
    
    for tmpRepoItem in repoItemArr:  
      get_latest_git_log(tmpRepoItem.srcDir,tmpRepoItem.srcBranch,tmpGitLog)   
      tmpRepoItem.srcUpdt = tmpGitLog.upDate
      tmpRepoItem.srcUpLog = tmpGitLog.comLog
      get_latest_git_log(tmpRepoItem.libDir,tmpRepoItem.libBranch,tmpGitLog)  
      tmpRepoItem.libUpdt = tmpGitLog.upDate
      tmpRepoItem.libUpLog = tmpGitLog.comLog
    
    
    
    
    print("\n$$$$$$$$$$$$$$$ EXECUTE GIT $$$$$$$$$$$$$$$$$$$")
    #比较更新时间，确认是否需要提交
    for tmpRepoItem in repoItemArr:
     tmpRepoItem.updateSts="no"
     if interfaceItem.udt > tmpRepoItem.libUpdt:
       #使用interface的commit log 提交
       print("\nstart Update(IF):",tmpRepoItem.libDir)       
       ret = git_add(tmpRepoItem.libDir)
       if  ret == 0:  
         ret = git_commit(tmpRepoItem.libDir,interfaceItem.upLog)
         if ret == 0:
            ret = git_push(tmpRepoItem.libDir,tmpRepoItem.libBranch)
            if ret == 0:
               tmpRepoItem.updateSts="yes"
     else:
         if tmpRepoItem.srcUpdt > tmpRepoItem.libUpdt:
           #需要提交bin
           print("\nstart Update(SRC):",tmpRepoItem.libDir)           
           ret = git_add(tmpRepoItem.libDir)
           if  ret == 0:  
               ret = git_commit(tmpRepoItem.libDir,tmpRepoItem.srcUpLog)
               if ret == 0:
                   ret = git_push(tmpRepoItem.libDir,tmpRepoItem.libBranch)
                   if ret == 0:
                      tmpRepoItem.updateSts="yes"
         else:
           #不需要提交bin
           print("\nNotUpdate:",tmpRepoItem.libDir)
    printRepoItemArr()

     
    
#============main======
      
#获取工作目录
import os
current_path_getcwd = os.getcwd()
current_work_path = current_path_getcwd.split("tools")[0]    
print("workpth: ", current_work_path) 

if __name__ == "__main__":
    userConfig = parse_and_assign_defaults()
    user_config_file = userConfig['configFile']
    force_update_flag = userConfig['forceUpdate']
    commit_identifier = userConfig['commit']

    print(f"User Config File: {user_config_file}")
    print(f"Force Update Flag: {force_update_flag}")
    print(f"Commit Identifier: {commit_identifier}")

# 读取配置文件  
json_file_path = user_config_file 
#处理配置文件，获取要处理的目录列表  
parse_json_file(json_file_path,repoItemArr)  

printRepoItemArr()

#相对目录转绝对路径&branch 
for tmpRepoItem in repoItemArr:
    tmpRepoItem.srcDir =current_work_path+tmpRepoItem.srcDir
    tmpRepoItem.libDir =current_work_path+tmpRepoItem.libDir 

if force_update_flag == "yes":
    for tmpRepoItem in repoItemArr:
       tmpRepoItem.updateSts="no"
       ret = git_add(tmpRepoItem.libDir)
       if  ret == 0:  
         ret = git_commit(tmpRepoItem.libDir,commit_identifier)
         if ret == 0:
            ret = git_push(tmpRepoItem.libDir,tmpRepoItem.libBranch) 
            if ret == 0:
               tmpRepoItem.updateSts="yes"                 
    printRepoItemArr()
else:
    autoUpdateByTime()



    
      

