import pandas as pd
import os
from openpyxl import load_workbook
from openpyxl import Workbook
import sys
import io

# 设置标准输出为 UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

output_file = 'release_notes.xlsx'
# writer = pd.ExcelWriter(output_file, engine='openpyxl')

def copy_sheet(source_wb, source_sheet_name, target_wb, target_sheet_name):
    source_sheet = source_wb[source_sheet_name]
    target_sheet = target_wb.create_sheet(target_sheet_name)
    
    for row in source_sheet.iter_rows():
        for cell in row:
            target_cell = target_sheet[cell.coordinate]
            target_cell.value = cell.value
            if cell.has_style:
                target_cell.font = cell.font.copy()
                target_cell.border = cell.border.copy()
                target_cell.fill = cell.fill.copy()
                target_cell.number_format = cell.number_format
                target_cell.alignment = cell.alignment.copy()

def merge_excel_files(file_paths):
    index=1
    for file_path in file_paths:
        # 加载源文件
        wb = load_workbook(file_path)
        
        # 复制工作表
        sheet_name=wb.sheetnames[0]+str(index)
        copy_sheet(wb, wb.sheetnames[0], wb_output, sheet_name)
        
        index = index+1

def find_xlsx_files(directory):
    print("Finding excel files ...")
    xlsx_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            # if file.endswith(".xlsx"):
            if file == "changelist.xlsx":
                xlsx_files.append(os.path.join(root, file))
    return xlsx_files

if __name__ == "__main__":
    # 合并当前目录下所有.xlsx文件（排除输出文件）
    # input_files = [f for f in os.listdir() if f.endswith('.xlsx') and f != output_file]
    input_files = find_xlsx_files("/hfr/modules")
    if input_files:
        print("input_files:")
        for item in input_files:
            print(item)
        # 创建新工作簿
        wb_output = load_workbook(input_files[0])  # 以其中一个文件为模板
        while len(wb_output.sheetnames) > 0:  # 删除所有现有工作表
            del wb_output[wb_output.sheetnames[0]]

        merge_excel_files(input_files)

        # 保存结果
        wb_output.save(output_file)
        print(f"Merged success, output file: {output_file}")
    else:
        print("No excel files found!")


