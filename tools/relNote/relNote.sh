#!/usr/bin/env bash

echo "start docker container"
container="rel_note"

if docker ps -a --format '{{.Names}}' | grep -q "${container}"; then
	info "Removing existing Hpp container: ${container}"
	docker stop "${container}" >/dev/null
	docker rm -v -f "${container}" 2>/dev/null
fi

docker container run -idt -u $(id -u) --name "${container}" --privileged --cap-add SYS_ADMIN \
    --cap-add NET_ADMIN --device /dev/fuse \
    --security-opt apparmor:unconfined --security-opt seccomp=unconfined \
    -e LANG=C.UTF-8 -e LC_ALL=C.UTF-8 \
    -v $WORKSPACE/tmp/hfr:/hfr \
    -w /hfr \
    --hostname in-ubuntu-1804-docker \
    ubuntu-1804-rel-notes:v2 bash

$WORKSPACE/script/docker/dev_into_rel_note.sh python3 /hfr/tools/relNote/generateReleaseNotes.py
