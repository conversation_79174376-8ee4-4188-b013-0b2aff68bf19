import subprocess  
import json  
import argparse  
from pathlib import Path  
import os
import sys
import xml.etree.ElementTree as ET

# 定义变量存储解析信息
class interfaceInfo:
    def __init__(self,dir,branch,udt,upLog):
        self.dir = dir  
        self.branch = branch  
        self.udt = udt  
        self.upLog = upLog       

interfaceItem=interfaceInfo("undef","unkonw",0,"unkonw")
def printInterfaceItem():
    print("Interface dir:%s, branch %s, uptime %s, log %s" %(interfaceItem.dir,interfaceItem.branch,interfaceItem.udt,interfaceItem.upLog) )


class manifestItem:
    def __init__(self,name,path,revision,srcDir,srcCommitID,srcUpLog,pushFlag):
        self.name = name  
        self.path = path  
        self.revision = revision  
        self.srcDir = srcDir  
        self.srcCommitID = srcCommitID  
        self.srcUpLog = srcUpLog 
        self.pushFlag = 0 
        self.addTag = srcCommitID
        self.isPriLib = 0

manifestArr=[]
fileList=[]
def printManifestArr():
    count = 0
    print("\033[31m Manifest Project List: \033[39m")
    print("\033[34m %s  %-50s %-50s %-20s" %("No.", "Name", "Path","Revision" ) )
    for tmpManiItem in manifestArr:  
       print("%-3d  %-50s %-50s %-20s " %(count, tmpManiItem.name,tmpManiItem.path,tmpManiItem.revision) )
       count = count + 1
    print("\033[39m") 
    return

def printGitItemArr(): 
   count = 0
   print("\033[31m Manifest Git INFO：\033[39m")
   print("\033[34m %s  %-50s %-15s %-20s %-70s" %("No.", "DIR", "BRANCH","GIT_COMMITID","GIT_LOG" ) )
   for tmpRepoItem in manifestArr:
      print("%-3d  %-50s %-15s %-20s %-2d" %(count, tmpRepoItem.srcDir,tmpRepoItem.revision,tmpRepoItem.srcCommitID,tmpRepoItem.pushFlag ) )
      count = count + 1
   print("\n")
   print("\033[39m") 
   return
        
class gitLog:  
    def __init__(self, comitID,comLog):  
        self.comitID = comitID  
        self.comLog = comLog 
 
  
# Git相关操作
def get_latest_git_comitID(repo_path,origbranch,gitLog):  
    branch="origin/"+origbranch

    # 执行git 命令获取指定分支的commit ID  
    command = ['git','rev-parse',branch]  
    result = subprocess.run(command, stdout=subprocess.PIPE, text=True, cwd=repo_path)
    # 检查命令是否成功执行  
    if result.returncode == 0:          
        log_entry = result.stdout.strip('"')
        gitLog.comitID = log_entry.rstrip('\n')
    else:
        print("Error executing rev-parse command failed") 

   # # 执行git log命令获取最新的日志的内容
   # # 使用--pretty=format选项来格式化输出  
   # command = ['git','log',branch, '-1', '--pretty=format:%s"']  
   # result = subprocess.run(command, stdout=subprocess.PIPE, text=True, cwd=repo_path)
   # # 检查命令是否成功执行  
   # if result.returncode == 0:          
   #     log_entry = result.stdout.strip('"')
   #     gitLog.comLog = log_entry
   # else:
   #     print("Error executing git log command")
   #      
    return
    
def git_add_tag(repo_path,tagID,comitID):  
    command = ['git', 'tag', '-a', tagID, comitID ,"-m", tagID]  
    print(command)
    result = subprocess.run(command, stdout=subprocess.PIPE, text=True, cwd=repo_path)  
  
    # 检查命令是否成功执行  
    if result.returncode != 0:  
        print("Error executing git add")  
        return 1  
    return 0  


def git_tag_exists(repo_path,tagID):
    try:
        # 保存当前工作目录
        original_cwd = os.getcwd()
        
        # 切换到目标目录
        os.chdir(repo_path)
        
        # 执行 git tag 命令以获取所有标签，并检查指定标签是否存在
        result = subprocess.run(['git', 'tag'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        tags = result.stdout.splitlines()       
      
        # 检查标签是否在标签列表中
        return tagID in tags
    except Exception as e:
        print(f"An error occurred: {e}")
        return False
    finally:
        # 切换回原始工作目录
        os.chdir(original_cwd)

def git_get_latest_tag_for_commit(repo_path, comitID):
    try:
        # 保存当前工作目录
        original_cwd = os.getcwd()
        
        # 切换到指定的 Git 仓库目录
        os.chdir(repo_path)

#        # 获取所有标签及其对应的提交哈希
#        tags_output = subprocess.check_output(['git', 'show-ref', '--tags'], text=True).strip().split('\n')
#        #print(f"tags_output: {tags_output}")
#        tags_on_commit = None
#        
#        for line in tags_output:
#            # 解析输出，格式为：<commit_hash> refs/tags/<tag_name>
#            parts = line.split()
#            if len(parts) < 2:
#                continue
#            
#            tag_commit_hash = parts[0]
#            tag_name = parts[1].split('/')[-1]  # 提取标签名
#            
#            # 检查标签是否指向指定的提交 ID
#            if tag_commit_hash == comitID:
#                #tags_on_commit=tag_name
#                return tag_name
#        
#        return tags_on_commit
   
        # 获取包含指定 commit ID 的所有 tags
        command = ['git', 'tag', '--contains', comitID]
        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=True)
        if len(result.stdout) == 0:
            print(f"No tags0 found for commit {comitID}.")
            return None

        tags = result.stdout.strip().split('\n')
        
        if not tags:
            print(f"No tags1 found for commit {comitID}.")
            return None

        #print(f"========={repo_path}==={tags}======")
        #tags.sort(key=lambda s: tuple(map(int, (s.lstrip('v').split('.')))), reverse=True)
        
        # 返回最新的 tag
        latest_tag = tags[0]
        #print(f"The latest tag for commit {comitID} is {latest_tag}.")
        return latest_tag
    
    except subprocess.CalledProcessError as e:
        print(f"An error occurred while running git_get_latest_tag_for_commit command: {e}")
        return None
    
    finally:
        # 切换回原始工作目录
        os.chdir(original_cwd)



def git_commit(repo_path,commitMsg):  
    command = ['git', 'commit', '-m', commitMsg ]  
    result = subprocess.run(command, stdout=subprocess.PIPE, text=True, cwd=repo_path)  

    # 检查命令是否成功执行  
    if result.returncode != 0:  
       print("Error executing git commit")
       return 1

    return 0  

def git_push(repo_path, destbranch,tagID):  
    headBranch="HEAD:"+destbranch
    command = ['git', 'push', 'origin', headBranch,tagID]  
    result = subprocess.run(command, stdout=subprocess.PIPE, text=True, cwd=repo_path)  

    # 检查命令是否成功执行  
    if result.returncode != 0:  
        print("Error executing git push")
        return 1  
    return 0

#XML处理函数
# 解析XML文件
def xmlParse(rootfile,manifestArr,fileList):  
    tree = ET.parse(rootfile)  
    
    # 获取根元素  
    root = tree.getroot()  
    #tmpFileList=[]
    # 遍历XML树中的所有元素  
    for element in root.iter():   
        if element.tag.lower() == "project" :
          #print(element.get('name'))
          #print(element.get('path'))
          #print(element.get('revision'))
          manifestArr.append(manifestItem(element.get('name'),element.get('path'),element.get('revision'),"","","",0) ) 
        elif element.tag.lower() == "include" : 
          #print("include name:",element.get('name'))
          fileList.append(element.get('name'))



#============main======

def is_valid_file_path(path_str):  
    path = Path(path_str)  
    return path.exists() and path.is_file() 

#获取文件名和new tag
if __name__ == "__main__":  
    parser = argparse.ArgumentParser(description="Process some integers.")  
    parser.add_argument("fileName", type=str, help="Manifest fileName or fullpath")  
    parser.add_argument("tag", type=str, help="a new tag for this relase")  
    args = parser.parse_args()  

newTag=args.tag
fullFile=""

#获取工作目录
current_path_getcwd = os.getcwd()
current_work_path = current_path_getcwd.split("tools")[0]

if is_valid_file_path(args.fileName):
    fullFile=args.fileName
else:
    fullFile=current_work_path+"../.repo/manifests/platform_project/"+args.fileName
    

if is_valid_file_path(fullFile):
    print(f"Manifest file:\n {fullFile}")
else:
    print(f"Err,Not found Manifest file:\n {fullFile}")
    sys.exit(1) #exit


#解析xml的所有project
xmlParse(fullFile,manifestArr,fileList)
uniqueFileList=list(set(fileList))
fileList.clear()
for fileItem in uniqueFileList:   
   childFile = fileItem.split("platform_project")[1]
   childFullFile=current_work_path+"../.repo/manifests/platform_project/"+childFile
   xmlParse(childFullFile,manifestArr,fileList)

printManifestArr()
#获取文件夹路径
for tmpManiItem in manifestArr:
    tmpManiItem.srcDir = current_work_path+"../"+tmpManiItem.path
    if "hfr/lib" in tmpManiItem.path:
        tmpManiItem.isPriLib=1
    else:
        tmpManiItem.isPriLib=0        
#printGitItemArr()

#获取commitID
tmpGitLog=gitLog("unknow","none")
for tmpManiItem in manifestArr:  
    get_latest_git_comitID(tmpManiItem.srcDir,tmpManiItem.revision,tmpGitLog)
    tmpManiItem.srcCommitID=tmpGitLog.comitID
    tmpManiItem.srcUpLog=tmpGitLog.comLog

#printGitItemArr()
#add Tag && push tag
for tmpManiItem in manifestArr: 
    #check tag if exist
    new_add_tag = newTag

    #for lib,new lib_newtag to avoid conflicts with private library
    if tmpManiItem.isPriLib == 1:
          new_add_tag= "lib_" + newTag


    #print(f"==============will add tag {new_add_tag}")      

    if  git_tag_exists(tmpManiItem.srcDir,new_add_tag): 
        print(f"{tmpManiItem.srcDir} exist the {new_add_tag}")
        tmpManiItem.addTag = new_add_tag
        tmpManiItem.pushFlag=1
    else:
        #not found the newTag,need to check if had old tag in this commitID  
        cur_latest_tag = git_get_latest_tag_for_commit(tmpManiItem.srcDir, tmpManiItem.srcCommitID)        
        if cur_latest_tag is not None:
             print(f"{tmpManiItem.srcDir} not update, use tag:{cur_latest_tag}")
             tmpManiItem.addTag = cur_latest_tag
             tmpManiItem.pushFlag=1
        else:
           #add tag
           ret = git_add_tag(tmpManiItem.srcDir,new_add_tag,tmpManiItem.srcCommitID)
           if  ret == 0:  
               #push tag
               ret = git_push(tmpManiItem.srcDir, tmpManiItem.revision,new_add_tag)
               if ret == 0 :
                   tmpManiItem.pushFlag=1
                   tmpManiItem.addTag = new_add_tag

printGitItemArr()

#创建release文件夹 
folder_path = newTag
# 使用os.mkdir创建文件夹，如果创建失败，则folder_path被设置为当前目录
if not os.path.exists(folder_path):  
    try:  
        os.mkdir(folder_path)          
    except OSError as e:  
        print(f"create dir failed: {e.strerror},will create file in current dir") 
        folder_path="." 

#write new manifest file base on commitID
newfileFull=folder_path+"/"+newTag+"_commitID.xml"        
# 使用 'w' 模式打开文件，如果文件已存在则会被覆盖  
# 如果想在文件末尾追加内容，可以使用 'a' 模式  
with open(newfileFull, 'w', encoding='utf-8') as file:  
    file.write('<?xml version="1.0" encoding="UTF-8" ?>\n')  
    file.write('<manifest>\n')
    file.write('  <remote name="origin" fetch="ssh://git@10.0.33.175"/>\n')
    file.write('  <default remote="origin" revision="release" sync-j="4" />\n\n')
    file.write(f'  <!--base commitID for tag:{newTag}-->\n')   
    for tmpManiItem in manifestArr:  
       file.write(f'  <project name="{tmpManiItem.name}" path="{tmpManiItem.path}" revision="{tmpManiItem.srcCommitID}" />\n') 
    file.write('</manifest>\n')  


#write new manifest file base on newtag
newfileFull=folder_path+"/"+newTag+"_tag.xml"        
# 使用 'w' 模式打开文件，如果文件已存在则会被覆盖  
# 如果想在文件末尾追加内容，可以使用 'a' 模式  
with open(newfileFull, 'w', encoding='utf-8') as file:  
    file.write('<?xml version="1.0" encoding="UTF-8" ?>\n')  
    file.write('<manifest>\n')
    file.write('  <remote name="origin" fetch="ssh://git@10.0.33.175"/>\n')
    file.write('  <default remote="origin" revision="release" sync-j="4" />\n\n')
    file.write(f'  <!--base tagID :{newTag}. NOTE: Only include repositories with successfully created tag-->\n')   
    for tmpManiItem in manifestArr:  
       if tmpManiItem.pushFlag == 1:
           file.write(f'  <project name="{tmpManiItem.name}" path="{tmpManiItem.path}" revision="refs/tags/{tmpManiItem.addTag}" />\n') 
    file.write('</manifest>\n')     

    
