[global]
#
# Default Data Set
#
twoStepFlag		1
gmCapable		1
priority1		248
priority2		248
domainNumber		0
#utc_offset		37
clockClass		248
clockAccuracy		0xFE
offsetScaledLogVariance	0xFFFF
free_running		0
freq_est_interval	1
#
# Port Data Set
#
logAnnounceInterval	1
logSyncInterval		-3
logMinPdelayReqInterval	0
announceReceiptTimeout	3
syncReceiptTimeout	3
delayAsymmetry		0
fault_reset_interval	4
neighborPropDelayThresh	800
min_neighbor_prop_delay	-20000000
#
# Run time options
#
assume_two_step		1
logging_level		6
path_trace_enabled	1
follow_up_info		1
hybrid_e2e		0
net_sync_monitor	0
tx_timestamp_timeout	1
use_syslog		1
verbose			0
summary_interval	0
kernel_leap		1
check_fup_sync		0
#
# Servo options
#
pi_proportional_const	0.0
pi_integral_const	0.0
pi_proportional_scale	0.0
pi_proportional_exponent	-0.3
pi_proportional_norm_max	0.7
pi_integral_scale	0.0
pi_integral_exponent	0.4
pi_integral_norm_max	0.3
step_threshold		0.0
first_step_threshold	0.00002
max_frequency		900000000
clock_servo		pi
sanity_freq_limit	200000000
ntpshm_segment		0
#
# Transport options
#
transportSpecific	0x1
ptp_dst_mac		01:80:C2:00:00:0E
p2p_dst_mac		01:80:C2:00:00:0E
uds_address		/var/run/ptp4l
#
# Default interface options
#
network_transport	L2
delay_mechanism		P2P
time_stamping		hardware
tsproc_mode		filter
delay_filter		moving_median
delay_filter_length	10
egressLatency		0
ingressLatency		0
boundary_clock_jbod	0
