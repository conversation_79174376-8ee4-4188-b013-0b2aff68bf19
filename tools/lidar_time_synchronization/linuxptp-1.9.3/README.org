
* Introduction

  This software is an implementation of the Precision Time Protocol
  (PTP) according to IEEE standard 1588 for Linux. The dual design
  goals are to provide a robust implementation of the standard and to
  use the most relevant and modern Application Programming Interfaces
  (API) offered by the Linux kernel. Supporting legacy APIs and other
  platforms is not a goal.

* License

  The software is copyrighted by the authors and is licensed under the
  GNU General Public License. See the file, COPYING, for details of
  the license terms.

* Features

  - Supports hardware and software time stamping via the Linux
    SO_TIMESTAMPING socket option.

  - Supports the Linux PTP Hardware Clock (PHC) subsystem by using the
    clock_gettime family of calls, including the new clock_adjtimex
    system call.

  - Implements Boundary Clock (BC) and Ordinary Clock (OC).

  - Transport over UDP/IPv4, UDP/IPv6, and raw Ethernet (Layer 2).

  - Supports IEEE 802.1AS-2011 in the role of end station.

  - Modular design allowing painless addition of new transports and
    clock servos.

* Getting the Code

  You can download the latest released version at Source Forge.

  http://sourceforge.net/projects/linuxptp/files/latest/download

  The source code is managed using the git version control system. To
  get your own copy of the project sources, use the following command.

#+BEGIN_EXAMPLE
  git clone git://git.code.sf.net/p/linuxptp/code linuxptp
#+END_EXAMPLE

  If the git protocol is blocked by your local area network, then you
  can use the alternative HTTP protocol instead.

#+BEGIN_EXAMPLE
  git clone http://git.code.sf.net/p/linuxptp/code linuxptp
#+END_EXAMPLE

* System Requirements

  In order to run this software, you need Linux kernel
  version 3.0 or newer, and the kernel header files must available at
  compile time.

  In addition, you will also need to have either:

  1. A supported Ethernet MAC device.

  2. A supported PHY device paired with a MAC that allows time
     stamping in the PHY (indicated by PHY=Y in the table below).

** Linux Kernel Support

   In order to support PTP, the operating system needs to provide two
   services: network packet time stamping and clock control. In 2009,
   Patrick Ohly added a new socket option called SO_TIMESTAMPING for
   packet time stamping, especially for PTP. This work appeared in
   Linux version 2.6.30.

   In July of 2011, the PTP Hardware Clock (PHC) subsystem was merged
   into Linux version 3.0. The PHC code provides a driver framework and
   the user space API for clock control.

** Ethtool Support

   Starting with version 3.5 of the Linux kernel, you can query the
   time stamping capabilities of a network interface using the
   ETHTOOL_GET_TS_INFO ioctl. Using ethtool version 3.4 or later, you
   can check your system's time stamping support as shown in the
   following example.

#+BEGIN_EXAMPLE
  ethtool -T eth0
#+END_EXAMPLE

   If the ethtool ioctl is available, then the ptp4l program will use
   it in order to discover the proper PHC device.

** Driver Support Matrix

   The following two tables list the drivers that support the PHC
   subsystem and the Linux kernel version when they first appeared.
   These drivers will create a PHC device for controlling the hardware
   clock.

*** Hardware Timestamping - PHY

    |---------+-------------------------------+---------|
    | Driver  | Hardware                      | Version |
    |---------+-------------------------------+---------|
    | dp83640 | National Semiconductor PHYTER |     3.0 |
    |---------+-------------------------------+---------|

*** Hardware Timestamping - MAC

    |------------+--------------------------+---------|
    | Driver     | Hardware                 | Version |
    |------------+--------------------------+---------|
    | amd-xgbe   | AMD 10GbE Ethernet Soc   |    3.17 |
    | bfin_mac   | Analog Blackfin          |     3.8 |
    | bnx2x      | Broadcom NetXtremeII 10G |    3.18 |
    | cpts       | Texas Instruments am335x |     3.8 |
    | e1000e     | Intel 82574, 82583       |     3.9 |
    | fm10k      | Intel FM10000            |    3.18 |
    | fec        | Freescale i.mx6          |     3.8 |
    | gianfar    | Freescale eTSEC PowerPC  |     3.0 |
    | i40e       | Intel XL710 Family       |    3.14 |
    | igb        | Intel 82576, 82580       |     3.5 |
    | ixgbe      | Intel 82599              |     3.5 |
    | mlx4       | Mellanox 40G PCI         |    3.14 |
    | ptp_ixp46x | Intel IXP465             |     3.0 |
    | ptp_phc    | Lapis EG20T PCH          |     3.5 |
    | sfc        | Solarflare SFC9000       |     3.7 |
    | stmmac     | STM Synopsys IP Core     |    3.10 |
    | tg3        | Broadcom Tigon3 PCI      |     3.8 |
    | tilegx     | Tilera GBE/XGBE          |    3.12 |
    |------------+--------------------------+---------|

*** Software Timestamping

    The table below shows the Linux drivers that support software time
    stamping. In addition, the 'PHY' column indicates whether the
    Ethernet MAC driver can support a PTP Hardware Clock in an
    external PHY. The letter 'Y' in this column means that if you
    design a mother board that combines such a MAC with a PTP capable
    PHY, then it will work with the Linux PHC subsystem.

    |--------------+--------------------------+---------+-----|
    | Driver       | Hardware                 | Version | PHY |
    |--------------+--------------------------+---------+-----|
    | 3c59x        | 3Com EtherLink PCI       |    3.14 | N   |
    | altera_tse   | Altera Triple-Speed MAC  |    3.15 | Y   |
    | bna          | Brocade 1010/1020 10Gb   |    3.14 | N   |
    | bnx2x        | Broadcom Everest         |     3.5 | N   |
    | davinci_emac | TI DaVinci, Sitara       |     3.1 | Y   |
    | dnet         | Dave Ethernet MAC        |     3.1 | Y   |
    | e100         | Intel PRO/100            |     3.5 | N   |
    | e1000        | Intel PRO/1000 PCI/PCI-X |     3.5 | N   |
    | e1000e       | Intel PRO/1000 PCIe      |     3.5 | N   |
    | emaclite     | Xilinx Ethernet Lite     |     3.1 | Y   |
    | ethoc        | OpenCores 10/100 MAC     |     3.1 | Y   |
    | fec          | Freescale Coldfire       |     3.1 | Y   |
    | fec_mpc52xx  | Freescale MPC5200        |     3.1 | Y   |
    | forcedeth    | NVIDIA nForce            |     3.5 | N   |
    | fs_enet      | Freescale MPC512x        |     3.1 | Y   |
    | genet        | Broadcom GENET           |    3.15 | Y   |
    | ixp4xx_eth   | Intel IXP4xx             |     3.0 | Y   |
    | lib8390      | Asix AX88796             |     3.1 | Y   |
    | lib8390      | Various 8390 based HW    |     3.1 | N   |
    | ll_temac     | Xilinx LL TEMAC          |     3.1 | Y   |
    | macb         | Atmel AT32, AT91         |     3.1 | Y   |
    | mv643xx_eth  | Marvell Discovery, Orion |     3.1 | Y   |
    | pxa168_eth   | Marvell pxa168           |     3.1 | Y   |
    | r6040        | RDC Ethernet MAC         |     3.1 | Y   |
    | r8169        | Realtek 8169/8168/8101   |     3.4 | N   |
    | samsun-sxgbe | Samsung SXGBE 10G        |    3.15 | Y   |
    | smsc911x     | SMSC LAN911x, LAN921x    |     3.1 | Y   |
    | smsc9420     | SMSC LAN9420 PCI         |     3.1 | Y   |
    | stmmac       | STM Synopsys IP Core     |     3.1 | Y   |
    | tg3          | Broadcom Tigon3 PCI      |     3.1 | Y   |
    | ucc_geth     | Freescale QE Gigabit     |     3.1 | Y   |
    | usbnet       | USB network devices      |     3.2 | Y/N |
    | xgene-enet   | APM X-Gene SoC           |    3.17 | Y   |
    |--------------+--------------------------+---------+-----|

* Installation

** Linux kernel

   There are many ways of getting a precompiled Linux kernel or
   compiling your own, so this section is only meant as an example. It
   is important to have the kernel headers available when compiling
   the Linux PTP stack.

#+BEGIN_EXAMPLE
   export ARCH=x86
   export CROSS_COMPILE=
   export KBUILD_OUTPUT=/home/<USER>/kernel/ptp_debian
   mkdir -p $KBUILD_OUTPUT
   cp /boot/config-2.6.38-bpo.2-686 $KBUILD_OUTPUT/.config
   make oldnoconfig
   make menuconfig
   time make -j4
   make headers_install
#+END_EXAMPLE

   Here is a table of kernel configuration options needed for PTP
   support. In addtion to these, you should enable the specific
   Ethernet MAC and PHY drivers for your hardware.

   |---------------------------------+-----------------------------|
   | Option                          | Description                 |
   |---------------------------------+-----------------------------|
   | CONFIG_PPS                      | Required                    |
   | CONFIG_NETWORK_PHY_TIMESTAMPING | Timestamping in PHY devices |
   | PTP_1588_CLOCK                  | PTP clock support           |
   |---------------------------------+-----------------------------|

** PTP stack

   1. Just type 'make'

   2. If you compiled your own kernel (and the headers are not
      installed into the system path), then you should set the
      KBUILD_OUTPUT environment variable as in the example, above.

   3. In order to install the programs and man pages into /usr/local,
      run the 'make install' target. You can change the installation
      directories by setttings the variables prefix, sbindir, mandir,
      and man8dir on the make command line.

* Getting Involved

  The software development is hosted at Source Forge.

  https://sourceforge.net/projects/linuxptp/

** Reporting Bugs

   Please report any bugs or other issues with the software to the
   linuxptp-users mailing list.

   https://lists.sourceforge.net/lists/listinfo/linuxptp-users

** Development

   If you would like to get involved in improving the software, please
   join the linuxptp-devel mailing list.

   https://lists.sourceforge.net/lists/listinfo/linuxptp-devel

*** Submitting Patches

   1. Before submitting patches, please make sure that you are starting
      your work on the *current HEAD* of the git repository.

   2. Please checkout the ~CODING_STYLE.org~ file for guidelines on how to
      properly format your code.

   3. Describe your changes. Each patch will be reviewed, and the reviewers
      need to understand why you did what you did.

   4. *Sign-Off* each commit, so the changes can be properly attributed to
      you and you explicitely give your agreement for distribution under
      linuxptp's license. Signing-off is as simple as:

      #+BEGIN_EXAMPLE
      git commit -s
      #+END_EXAMPLE

      or by adding the following line (replace your real name and email)
      to your patch:

      #+BEGIN_EXAMPLE
      Signed-off-by: Random J Developer <<EMAIL>>
      #+END_EXAMPLE

   5. Finally, send your patches via email to the linuxptp-devel mailing
      list, where they will be reviewed, and eventually be included in the
      official code base.

      #+BEGIN_EXAMPLE
      git send-email --to <EMAIL> origin/master
      #+END_EXAMPLE

* Thanks

  Thanks to AudioScience Inc for sponsoring the 8021.AS support.

  - http://www.audioscience.com

  Thanks to Exablaze for donating an ExaNIC X10

  - http://exablaze.com/exanic-x10

  Thanks to Intel Corporation for donating four NICs, the 82574,
  82580, 82599, and the i210.

  - http://www.intel.com
  - http://e1000.sourceforge.net

  Thanks to Meinberg Funkuhren for donating a LANTIME M1000.

  - https://www.meinbergglobal.com

  For testing I use an OTMC 100 grandmaster clock donated by OMICRON Lab.

  - http://www.omicron-lab.com/ptp
