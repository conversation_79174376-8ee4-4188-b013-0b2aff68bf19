cmake_minimum_required(VERSION 3.10)
project(ptp4l)

add_compile_definitions (HAVE_ONESTEP_SYNC)

file(GLOB CPP_FILES *.h)
add_executable(${PROJECT_NAME}  bmc.c clock.c clockadj.c clockcheck.c config.c fault.c filter.c fsm.c hash.c linreg.c mave.c mmedian.c msg.c ntpshm.c nullf.c phc.c pi.c port.c print.c ptp4l.c raw.c rtnl.c servo.c sk.c stats.c tlv.c transport.c tsproc.c udp.c udp6.c uds.c util.c version.c  ${CPP_FILES})
#Add compilation macro, must be called after defining target executable file or library

target_link_libraries(ptp4l PUBLIC
                      -lm -lrt -lpthread stdc++fs)

install(TARGETS ptp4l DESTINATION  ${HPP_MODULE_INSTALL_PATH}/ptp4l/bin)
install(DIRECTORY resource DESTINATION  ${HPP_MODULE_INSTALL_PATH}/ptp4l)