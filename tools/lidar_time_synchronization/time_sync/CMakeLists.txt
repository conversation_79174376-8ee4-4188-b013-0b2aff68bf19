cmake_minimum_required(VERSION 3.10)

if ("${HFR_ARCH}" STREQUAL "arm64_a1000b")

project(time_sync)

if( DEFINED COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR )
  # The variable has already been defined
else()
  include("/hfr/script/cmake/hfrLibraryGroup.cmake")
endif()

link_directories(${COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR}) 
file(GLOB CPP_FILES src/*.cpp src/*.hpp src/*.h)
add_executable(${PROJECT_NAME}  main.cpp ${CPP_FILES})
#Add compilation macro, must be called after defining target executable file or library
common_defines()

include_directories(${COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR} ${CMAKE_INTERFACE_INSTALL_PATH})
target_link_libraries(time_sync PUBLIC
                    ${COMPONENTS_AND_DATA_CENTER_LIBRARIES}
                      )

install(TARGETS time_sync DESTINATION  ${HPP_MODULE_INSTALL_PATH}/time_sync/bin)
install(DIRECTORY resource DESTINATION  ${HPP_MODULE_INSTALL_PATH}/time_sync)
install(PROGRAMS run.sh DESTINATION  ${HPP_MODULE_INSTALL_PATH}/time_sync)

endif()