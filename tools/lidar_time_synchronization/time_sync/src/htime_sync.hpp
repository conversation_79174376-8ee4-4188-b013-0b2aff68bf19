#pragma once

#include <string.h>

class HTimeSyncInterface
{
public :
  HTimeSyncInterface() {;};
  virtual ~HTimeSyncInterface() { Stop(); };

  virtual int Start() = 0;
  virtual int Sync() { return 0; };
  virtual void Stop()  {};
};

class HLvFpgaSync : public HTimeSyncInterface
{
public :
  HLvFpgaSync() {;};

  int Start() override;
  int Sync() override;
  void Stop() override;

private :
  int time_sync_fd_ = 0;
};
