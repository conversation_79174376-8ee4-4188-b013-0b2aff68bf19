#pragma once

#include "component_base.hpp"
#include "singleton.hpp"

#include "htime_sync.hpp"

const static std::string HPP_FUCTION_LV = "LV";

class TimeSync : public HBL::ComponentBase {
public :
    ~TimeSync() override;

    void on_load() override;
    void on_process() override;

private :
    std::string func_mode = "NULL";
    std::shared_ptr<HTimeSyncInterface> time_sync_hd_;

};

REGISTER_MCOMPONENT(TimeSync)