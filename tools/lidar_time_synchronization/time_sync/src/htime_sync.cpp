#include "htime_sync.hpp"

#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>

#include <iostream>

#include "time.hpp"

int HLvFpgaSync::Start()
{
  if (time_sync_fd_ != 0) {
    Stop();
  }
  time_sync_fd_ = open("/dev/lv_timing", O_RDWR);
  if (time_sync_fd_ <= 1) {
    std::cout << "Error : failed to open /dev/lv_timing, not to sync time to FPGA!!!\n";
    time_sync_fd_ = 0;
  }
  return time_sync_fd_ > 0;
}

int HLvFpgaSync::Sync()
{
  if (time_sync_fd_ <= 0) {
    std::cout << "Error : not start sync,so not to sync time to FPGA!!!\n";
    return -1;
  }
  uint64_t now_time = HBL::now_us();
  return write(time_sync_fd_, (void*)(&now_time), sizeof(now_time)) == sizeof(now_time);
}

void HLvFpgaSync::Stop()
{
  if (time_sync_fd_ != 0) {
    close(time_sync_fd_);
    time_sync_fd_ = 0;
    std::cout << "Info : stop to sync time to FPGA!!!\n";
  }
}