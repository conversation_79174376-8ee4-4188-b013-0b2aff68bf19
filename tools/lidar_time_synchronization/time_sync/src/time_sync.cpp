#include "time_sync.hpp"

#include <iostream>
#include <string>

#include <core/parameter/parameter.h>

TimeSync::~TimeSync()
{
  if (time_sync_hd_) {
    time_sync_hd_->Stop();
    time_sync_hd_.reset();
  }
}

void TimeSync::on_load() {
  func_mode = parameter::Get<std::string>("resource/parameter_config.json","func_mode");
  if (func_mode.compare(HPP_FUCTION_LV) == 0) {
    time_sync_hd_.reset(new HLvFpgaSync());
    if (time_sync_hd_->Start() != 0) {
      std::cout << "Error : fail to start time sync!!!\n";
    }
  } else {
    //not other time sync, so exit;
    exit(0);
  }
}

void TimeSync::on_process() {
  if (time_sync_hd_) {
    time_sync_hd_->Sync();
  }
}