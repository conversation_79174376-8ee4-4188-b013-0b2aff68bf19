#!/bin/bash 

#defalult op
opCmd="exec"

#verMode = "rel" to build release version
#verMode = "dev" to build debug version
verMode="dev"
dir="build"

function build_help()
{
    #echo -e  "\033[41;255m read  \033[0m"
    #echo -e  "\033[1;34m Green  \033[0m"
    #echo -e  "\033[1;32m Blue \033[0m"
    #echo -e  "\033[1;31m red \033[0m"
    echo -e  "\033[1;32m Usage: $0 [cmd] [mode|dir] \033[0m"
    echo -e  "\033[1;32m cmd option: \033[0m"
    echo -e  "\033[1;32m   env         Building a compilation environment. \033[0m"
    echo -e  "\033[1;32m               Note that the build directory will be automatically deleted. \033[0m"
    echo -e  "\033[1;32m   exec        Execute compilation process [Default]   \033[0m"
    echo -e  "\033[1;32m   pack        Packaging output files  \033[0m"
    echo -e  "\033[1;32m  mode option: \033[0m"
    echo -e  "\033[1;32m   dev        Development version [Default] \033[0m"
    echo -e  "\033[1;32m   rel        Release version \033[0m"
    echo -e  "\033[1;32m  pack directory: \033[0m"
    echo -e  "\033[1;32m   build      Tar build install dir [Default] \033[0m"
    echo -e  "\033[1;32m   script     Tar script dir\033[0m"
    echo -e  "\033[1;32m   full       Tar build & script dir\033[0m"
}

for arg in "$@" ; do
    if [ "$arg" = "env" ]; then
      opCmd="env"
      continue           
    fi
    if [ "$arg" = "exec" ]; then
      opCmd="exec"
      continue      
    fi 
    if [ "$arg" = "pack" ]; then
      opCmd="pack"
      continue      
    fi 
    if [ "$arg" = "rel" ]; then
      verMode="rel"
      continue      
    fi    
    if [ "$arg" = "dev" ]; then
      verMode="dev"
      continue      
    fi  
    if [ "$arg" = "full" ]; then
      dir="full"
      continue      
    fi
    if [ "$arg" = "build" ]; then
      dir="build"
      continue      
    fi 
    if [ "$arg" = "script" ]; then
      dir="script"
      continue      
    fi         
    echo -e "Usage" "\033[1;31m[$arg]\033[0m" "is not support, see help. "
    build_help
    exit
done

#include the build function
source script/build_function.sh


case $opCmd in
   env)
     cmd_env $verMode
     ;;
   exec)
     cmd_exec
     ;;
   pack)
     cmd_pack $dir
     ;;
esac



