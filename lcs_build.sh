#!/bin/bash

# Build script for LCS module
# This script builds the LidarCurbSegment module based on the architecture parameter

set -e  # Exit on any error

# Function to display usage information
show_usage() {
    echo "Usage: $0 [ARCHITECTURE] [BUILD_TYPE]"
    echo ""
    echo "Build script for LCS (LidarCurbSegment) module"
    echo ""
    echo "Arguments:"
    echo "  ARCHITECTURE:"
    echo "    (none)      Use x86_64 architecture (Debug mode)"
    echo "    a1000       Use arm64_a1000b architecture (Release mode)"
    echo ""
    echo "  BUILD_TYPE:"
    echo "    (none)      Incremental build (default)"
    echo "    full        Full build with cache cleanup"
    echo ""
    echo "Options:"
    echo "  -h, --help  Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Incremental build for x86_64 architecture"
    echo "  $0 a1000              # Incremental build for arm64_a1000b architecture"
    echo "  $0 full               # Full build for x86_64 architecture (with cleanup)"
    echo "  $0 a1000 full         # Full build for arm64_a1000b architecture (with cleanup)"
    echo ""
}

# Parse command line arguments
ARCH="x86_64"  # Default architecture
FULL_BUILD=false  # Default to incremental build

# Parse arguments
if [ $# -eq 0 ]; then
    # No arguments, use defaults
    ARCH="x86_64"
    FULL_BUILD=false
elif [ $# -eq 1 ]; then
    case "$1" in
        a1000)
            ARCH="arm64_a1000b"
            FULL_BUILD=false
            ;;
        full)
            ARCH="x86_64"
            FULL_BUILD=true
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo "Error: Unknown option '$1'"
            echo ""
            show_usage
            exit 1
            ;;
    esac
elif [ $# -eq 2 ]; then
    case "$1" in
        a1000)
            ARCH="arm64_a1000b"
            ;;
        *)
            echo "Error: Unknown architecture '$1'"
            echo ""
            show_usage
            exit 1
            ;;
    esac
    
    case "$2" in
        full)
            FULL_BUILD=true
            ;;
        *)
            echo "Error: Unknown build type '$2'"
            echo ""
            show_usage
            exit 1
            ;;
    esac
else
    echo "Error: Too many arguments"
    echo ""
    show_usage
    exit 1
fi

# Set build directory based on architecture
if [ "${ARCH}" = "arm64_a1000b" ]; then
    BUILD_DIR="build_a1000"
else
    BUILD_DIR="build"
fi

# Display build configuration
echo "=============================================================="
echo "                   LCS BUILD PROCESS STARTING                "
echo "=============================================================="
echo "Architecture: ${ARCH}"
echo "Build Type: $([ "$FULL_BUILD" = true ] && echo "Full Build (with cleanup)" || echo "Incremental Build")"
echo "Build Directory: ${BUILD_DIR}"
echo ""

# Step 0: Clean build cache if full build is requested
if [ "$FULL_BUILD" = true ]; then
    echo "=============================================================="
    echo "                        STEP 0/5                            "
    echo "                   BUILD CACHE CLEANUP                      "
    echo "=============================================================="
    echo "🧹 Performing full build cache cleanup..."
    
    # Clean main build directory
    MAIN_BUILD_PATH="/hfr/${BUILD_DIR}"
    echo "🗑️  Cleaning main build directory: ${MAIN_BUILD_PATH}"
    if [ -d "${MAIN_BUILD_PATH}" ]; then
        rm -rf "${MAIN_BUILD_PATH}"
        echo "   ✅ Removed: ${MAIN_BUILD_PATH}"
    else
        echo "   ℹ️  Directory not found: ${MAIN_BUILD_PATH}"
    fi
    
    # Clean LCS module build directory
    LCS_BUILD_PATH="/hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs/${BUILD_DIR}"
    echo "🗑️  Cleaning LCS module build directory: ${LCS_BUILD_PATH}"
    if [ -d "${LCS_BUILD_PATH}" ]; then
        rm -rf "${LCS_BUILD_PATH}"
        echo "   ✅ Removed: ${LCS_BUILD_PATH}"
    else
        echo "   ℹ️  Directory not found: ${LCS_BUILD_PATH}"
    fi
    
    echo "✅ Build cache cleanup completed successfully!"
    echo ""
fi

# Step 1: Navigate to /hfr and configure cmake based on ARCH
echo "=============================================================="
echo "                        STEP 1/5                            "
echo "               CMAKE CONFIGURATION SETUP                    "
echo "=============================================================="
cd /hfr

if [ "${ARCH}" = "arm64_a1000b" ]; then
    echo "🏗️  Building for arm64_a1000b architecture..."
    echo "📋 Command: cmake -Bbuild_a1000 -Sscript -DCMAKE_BUILD_TYPE=Release"
    cmake -Bbuild_a1000 -Sscript -DCMAKE_BUILD_TYPE=Release
    BUILD_DIR="build_a1000"
else
    echo "🏗️  Building for x86_64 architecture..."
    echo "📋 Command: cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=Debug"
    cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=Debug
    BUILD_DIR="build"
fi
echo "✅ CMAKE configuration completed successfully!"
echo ""

# Step 2: Navigate to LCS module directory
echo "=============================================================="
echo "                        STEP 2/5                            "
echo "             NAVIGATE TO LCS MODULE DIRECTORY               "
echo "=============================================================="
echo "📁 Changing directory to: /hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs"
cd /hfr/modules/HAF/lidar_curb_perception_segment/core/private/lcs
echo "✅ Directory changed successfully!"
echo ""

# Step 3: Build the module using the appropriate build directory
echo "=============================================================="
echo "                        STEP 3/5                            "
echo "                   LCS MODULE BUILD                         "
echo "=============================================================="
echo "🔨 Building LCS module in ${BUILD_DIR} directory..."

# Create build directory if it doesn't exist (especially for full builds)
if [ ! -d "${BUILD_DIR}" ]; then
    echo "📁 Creating build directory: ${BUILD_DIR}"
    mkdir -p "${BUILD_DIR}"
fi

echo "📁 Changing to build directory: ${BUILD_DIR}"
cd ${BUILD_DIR}
echo "📋 Executing: cmake .."
cmake -DLCS_BUILD_TEST_EXE=OFF ..
echo "📋 Executing: make -j8"
make -j8
echo "📋 Executing: make install"
make install
echo "✅ LCS module build completed successfully!"
echo ""

# Step 4: Global build and install
echo "=============================================================="
echo "                        STEP 4/5                            "
echo "                 GLOBAL BUILD AND INSTALL                   "
echo "=============================================================="
echo "🌍 Performing global build and install..."
echo "📁 Changing directory to: /hfr"
cd /hfr

if [ "${ARCH}" = "arm64_a1000b" ]; then
    echo "🚀 Executing global build for arm64_a1000b architecture..."
    echo "📋 Command: cmake --build build_a1000 --target install"
    cmake --build build_a1000 --target install --parallel 8
else
    echo "🚀 Executing global build for x86_64 architecture..."
    echo "📋 Command: cmake --build build --target install"
    cmake --build build --target install --parallel 8
fi
echo "✅ Global build and install completed successfully!"
echo ""

# Step 5: Display build artifacts information
echo "=============================================================="
echo "                        STEP 5/5                            "
echo "               BUILD ARTIFACTS INFORMATION                  "
echo "=============================================================="
echo "📦 Displaying generated library and binary information..."
echo ""

# Check LCS dynamic library
LCS_LIB_DIR="/hfr/${BUILD_DIR}/install/${ARCH}/lib"
echo "🔍 Checking LCS dynamic library in: ${LCS_LIB_DIR}"
if [ -d "${LCS_LIB_DIR}" ]; then
    LCS_LIB_FILE=$(find "${LCS_LIB_DIR}" -name "liblcs.so*" -type f 2>/dev/null | head -1)
    if [ -n "${LCS_LIB_FILE}" ]; then
        LCS_LIB_NAME=$(basename "${LCS_LIB_FILE}")
        LCS_LIB_TIME=$(stat -c "%Y" "${LCS_LIB_FILE}" 2>/dev/null || stat -f "%m" "${LCS_LIB_FILE}" 2>/dev/null)
        LCS_LIB_FORMATTED_TIME=$(date -d "@${LCS_LIB_TIME}" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || date -r "${LCS_LIB_TIME}" "+%Y-%m-%d %H:%M:%S" 2>/dev/null)
        echo "✅ LCS Library Found:"
        echo "   📄 File: ${LCS_LIB_NAME}"
        echo "   🕐 Generated: ${LCS_LIB_FORMATTED_TIME}"
        echo "   📍 Location: ${LCS_LIB_FILE}"
    else
        echo "❌ LCS library (liblcs.so*) not found in ${LCS_LIB_DIR}"
    fi
else
    echo "❌ LCS library directory not found: ${LCS_LIB_DIR}"
fi
echo ""

# Check lidar_curb_perception_segment module binary
MODULE_BIN_DIR="/hfr/${BUILD_DIR}/install/${ARCH}/modules/lidar_curb_perception_segment/bin"
echo "🔍 Checking lidar_curb_perception_segment module binary in: ${MODULE_BIN_DIR}"
if [ -d "${MODULE_BIN_DIR}" ]; then
    MODULE_BIN_FILE=$(find "${MODULE_BIN_DIR}" -name "liblidar_curb_perception_segment.so*" -type f 2>/dev/null | head -1)
    if [ -n "${MODULE_BIN_FILE}" ]; then
        MODULE_BIN_NAME=$(basename "${MODULE_BIN_FILE}")
        MODULE_BIN_TIME=$(stat -c "%Y" "${MODULE_BIN_FILE}" 2>/dev/null || stat -f "%m" "${MODULE_BIN_FILE}" 2>/dev/null)
        MODULE_BIN_FORMATTED_TIME=$(date -d "@${MODULE_BIN_TIME}" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || date -r "${MODULE_BIN_TIME}" "+%Y-%m-%d %H:%M:%S" 2>/dev/null)
        echo "✅ Module Binary Found:"
        echo "   📄 File: ${MODULE_BIN_NAME}"
        echo "   🕐 Generated: ${MODULE_BIN_FORMATTED_TIME}"
        echo "   📍 Location: ${MODULE_BIN_FILE}"
    else
        echo "❌ Module binary (liblidar_curb_perception_segment.so*) not found in ${MODULE_BIN_DIR}"
    fi
else
    echo "❌ Module binary directory not found: ${MODULE_BIN_DIR}"
fi
echo ""

echo "=============================================================="
echo "                    🎉 BUILD SUCCESSFUL! 🎉                 "
echo "=============================================================="
echo "✅ LCS build process completed successfully!"
echo "📦 Architecture: ${ARCH}"
echo "🔧 Build Type: $([ "$FULL_BUILD" = true ] && echo "Full Build" || echo "Incremental Build")"
echo "🕐 Build finished at: $(date)"
echo "==============================================================" 