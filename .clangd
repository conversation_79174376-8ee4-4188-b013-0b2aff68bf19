CompileFlags:
  Add:
    - "-I/hfr"
    - "-I/hfr/lib/components_and_data_center/x86_64/include"
    - "-I/hfr/lib/planning/x86_64/include"
    - "-I/hfr/lib/fusion_localization_cargovehicle/x86_64/include"
    - "-I/hfr/lib/agent_prediction/x86_64/include"
    - "-I/hfr/lib/iceoryx/x86_64/include"
    - "-I/hfr/lib/map/x86_64/include"
    - "-I/hfr/lib/hlog/x86_64/include"
    - "-I/hfr/lib/execution_manifest/x86_64/include"
    - "-I/hfr/lib/vsomeip/x86_64/include"
    - "-I/hfr/lib/autocalib/x86_64/include"
    - "-I/hfr/lib/control/x86_64/include"
    - "-I/hfr/lib/lidar_obstacle_perception_segment/x86_64/include"
    - "-I/hfr/lib/lidar_obstacle_perception_detect/x86_64/include"
    - "-I/hfr/lib/lidar_obstacle_perception_fusion/x86_64/include"
    - "-I/hfr/lib/lidar_obstacle_perception_track/x86_64/include"
    - "-I/hfr/lib/road_boundary_perception_track/x86_64/include"
    - "-I/hfr/3rdparty/boost/x86_64/include"
    - "-I/hfr/3rdparty/yaml/x86_64/include"
    - "-I/hfr/3rdparty/opencv/x86_64/include"
    - "-I/hfr/3rdparty/eigen/x86_64/include"
    - "-I/hfr/3rdparty/pcl/x86_64/include"
    - "-I/hfr/3rdparty/glog/x86_64/include"
    - "-I/hfr/3rdparty/protobuf/x86_64/include"
    - "-I/hfr/3rdparty/flann/x86_64/include"
    - "-I/hfr/interface"
    - "-I/usr/local/cuda-11.1/include"
    - "-I/usr/src/tensorrt/samples/common"
    - "-std=c++14"
    - "-DHASCO_HFR_ARCH=\"x86_64\""
    - "-DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED"
    - "-fopenmp"
    - "-pthread"
  Remove:
    - "-march=*"
    - "-mtune=*"

Index:
  Background: Build
  StandardLibrary: Yes

Diagnostics:
  ClangTidy:
    Add: [performance-*, readability-*, modernize-*]
    Remove: [modernize-use-trailing-return-type, readability-magic-numbers]
  UnusedIncludes: Strict
  MissingIncludes: Strict

Completion:
  AllScopes: Yes

InlayHints:
  Enabled: Yes
  ParameterNames: Yes
  DeducedTypes: Yes 