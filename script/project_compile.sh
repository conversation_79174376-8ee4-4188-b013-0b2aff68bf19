#!/usr/bin/env bash

CRT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd -P)"

ROOT_PATH=${CRT_PATH%%/script*}

CODE_TMP_PATH=${ROOT_PATH}/tmp

TARGET_ARCH="$(uname -m)"
PROJECT_NAME="HPP"
PROJECT_VERSION="1.0.0"
PROJECT_MANIFEST="platform_project/default.xml"
PROJECT_DESTPATH="/data2/dailybuild/zhl"
PROJECT_EX_MACRO=
IS_CI_COMPILE=0
BST_DOWNLOADS=
IS_DEBUG=0

GIT_SSH_URL="git@***********:p_adas_hpp/hf/hf_repo.git -b release -m "

function show_usage() 
{
    cat <<EOF
Usage: $0 [options] ...
    -t, --target           Specify the target type to compile. default is x86_64
    -p, --project          Specify the name of project. default is hpp
    -v, --version          Specify the version of current code. default is 1.0.0
    -m, --manifest         Specify the file name of current project manifest. default is default.xml
    -o, --outpath          Specify the path of output X.tar.gz
    -c, --CI               Specify the type of CI, use /miniforge3/bin/repo when CI
    --sdk                  Compile a1000 sdk
OPTIONS:
    -e, --ex_macro         Specify the macro define to extern
    -h, --help             Display this help and exit.
Example :
     $0 -t x86_64 -p hhp -v 1.0.2 -m default.xml -d /data2/dailybuild
EOF
}

function make_install_tmp_path ()
{
    echo "ready to create tmp file : ${CODE_TMP_PATH}"
    rm -rf ${CODE_TMP_PATH}
    mkdir -p ${CODE_TMP_PATH}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to create :[ ${CODE_TMP_PATH} ], error code : ${ret}"
    fi
    return ${ret}
}

function download_project_code ()
{
    cd ${CODE_TMP_PATH}
    REPO=repo
    if [ ${IS_CI_COMPILE} -eq 1 ] ; then
        if [ ! -d ${CODE_TMP_PATH}/.repo ]; then
          mkdir -p ${CODE_TMP_PATH}/.repo
        fi
        cp -r ~/.repo/repo ./.repo
        REPO=~/miniforge3/bin/repo
    fi
    echo "=== will use $REPO"
    ${REPO} init -u ${GIT_SSH_URL}${PROJECT_MANIFEST} --no-clone-bundle
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to repo init code :[ ${REPO} ${GIT_SSH_URL}${PROJECT_MANIFEST} ], error code : ${ret}"
    fi

    ${REPO} sync -f
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to repo synccd code :[ ${REPO} ${GIT_SSH_URL}${PROJECT_MANIFEST} ], error code : ${ret}"
    fi
    
    return ${ret}
}

function enter_docker_and_compile ()
{
    # start to compile in docker
    if [ "$TARGET_ARCH" == "x86_64" ]; then
        cd ${CODE_TMP_PATH}/hfr
        echo "start x86 docker"
        bash script/docker/dev_start_ci.sh -r hpp_x86 -t v1.2
    elif [ "$TARGET_ARCH" == "arm64_a1000b" ]; then
        cd ${CODE_TMP_PATH}/hfr
        echo "start a1000 docker"
        bash script/docker/dev_start_ci.sh -r a1000b-sdk-fad -t 23.1
    else
        if [ -n "$SDK" ]; then
           echo "start yocto docker"
           cd ${CODE_TMP_PATH}/bstyoctolinux
           bash yocto-linux/docker_run.sh -r a1000_yocto -t 18.04 -l $BST_DOWNLOADS
        else
            echo "Unsupported target arch"
        fi
    fi
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to start docker in  [ ${PROJECT_NAME} ] in docker, error code : ${ret}"
    fi

    if [ -n "$SDK" ]; then
        bash yocto-linux/docker_exec_ci.sh /yocto-linux/build.sh ci
    else
        bash script/docker/dev_into_ci.sh /hfr/script/docker/auto_compile_project.sh ${TARGET_ARCH} ${PROJECT_NAME} ${PROJECT_VERSION} ${PROJECT_EX_MACRO}
    fi
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to compile  [ ${PROJECT_NAME} ] in docker, error code : ${ret}"
    fi
    return ${ret}
}

function packaging_compilation_results ()
{
    if [ -n "$SDK" ]; then
        cd ${CODE_TMP_PATH}/bstyoctolinux/yocto-linux/build/tmp/deploy/images/a1000
        echo "packing sdk"
        cp backup.img ${PROJECT_DESTPATH}/HDK_C_v2351/images/backup
        cp cache.img ${PROJECT_DESTPATH}/HDK_C_v2351/images/cache
        cp boot.img ${PROJECT_DESTPATH}/HDK_C_v2351/images/kernel
        cp recovery.img ${PROJECT_DESTPATH}/HDK_C_v2351/images/recovery
        cp core-image-bstos.img ${PROJECT_DESTPATH}/HDK_C_v2351/images/rootfs

        cd ${PROJECT_DESTPATH}
        COMPLIE_TIME=`date +%Y%m%d_%H%M`
        tar -czf HDK_C_${COMPLIE_TIME}.tar.gz HDK_C_v2351
    else
        cd ${CODE_TMP_PATH}/hfr
        cp ${PROJECT_NAME}_${PROJECT_VERSION}* ${PROJECT_DESTPATH}
        cp build/install/hfr_images/*.img ${PROJECT_DESTPATH}
    fi
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to copy  [ ${PROJECT_NAME}_${PROJECT_VERSION} ] , error code : ${ret}"
    else
        echo "Success to copy the file to ${PROJECT_DESTPATH}"
    fi
    return ${ret}
}

function parse_arguments() 
{
    while [ $# -gt 0 ]; do
        local opt="$1"
        shift
        case "${opt}" in
            -t | --target)
                if [ -n "${TARGET_ARCH}" ]; then
                    echo "Multiple option ${opt} specified, only the last one will take effect."
                fi
                TARGET_ARCH="$1"
                shift
                #optarg_check_for_opt "${opt}" "${custom_repository}"
                ;;

            -p | --project)
                if [ -n "${PROJECT_NAME}" ]; then
                    echo "Multiple option ${opt} specified, only the last one will take effect."
                fi
                PROJECT_NAME="$1"
                shift
                #optarg_check_for_opt "${opt}" "${custom_repository}"
                ;;

            -v | --version)
                if [ -n "${PROJECT_VERSION}" ]; then
                    echo "Multiple option ${opt} specified, only the last one will take effect."
                fi
                PROJECT_VERSION="$1"
                shift
                ;;

            -m | --manifest)
                if [ -n "${PROJECT_MANIFEST}" ]; then
                    echo "Multiple option ${opt} specified, only the last one will take effect."
                fi
                PROJECT_MANIFEST="$1"
                shift
                ;;
            -o | --outpath)
                if [ ! -d $1 ]; then
                    echo "Error : the directory does not exist, dir : ${1}"
                    exit 0
                fi
                if [ -n "${PROJECT_DESTPATH}" ]; then
                    echo "Multiple option ${opt} specified, only the last one will take effect."
                fi
                PROJECT_DESTPATH="$1"
                shift
                ;;
            -e | --ex_macro)
                if [ -n "${PROJECT_EX_MACRO}" ]; then
                    echo "Multiple option ${opt} specified, only the last one will take effect."
                fi
                PROJECT_EX_MACRO="$1"
                shift
                ;;
            -h | --help)
                show_usage
                exit 0
                ;;
            -c | --CI)
                IS_CI_COMPILE=1
                ;;
            --debug)
                IS_DEBUG=1
                ;;
            --sdk)
                SDK="A1000"
                BST_DOWNLOADS="$1"
                shift
                ;;

            *)
                echo "Unknown option: ${opt}"
                exit 2
                ;;
        esac
    done
}

function aftermath_handling_recovery()
{
    cd ${ROOT_PATH}
    if [ -n "$SDK" ]; then
        echo "remove sdk tmp dir"
        rm -rf ${CODE_TMP_PATH}
    else
        if [ -d ${CODE_TMP_PATH} ] && [ ${IS_DEBUG} -eq 0 ]; then
            echo "remove hfr tmp dir"
            rm -rf ${CODE_TMP_PATH}
        fi
    fi
}

function exit_if_return_failure()
{
    ret=$1
    if [ ${ret} -eq 0 ]; then
        echo "Success to call ${2}"
        return
    fi
    echo "Fault : in [$2], ret = ${ret}"
    aftermath_handling_recovery
    exit ${ret}
}

function main() 
{
    parse_arguments "$@"

    make_install_tmp_path
    exit_if_return_failure $? "make_install_tmp_path"

    download_project_code
    exit_if_return_failure $? "download_project_code"

    enter_docker_and_compile
    exit_if_return_failure $? "enter_docker_and_compile"

    packaging_compilation_results
    exit_if_return_failure $? "packaging_compilation_results"

    aftermath_handling_recovery
    exit 0
}

main "$@"
