#!/bin/bash

#Detection retry count
retryCount=6
#Detection interval second , 0.5s=500ms
detePeriod=0.5

apps=(config_service data_transfer camera_service around_view_monitor vehicle_service dead_reckoning line_slot_perception
 lidar_service mechanical_slot_perception slot_manager planning apa_function hmi control apa_ui)

CURRENT_DIR=$(cd $(dirname $0); pwd)
export LD_LIBRARY_PATH=$CURRENT_DIR/lib:$LD_LIBRARY_PATH

#ModelPath
MODULE_PATH=$CURRENT_DIR/modules/


#run deamon first
./iox-roudi -c roudi_config.toml&
./dlt-daemon -c dlt.conf&
sleep 1


checkprocess()
{
        #echo "checkprocess...$1"
        if [ "$1" = "" ];
        then
                return 1
        fi
        process_num=`ps -ef|grep "$1" |grep -v "grep" |wc -l`
        echo "checkprocess num=$process_num"
        return $process_num
}

runControl()
{   
    if [ ! -d "$MODULE_PATH/$1" ]; then
        echo "PATH[$MODULE_PATH/$1] is not exist, need to double check it"
        exit
    fi
    
    cd $MODULE_PATH/$1
    index=0
    while [ $index -le $retryCount ]
    do
        if [ ! -f "$MODULE_PATH/$1/run.sh" ]; then
          echo "file[$MODULE_PATH/$1/run.sh] is not exist, need to double check it"
          exit
        fi
        #start the app
        ./run.sh
        sleep $detePeriod
        checkprocess $1
        check_result=$?
        if [ $check_result -eq 0 ];
        then
              index=`expr $index + 1`
              continue
        else              
              return
        fi        
        
    done
}

#runControl test

for app in "${apps[@]}"
do
  runControl $app
done

#run iceoryx
#./iox-roudi -c roudi_config.toml&
#sleep 1
##load modules
#cd $CURRENT_DIR/modules/camera_service
#./run.sh
#sleep 1
#cd $CURRENT_DIR/modules/around_view_monitor
#./run.sh
#sleep 1
#cd $CURRENT_DIR/modules/vehicle_service
#./run.sh
#cd $CURRENT_DIR/modules/dead_reckoning
#./run.sh
#cd $CURRENT_DIR/modules/line_slot_perception
#./run.sh
#cd $CURRENT_DIR/modules/slot_manager
#./run.sh
#cd $CURRENT_DIR/modules/planning
#./run.sh
#cd $CURRENT_DIR/modules/apa_function
#./run.sh
#cd $CURRENT_DIR/modules/hmi
#./run.sh
#cd $CURRENT_DIR/modules/control
#./run.sh
#cd $CURRENT_DIR/modules/apa_ui
#./run.sh


