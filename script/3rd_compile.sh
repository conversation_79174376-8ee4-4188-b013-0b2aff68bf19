#!/usr/bin/env bash

TARGET_ARCH="$(uname -m)"

CRT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd -P)"
ROOT_PATH=${CRT_PATH%%/script*}

T3RD_PATH_NAME="3rdparty"
T3RD_NAME=$1

T3RD_SRC_RELATIVE_PATH="${T3RD_PATH_NAME}/${T3RD_NAME}"
INSTALL_RELATIVE_PATH="${T3RD_PATH_NAME}/install_${T3RD_NAME}"

TMP_SRC_PATH="${ROOT_PATH}/${T3RD_PATH_NAME}"
TMP_DEV_INSTALL_PATH=${ROOT_PATH}/${INSTALL_RELATIVE_PATH}

T3RD_REL_RELATIVE_PATH="3rdparty"
PROJECT_INFO_FILE_NAME="hproject_info.makefile"

T3RD_SSH_URL="git@***********:p_adas_hpp/hfr/h3rdl/${T3RD_NAME}.git"
T3RD_LATEST_COMMIT_ID=""
T3RD_REL2DEV_COMMIT_ID=""

function parse_arguments()
{
	opt="$1"
	if [[ "$opt" = "-h" ]] || [[ "$opt" = "--help" ]]; then
		show_usage
		exit 0
	else
		if [ $# -ne 1 ]; then
			show_usage
			exit 1
		fi
	fi
	T3RD_NAME=$1
    if [[ -z ${T3RD_NAME} ]]; then
        show_usage
        exit 1
    fi
    T3RD_SRC_RELATIVE_PATH="${T3RD_PATH_NAME}/${T3RD_NAME}"
    T3RD_SSH_URL="git@***********:p_adas_hpp/hfr/h3rdl/${T3RD_NAME}.git"
    TMP_DEV_INSTALL_PATH=${ROOT_PATH}/${INSTALL_RELATIVE_PATH}
	echo "== ready to compile ${T3RD_NAME}"
}

function show_usage() 
{
    cat <<EOF
Usage: $0 [options] ...
	used as : $0 XXX       XXX is the name of project in git
	example : $0 nana
OPTIONS:
    -h, --help             Display this help and exit.
EOF
}

function make_install_tmp_path ()
{
    rm -rf ${TMP_SRC_PATH}/*
    mkdir -p ${TMP_SRC_PATH}
    if [ ! -d ${TMP_SRC_PATH} ]; then
        echo "Fault : error to create dir : ${TMP_SRC_PATH}"
        return 1
    fi
    rm -rf ${TMP_DEV_INSTALL_PATH}
    mkdir -p ${TMP_DEV_INSTALL_PATH}
    if [ ! -d ${TMP_DEV_INSTALL_PATH} ]; then
        echo "Fault : error to create dir : ${TMP_DEV_INSTALL_PATH}"
        return 1
    fi
    return 0
}

function clone_code_and_get_commitid ()
{
    cd ${TMP_SRC_PATH}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to dowland the source code, the dir is not exist : ${TMP_SRC_PATH}"
        return ${ret}
    fi
    T3RD_SSH_URL="git@***********:p_adas_hpp/hfr/h3rdl/${1}.git"
    git clone ${T3RD_SSH_URL}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to dowland the source code,with : git clone ${T3RD_SSH_URL}"
        return ${ret}
    fi
    cd ${TMP_SRC_PATH}/${1}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to dowland the source code, the dir is not exist : ${TMP_SRC_PATH}/${1}"
        return ${ret}
    fi

    git branch | grep release
    ret=$?
    if [ ${ret} -eq 0 ]; then
        git checkout release
    else
        git checkout -b release remotes/origin/release
    fi
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to checkout release, in : ${TMP_SRC_PATH}/${1},error code : ${ret}"
        return ${ret}
    fi

    T3RD_REL2DEV_COMMIT_ID=""
    local version_line=`grep  "PROJECT_COMMIT_ID" ${PROJECT_INFO_FILE_NAME}`
    if [ $? = 0 ]; then
        T3RD_REL2DEV_COMMIT_ID=${version_line##*=}
    fi

    git branch | grep dev
    ret=$?
    if [ ${ret} -eq 0 ]; then
        git checkout dev
    else
        git checkout -b dev remotes/origin/dev
    fi
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to checkout dev, in : ${TMP_SRC_PATH}/${1},error code : ${ret}"
        return ${ret}
    fi

    T3RD_LATEST_COMMIT_ID=""
    T3RD_LATEST_COMMIT_ID=`git rev-parse HEAD`
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to get dev latest commit id, in : ${TMP_SRC_PATH}/${1},error code : ${ret}"
        return ${ret}
    fi
    if [[ -z  ${T3RD_LATEST_COMMIT_ID} ]] ; then
        echo "Fault : failed to get dev latest commit id, get id is ${T3RD_LATEST_COMMIT_ID}"
        ret=1
    fi
    return ${ret}
	#cp -rf /data1/hongliangz/work/def/hfr/nana ${TMP_SRC_PATH}
}

function check_if_need_compile ()
{
    ret=1
    if [ ${T3RD_REL2DEV_COMMIT_ID} = "\"${T3RD_LATEST_COMMIT_ID}\"" ]; then
        echo "Success : commit id is same, not need to complile & commit."
        ret=0
    else
        echo "Info : commit id not is same, need to complie & commit"
    fi
    echo "old=${T3RD_REL2DEV_COMMIT_ID},new=${T3RD_LATEST_COMMIT_ID}"
    return ${ret}
}


function 3rd_complie_source ()
{
    cd ${ROOT_PATH}

    bash script/docker/dev_start_ci.sh -r hpp_x86 -t v1.2
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to compile  [ ${T3RD_NAME} ], can't to create docker , error code : ${ret}"
        exit ${ret}
    fi

    # start to compile in docker
    bash script/docker/dev_into_ci.sh /hfr/script/docker/auto_compile_3rd.sh ${T3RD_SRC_RELATIVE_PATH} ${TARGET_ARCH} ${INSTALL_RELATIVE_PATH}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to compile  [ ${T3RD_SRC_RELATIVE_PATH} ] in docker, error code : ${ret}"
    fi
    return ${ret}
}

function 3rd_checkout_rel_branch ()
{
    local t3rd_code_path=${TMP_SRC_PATH}/${1}
    cd ${t3rd_code_path}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to checkout release, the dir is not exist : ${t3rd_code_path}"
        return ${ret}
    fi

    git reset --hard remotes/origin/dev
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to reset [remotes/origin/dev] in : ${t3rd_code_path},error code : ${ret}"
        return ${ret}
    fi

    git checkout release
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to checkout [remotes/origin/release] in : ${t3rd_code_path},error code : ${ret}"
    fi
    return ${ret}
}

function overwrite_3rd_from_install ()
{
    local t3rd_code_path=${TMP_SRC_PATH}/${1}
    cd ${t3rd_code_path}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to overwrite, dir is not exist : ${t3rd_code_path}"
        return $ret
    fi

    git status | grep -q "On branch release"
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to overwrite, not in release branch"
        return $ret
    fi

    rm -rf ${t3rd_code_path}/include
    rm -rf ${t3rd_code_path}/lib/${TARGET_ARCH}/*
    
    cp -rf ${TMP_DEV_INSTALL_PATH}/include ${t3rd_code_path}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to copy [${TMP_DEV_INSTALL_PATH}/include] to ${t3rd_code_path}"
        return $ret
    fi

    cp -rf ${TMP_DEV_INSTALL_PATH}/lib/* ${t3rd_code_path}/lib/${TARGET_ARCH}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to copy [${TMP_DEV_INSTALL_PATH}/lib] to ${t3rd_code_path}/lib/${TARGET_ARCH}"
        return $ret
    fi
    echo "export PROJECT_COMMIT_ID=\"${T3RD_LATEST_COMMIT_ID}\"" > ${t3rd_code_path}/${PROJECT_INFO_FILE_NAME}
    return $?
}

function commit_3rd_2_rel ()
{
    local t3rd_code_path=${TMP_SRC_PATH}/${1}
    cd ${t3rd_code_path}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : the directory of  ${t3rd_code_path} is not exist"
        return $ret
    fi

    git status . | grep -q "nothing to commit"
    ret=$?
    if [ ${ret} -eq 0 ]; then
        echo "Info : nothing to commit in ${t3rd_code_path}"
        return 0
    fi

    CRT_TIME=`date +%Y%m%d_%H%M%s`
    git add ./include
    ret=ret+$?
    git add ./lib
    ret=ret+$?
    git add ./${PROJECT_INFO_FILE_NAME}
    ret=ret+$?
    git commit -m "[${1}] commit by script for test at : ${CRT_TIME}"
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to commit ${t3rd_code_path},ret = ${ret}"
        return $ret
    fi

    git push
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to push ${t3rd_code_path},ret = ${ret}"
    fi
    return $ret
}

function aftermath_handling_recovery()
{
    if [ -d ${TMP_SRC_PATH} ]; then
        rm -rf ${TMP_SRC_PATH}/*
	fi

    rm -rf ${TMP_DEV_INSTALL_PATH}
    cd ${ROOT_PATH}
}

function exit_if_return_failure()
{
    ret=$1
    if [ ${ret} -eq 0 ]; then
        echo "Success to call ${2}"
        return
    fi
    echo "Fault : in [$2], ret = ${ret}"
    aftermath_handling_recovery
    exit ${ret}
}

function main() 
{
    parse_arguments "$@"

    make_install_tmp_path
    exit_if_return_failure $? "make_install_tmp_path"

    clone_code_and_get_commitid ${T3RD_NAME}
    exit_if_return_failure $? "clone_code_and_get_commitid"

    check_if_need_compile ${T3RD_NAME}
    if [ $? -eq 0 ]; then
        aftermath_handling_recovery
        exit 0
    fi

    3rd_complie_source
    exit_if_return_failure $? "3rd_complie_source"

    3rd_checkout_rel_branch ${T3RD_NAME}
    exit_if_return_failure $? "3rd_checkout_rel_branch"
 
    overwrite_3rd_from_install ${T3RD_NAME}
    exit_if_return_failure $? "overwrite_3rd_from_install"

    commit_3rd_2_rel ${T3RD_NAME}
    exit_if_return_failure $? "commit_3rd_2_rel"

    aftermath_handling_recovery
    exit 0
}


main "$@"
