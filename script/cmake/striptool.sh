#!/bin/bash

# char parameters
#$1 is strip tool
#$2 is dir
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <strip-tool-path> <directory>"
    exit 1
fi
 
STRIP_TOOL=$1
TARGET_DIR=$2 

#check dir
if [ ! -d "$TARGET_DIR" ]; then
    echo "Error: Directory '$TARGET_DIR' does not exist."
    exit 1
fi
 
while IFS= read -r -d '' file; do
    file_list+=("$file")
done < <(find "$TARGET_DIR" -type f \( -perm -u=x -o -name "*.so*" \) -print0)
 
# 打印数组中的每个文件路径（可选）
for file in "${file_list[@]}"; do
    echo "$STRIP_TOOL  $file"
    ${STRIP_TOOL} "$file"
done
 
echo "Stripping completed."
