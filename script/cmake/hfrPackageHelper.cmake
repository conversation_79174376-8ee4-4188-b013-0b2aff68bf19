include(GNUInstallDirs)

function(set_global)
    set(oneValueArgs VAR)
    set(multiValueArgs VALUE)
    cmake_parse_arguments(HFR "" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    set(${HFR_VAR} ${HFR_VALUE} CACHE INTERNAL "${HFR_VAR}")
endfunction()
  

Macro(setup_package_name_and_create_files)
    set(options )
    set(oneValueArgs NAME NAMESPACE PROJECT_PREFIX)
    set(multiValueArgs)
    cmake_parse_arguments(PARAMS "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})

    message("")
    message(" ==> --- setup_package_name_and_create_files ---")
    message(" ==> PARAMS_NAME = ${PARAMS_NAME}")
    message(" ==> PARAMS_NAMESPACE = ${PARAMS_NAMESPACE}")
    message(" ==> PARAMS_PROJECT_PREFIX = ${PARAMS_PROJECT_PREFIX}")

    set(PACKAGE_VERSION_FILE "${CMAKE_CURRENT_BINARY_DIR}/${PARAMS_NAME}ConfigVersion.cmake")
    set(PACKAGE_CONFIG_FILE "${CMAKE_CURRENT_BINARY_DIR}/${PARAMS_NAME}Config.cmake")
    set(TARGETS_EXPORT_NAME "${PARAMS_NAME}Targets")
    set(PROJECT_NAMESPACE ${PARAMS_NAMESPACE})

    message(" ==> PACKAGE_VERSION_FILE = ${PACKAGE_VERSION_FILE}")
    message(" ==> PACKAGE_CONFIG_FILE = ${PACKAGE_CONFIG_FILE}")
    message(" ==> TARGETS_EXPORT_NAME = ${TARGETS_EXPORT_NAME}")  
    message(" ==> PROJECT_NAMESPACE = ${PROJECT_NAMESPACE}")  

    set(DESTINATION_BINDIR ${CMAKE_INSTALL_FULL_BINDIR})
    set(DESTINATION_INCLUDEDIR ${CMAKE_INSTALL_FULL_INCLUDEDIR}/${PARAMS_PROJECT_PREFIX})
    set(DESTINATION_CONFIGDIR ${CMAKE_INSTALL_FULL_LIBDIR}/cmake/${PARAMS_NAME})

    message(" ==> DESTINATION_BINDIR = ${DESTINATION_BINDIR}")  
    message(" ==> DESTINATION_INCLUDEDIR = ${DESTINATION_INCLUDEDIR}")  
    message(" ==> DESTINATION_CONFIGDIR = ${DESTINATION_CONFIGDIR}") 

    #include(CMakePackageConfigHelpers)
    #write_basic_package_version_file(${PACKAGE_VERSION_FILE} COMPATIBILITY AnyNewerVersion)
    #configure_package_config_file("${PROJECT_SOURCE_DIR}/cmake/Config.cmake.in" ${PACKAGE_CONFIG_FILE} INSTALL_DESTINATION ${DESTINATION_CONFIGDIR})

    message(" ==> --- setup_package_name_and_create_files ---")
    message("")
endMacro()


Macro(setup_install_directories_and_export_package)
    set(options)
    set(multiValueArgs TARGETS INCLUDE_DIRECTORIES)
    cmake_parse_arguments(INSTALL "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    install_target_directories_and_header(TARGETS ${INSTALL_TARGETS} INCLUDE_DIRECTORIES ${INSTALL_INCLUDE_DIRECTORIES})
    install_package_files_and_export()
endMacro()


Macro(install_target_directories_and_header)
    set(options)
    set(multiValueArgs TARGETS INCLUDE_DIRECTORIES)
    cmake_parse_arguments(INSTALL "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    install(TARGETS ${INSTALL_TARGETS} EXPORT ${TARGETS_EXPORT_NAME}
            RUNTIME DESTINATION ${DESTINATION_BINDIR} COMPONENT bin
            LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR} COMPONENT bin
            ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR} COMPONENT bin)
    install(DIRECTORY ${INSTALL_INCLUDE_DIRECTORIES} 
            DESTINATION ${DESTINATION_INCLUDEDIR}
            COMPONENT dev)
endMacro()


Macro(install_package_files_and_export)
    install(FILES ${PACKAGE_VERSION_FILE} ${PACKAGE_CONFIG_FILE} 
            DESTINATION ${DESTINATION_CONFIGDIR})
    install(EXPORT ${TARGETS_EXPORT_NAME} 
            NAMESPACE ${PROJECT_NAMESPACE}::
            DESTINATION ${DESTINATION_CONFIGDIR})
endMacro()



Macro(hfr_set_file_language)
    set(switches USE_C_LANGUAGE)
    set(multiArguments FILES)
    cmake_parse_arguments(HFR "${switches}" "" "${multiArguments}" ${ARGN})

    if(HFR_USE_C_LANGUAGE)
        set_source_files_properties(${HFR_FILES} PROPERTIES LANGUAGE C)
        set_target_properties(${HFR_TARGET}  PROPERTIES 
                              C_STANDARD_REQUIRED ON
                              C_STANDARD 11)
    else()
        set_source_files_properties(${HFR_FILES} PROPERTIES LANGUAGE CXX)
        set_target_properties(${HFR_TARGET}  PROPERTIES
                              CXX_STANDARD_REQUIRED ON
                              CXX_STANDARD ${HFR_CXX_STANDARD})
    endif()
endMacro()



Macro(hfr_add_executable)
    set(switches USE_C_LANGUAGE PLACE_IN_BUILD_ROOT)
    set(arguments TARGET STACK_SIZE)
    set(multiArguments FILES LIBS INCLUDE_DIRECTORIES LINK_DIRECTORIES BUILD_INTERFACE INSTALL_INTERFACE)
    cmake_parse_arguments(HFR "${switches}" "${arguments}" "${multiArguments}" ${ARGN})

    add_executable(${HFR_TARGET} ${HFR_FILES})

    target_include_directories(${HFR_TARGET} PRIVATE ${HFR_INCLUDE_DIRECTORIES})

    #target_link_directories(${HFR_TARGET} PUBLIC ${HFR_LINK_DIRECTORIES})

    target_link_libraries(${HFR_TARGET} ${HFR_LIBS})

    if(HFR_PLACE_IN_BUILD_ROOT)
        set_target_properties(${HFR_TARGET} PROPERTIES RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}")
    endif()

    if (HFR_USE_C_LANGUAGE)
        hfr_set_file_language(USE_C_LANGUAGE FILES ${HFR_FILES})
    else()
        hfr_set_file_language(FILES ${HFR_FILES})
    endif()

    if(HFR_USE_C_LANGUAGE)
        target_compile_options(${HFR_TARGET} PRIVATE ${HFR_C_WARNINGS})
    else()
        target_compile_options(${HFR_TARGET} PRIVATE ${HFR_CXX_WARNINGS})
    endif()

    set_target_properties(${HFR_TARGET} PROPERTIES POSITION_INDEPENDENT_CODE ON) 

    foreach(INTERFACE ${HFR_BUILD_INTERFACE})
        target_include_directories(${HFR_TARGET} PUBLIC $<BUILD_INTERFACE:${INTERFACE}>)
    endforeach()

    foreach(INTERFACE ${HFR_INSTALL_INTERFACE})
        target_include_directories(${HFR_TARGET} PUBLIC $<INSTALL_INTERFACE:${INTERFACE}>)
    endforeach()

    install(TARGETS ${HFR_TARGET} RUNTIME DESTINATION bin)
endMacro()



Macro(hfr_add_library)
    set(switches USE_C_LANGUAGE NO_EXPORT NO_PACKAGE_SETUP NO_FIND_PACKAGE_SUPPORT STATIC)
    set(arguments TARGET NAMESPACE PROJECT_PREFIX)
    set(multiArguments FILES PUBLIC_LIBS PRIVATE_LIBS BUILD_INTERFACE
                       INSTALL_INTERFACE ADDITIONAL_EXPORT_TARGETS
                       PUBLIC_INCLUDES PRIVATE_INCLUDES LINK_DIRECTORIES
                       PUBLIC_LIBS_LINUX PRIVATE_LIBS_LINUX EXPORT_INCLUDE_DIRS)
    cmake_parse_arguments(HFR "${switches}" "${arguments}" "${multiArguments}" ${ARGN})

    if(NOT HFR_NO_PACKAGE_SETUP)
        setup_package_name_and_create_files(NAME ${HFR_TARGET} 
                                            NAMESPACE ${HFR_NAMESPACE}
                                            PROJECT_PREFIX ${HFR_PROJECT_PREFIX})
    endif()

    if(NOT HFR_NO_FIND_PACKAGE_SUPPORT)
        set(${HFR_TARGET}_DIR ${PROJECT_SOURCE_DIR}/cmake CACHE FILEPATH
            "${HFR_TARGET}Config.cmake to make find_package(${HFR_TARGET}) work in source tree!"
            FORCE)
    endif()

    if(HFR_STATIC)
        add_library(${HFR_TARGET} STATIC ${HFR_FILES})
    else()
        add_library(${HFR_TARGET} ${HFR_FILES})
    endif()

    if (HFR_NAMESPACE)
        add_library(${HFR_NAMESPACE}::${HFR_TARGET} ALIAS ${HFR_TARGET})
    endif()

    if (HFR_USE_C_LANGUAGE)
        hfr_set_file_language(USE_C_LANGUAGE FILES ${HFR_FILES})
    else()
        hfr_set_file_language(FILES ${HFR_FILES})
    endif()

    if (HFR_USE_C_LANGUAGE)
        if("-Wno-noexcept-type" IN_LIST HFR_WARNINGS) 
            list(REMOVE_ITEM HFR_WARNINGS "-Wno-noexcept-type")
        endif()
    endif()

    if(HFR_USE_C_LANGUAGE)
        target_compile_options(${HFR_TARGET} PRIVATE ${HFR_C_WARNINGS})
    else()
        target_compile_options(${HFR_TARGET} PRIVATE ${HFR_CXX_WARNINGS})
    endif()

    set_target_properties(${HFR_TARGET} PROPERTIES 
                          RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}"
                          VERSION ${PROJECT_VERSION}
                          SOVERSION ${PROJECT_VERSION_MAJOR})

    set_target_properties(${HFR_TARGET} PROPERTIES POSITION_INDEPENDENT_CODE ON)

    target_link_directories(${HFR_TARGET} PUBLIC ${HFR_LINK_DIRECTORIES})

    target_link_libraries(${HFR_TARGET} PUBLIC ${HFR_PUBLIC_LIBS} PRIVATE ${HFR_PRIVATE_LIBS})

    target_include_directories(${HFR_TARGET} PUBLIC ${HFR_PUBLIC_INCLUDES} PRIVATE ${HFR_PRIVATE_INCLUDES})

    foreach(INTERFACE ${HFR_BUILD_INTERFACE})
        target_include_directories(${HFR_TARGET} PUBLIC $<BUILD_INTERFACE:${INTERFACE}>)
    endforeach()

    foreach(INTERFACE ${HFR_INSTALL_INTERFACE})
        target_include_directories(${HFR_TARGET} PUBLIC $<INSTALL_INTERFACE:${INTERFACE}>)
    endforeach()

    if (EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/LICENSE)
        install(FILES ${CMAKE_CURRENT_SOURCE_DIR}/LICENSE
                DESTINATION share/doc/${HFR_TARGET}
                COMPONENT dev)
    endif()

    if (EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/doc/3rd_party_licenses)
        install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/doc/3rd_party_licenses
                DESTINATION share/doc/${HFR_TARGET}
                COMPONENT dev)
    endif()

    if (NOT HFR_NO_EXPORT)
        setup_install_directories_and_export_package(TARGETS ${HFR_TARGET} ${HFR_ADDITIONAL_EXPORT_TARGETS}
                                                     INCLUDE_DIRECTORIES ${HFR_EXPORT_INCLUDE_DIRS})
    endif()

    unset(HFR_NO_PACKAGE_SETUP)
    unset(HFR_NO_FIND_PACKAGE_SUPPORT)
endMacro()



Macro(hfr_module_library_compile_and_install)
    set(switches USE_C_LANGUAGE NO_EXPORT STATIC)
    set(arguments TARGET NAMESPACE)
    set(multiArguments FILES PUBLIC_LIBS PRIVATE_LIBS BUILD_INTERFACE INSTALL_INTERFACE
                       PUBLIC_INCLUDES PRIVATE_INCLUDES LINK_DIRECTORIES
                       ADDITIONAL_EXPORT_TARGETS EXPORT_INCLUDE_DIRS)
    cmake_parse_arguments(HFR "${switches}" "${arguments}" "${multiArguments}" ${ARGN})

    if(HFR_STATIC)
        add_library(${HFR_TARGET} STATIC ${HFR_FILES})
    else()
        add_library(${HFR_TARGET} ${HFR_FILES})
    endif()

    if (HFR_NAMESPACE)
        add_library(${HFR_NAMESPACE}::${HFR_TARGET} ALIAS ${HFR_TARGET})
    endif()
    
    if (HFR_USE_C_LANGUAGE)
        hfr_set_file_language(USE_C_LANGUAGE FILES ${HFR_FILES})
    else()
        hfr_set_file_language(FILES ${HFR_FILES})
    endif()

    set_target_properties(${HFR_TARGET} PROPERTIES POSITION_INDEPENDENT_CODE ON)

    target_link_directories(${HFR_TARGET} PUBLIC ${HFR_LINK_DIRECTORIES})

    target_link_libraries(${HFR_TARGET} PUBLIC ${HFR_PUBLIC_LIBS} PRIVATE ${HFR_PRIVATE_LIBS})

    target_include_directories(${HFR_TARGET} PUBLIC ${HFR_PUBLIC_INCLUDES} PRIVATE ${HFR_PRIVATE_INCLUDES})

    foreach(INTERFACE ${HFR_BUILD_INTERFACE})
        target_include_directories(${HFR_TARGET} PUBLIC $<BUILD_INTERFACE:${INTERFACE}>)
    endforeach()

    foreach(INTERFACE ${HFR_INSTALL_INTERFACE})
        target_include_directories(${HFR_TARGET} PUBLIC $<INSTALL_INTERFACE:${INTERFACE}>)
    endforeach()

    if (EXISTS ${PROJECT_SOURCE_DIR}/tool)
        install(DIRECTORY ${PROJECT_SOURCE_DIR}/tool
                DESTINATION ${CMAKE_LIB_INSTALL_PATH}/${HFR_TARGET})
    endif()

    if (NOT HFR_NO_EXPORT)
        install(TARGETS ${HFR_TARGET}
                RUNTIME DESTINATION ${CMAKE_LIB_INSTALL_PATH}/${HFR_TARGET}/${HFR_ARCH}/lib
                LIBRARY DESTINATION ${CMAKE_LIB_INSTALL_PATH}/${HFR_TARGET}/${HFR_ARCH}/lib
                ARCHIVE DESTINATION ${CMAKE_LIB_INSTALL_PATH}/${HFR_TARGET}/${HFR_ARCH}/lib)

        install(DIRECTORY ${HFR_EXPORT_INCLUDE_DIRS}
                DESTINATION ${CMAKE_LIB_INSTALL_PATH}/${HFR_TARGET})
    endif()

    unset(HFR_NO_PACKAGE_SETUP)
endMacro()



Macro(hfr_module_executable_compile_and_install)
    set(switches USE_C_LANGUAGE NO_EXPORT)
    set(arguments TARGET)
    set(multiArguments FILES PUBLIC_LIBS PRIVATE_LIBS BUILD_INTERFACE INSTALL_INTERFACE
                       PUBLIC_INCLUDES PRIVATE_INCLUDES LINK_DIRECTORIES
                       ADDITIONAL_EXPORT_TARGETS EXPORT_INCLUDE_DIRS)
    cmake_parse_arguments(HFR "${switches}" "${arguments}" "${multiArguments}" ${ARGN})

    add_executable(${HFR_TARGET} ${HFR_FILES})

    if (HFR_USE_C_LANGUAGE)
        hfr_set_file_language(USE_C_LANGUAGE FILES ${HFR_FILES})
    else()
        hfr_set_file_language(FILES ${HFR_FILES})
    endif()

    set_target_properties(${HFR_TARGET} PROPERTIES POSITION_INDEPENDENT_CODE ON)

    target_link_directories(${HFR_TARGET} PUBLIC ${HFR_LINK_DIRECTORIES})

    target_link_libraries(${HFR_TARGET} PUBLIC ${HFR_PUBLIC_LIBS} PRIVATE ${HFR_PRIVATE_LIBS})

    target_include_directories(${HFR_TARGET} PUBLIC ${HFR_PUBLIC_INCLUDES} PRIVATE ${HFR_PRIVATE_INCLUDES})

    foreach(INTERFACE ${HFR_BUILD_INTERFACE})
        target_include_directories(${HFR_TARGET} PUBLIC $<BUILD_INTERFACE:${INTERFACE}>)
    endforeach()

    foreach(INTERFACE ${HFR_INSTALL_INTERFACE})
        target_include_directories(${HFR_TARGET} PUBLIC $<INSTALL_INTERFACE:${INTERFACE}>)
    endforeach()

    if (EXISTS ${PROJECT_SOURCE_DIR}/tool)
        install(DIRECTORY ${PROJECT_SOURCE_DIR}/tool
                DESTINATION ${CMAKE_LIB_INSTALL_PATH}/${HFR_TARGET})
    endif()

    if (NOT HFR_NO_EXPORT)
        install(TARGETS ${HFR_TARGET}
                RUNTIME DESTINATION ${CMAKE_LIB_INSTALL_PATH}/${HFR_TARGET}/${HFR_ARCH}/lib
                LIBRARY DESTINATION ${CMAKE_LIB_INSTALL_PATH}/${HFR_TARGET}/${HFR_ARCH}/lib
                ARCHIVE DESTINATION ${CMAKE_LIB_INSTALL_PATH}/${HFR_TARGET}/${HFR_ARCH}/lib)

        install(DIRECTORY ${HFR_EXPORT_INCLUDE_DIRS}
                DESTINATION ${CMAKE_LIB_INSTALL_PATH}/${HFR_TARGET})
    endif()

    unset(HFR_NO_PACKAGE_SETUP)
endMacro()
