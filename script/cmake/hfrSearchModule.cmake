function(_glob_locations_single_layer out_paths out_names)
  set(PATHS ${ARGN})
  foreach(path ${PATHS})
    list(LENGTH paths before)
    get_filename_component(path "${path}" ABSOLUTE)
    if(NOT path STREQUAL CMAKE_CURRENT_SOURCE_DIR AND EXISTS "${path}/CMakeLists.txt")
      get_filename_component(name "${path}" NAME)
      list(APPEND paths "${path}")
      list(APPEND names "${name}")
    else()
      file(GLOB subdirs RELATIVE "${path}" "${path}/*")
      foreach(subdir ${subdirs})
        if(EXISTS "${path}/${subdir}/CMakeLists.txt")
          list(APPEND paths "${path}/${subdir}")
          list(APPEND names "${subdir}")
        endif()
      endforeach()
    endif()
    list(LENGTH paths after)
    if(before EQUAL after)
      message(WARNING "No modules has been found: ${path}")
    endif()
  endforeach()
  set(${out_paths} ${paths} PARENT_SCOPE)
  set(${out_names} ${names} PARENT_SCOPE)
endfunction()


function(_glob_locations_second_layer out_paths out_names)
  set(PATHS ${ARGN})
  foreach(path ${PATHS})
    list(LENGTH paths before)
    get_filename_component(path "${path}" ABSOLUTE)
    file(GLOB subdirs RELATIVE "${path}" "${path}/*")
    foreach(subdir ${subdirs})
      file(GLOB subsubdirs RELATIVE "${path}/${subdir}" "${path}/${subdir}/*")
      foreach(subsubdir ${subsubdirs})
        if(EXISTS "${path}/${subdir}/${subsubdir}/CMakeLists.txt")
          list(APPEND paths "${path}/${subdir}/${subsubdir}")
          list(APPEND names "${subsubdir}")
        endif()
      endforeach()
    endforeach()
    list(LENGTH paths after)
    if(before EQUAL after)
      message(WARNING "No modules has been found: ${path}")
    endif()
  endforeach()
  set(${out_paths} ${paths} PARENT_SCOPE)
  set(${out_names} ${names} PARENT_SCOPE)
endfunction()


macro(_add_modules_1 paths names)
  list(LENGTH ${paths} __len)
  if(NOT __len EQUAL 0)
    list(LENGTH ${names} __len_verify)
    if(NOT __len EQUAL __len_verify)
      message(FATAL_ERROR "Bad configuration! ${__len} != ${__len_verify}")
    endif() 
    math(EXPR __len "${__len} - 1")
    foreach(i RANGE ${__len})
      list(GET ${paths} ${i} __path)
      list(GET ${names} ${i} __name)
      #include("${__path}/cmake/init.cmake" OPTIONAL)
      message("    ==> build subdirectory: ${__path}")
      add_subdirectory("${__path}" "${CMAKE_CURRENT_BINARY_DIR}/.cache/${CMAKE_BUILD_TYPE}/${__name}")
    endforeach()
  endif()
endmacro()


macro(hfr_clear_vars)
  foreach(_var ${ARGN})
    unset(${_var})
    unset(${_var} CACHE)
  endforeach()
endmacro()


macro(hfr_glob_modules)
  set(ROOT_DIRS ${ARGN})
  message("")
  message("Start glob subdirectory module")
  _glob_locations_second_layer(__main_paths __main_names ${ROOT_DIRS})
  _add_modules_1(__main_paths __main_names)
  hfr_clear_vars(__main_names  __main_paths)
endmacro()


######################################################################


function(_glob_cmakes_single_layer out_paths out_names)
  set(PATHS ${ARGN})
  foreach(path ${PATHS})
    list(LENGTH paths before)
    get_filename_component(path "${path}" ABSOLUTE)
    file(GLOB subdirs RELATIVE "${path}" "${path}/*")
    foreach(subdir ${subdirs})
      if(EXISTS "${path}/${subdir}/lib" AND EXISTS "${path}/${subdir}/lib/cmake")
        file(GLOB subsubfiles LIST_DIRECTORIES false RELATIVE "${path}/${subdir}/lib/cmake" "${path}/${subdir}/lib/cmake/*.cmake")
        foreach(subsubfile ${subsubfiles})
          if(EXISTS "${path}/${subdir}/lib/cmake/${subsubfile}")
            list(APPEND paths "${path}/${subdir}/lib/cmake")
            list(APPEND names "${subsubfile}")
          endif()
        endforeach()
      endif()
    endforeach()
    list(LENGTH paths after)
    if(before EQUAL after)
      #message(WARNING "No configure cmakes has been found: ${path}")
      message("Search Done: ${path}")
    endif()
  endforeach()
  set(${out_paths} ${paths} PARENT_SCOPE)
  set(${out_names} ${names} PARENT_SCOPE)
endfunction()


macro(_add_cmakes_1 paths names)
  list(LENGTH ${paths} __len)
  if(NOT __len EQUAL 0)
    list(LENGTH ${names} __len_verify)
    if(NOT __len EQUAL __len_verify)
      message(FATAL_ERROR "Bad configuration! ${__len} != ${__len_verify}")
    endif() 
    math(EXPR __len "${__len} - 1")
    foreach(i RANGE ${__len})
      list(GET ${paths} ${i} __path)
      list(GET ${names} ${i} __name)
      include("${__path}/${__name}")
      message("    ==> find configure cmake: ${__path}/${__name}")
      #list(APPEND CMAKE_MODULE_PATH ${__path})
    endforeach()
  endif()
endmacro()


macro(hfr_glob_cmakes)
  set(ROOT_DIRS ${ARGN})
  message("")
  message("Start glob configure cmake")
  _glob_cmakes_single_layer(__main_paths __main_names ${ROOT_DIRS})
  _add_cmakes_1(__main_paths __main_names)
  hfr_clear_vars(__main_names  __main_paths)
endmacro()


######################################################################


function(_glob_libraries_single_layer out_paths out_names)
  set(PATHS ${ARGN})
  foreach(path ${PATHS})
    set(Without_LIB_COMPONENTS eigen;json;rs_driver)
    list(LENGTH paths before)
    get_filename_component(path "${path}" ABSOLUTE)
    file(GLOB subdirs RELATIVE "${path}" "${path}/*")
    foreach(subdir ${subdirs})
      list(FIND Without_LIB_COMPONENTS ${subdir} __componentIdx)
      if(__componentIdx LESS 0 AND EXISTS "${path}/${subdir}/${HFR_ARCH}/include" AND EXISTS "${path}/${subdir}/${HFR_ARCH}/lib")
        list(APPEND paths "${path}/${subdir}")
        list(APPEND names "${subdir}")
      elseif(NOT __componentIdx LESS 0 AND EXISTS "${path}/${subdir}/${HFR_ARCH}/include") 
      #elseif(EXISTS "${path}/${subdir}/include" AND subdir STREQUAL "eigen")
        list(APPEND paths "${path}/${subdir}")
        list(APPEND names "${subdir}")
      endif()
    endforeach()
    list(LENGTH paths after)
    if(before EQUAL after)
      message(WARNING "No configure cmakes has been found: ${path}")
    endif()
  endforeach()
  set(${out_paths} ${paths} PARENT_SCOPE)
  set(${out_names} ${names} PARENT_SCOPE)
endfunction()


macro(_add_libraries_1 paths names)
  list(LENGTH ${paths} __len)
  if(NOT __len EQUAL 0)
    list(LENGTH ${names} __len_verify)
    if(NOT __len EQUAL __len_verify)
      message(FATAL_ERROR "Bad configuration! ${__len} != ${__len_verify}")
    endif() 
    set(Without_LIB_COMPONENTS eigen;json)
    math(EXPR __len "${__len} - 1")
    foreach(i RANGE ${__len})
      list(GET ${paths} ${i} __path)
      list(GET ${names} ${i} __name)
      string(TOUPPER "${__name}" __nameUP)
      set(${__nameUP}_FOUND TRUE)
      set(${__nameUP}_DIR "${__path}/${HFR_ARCH}/")
      set(${__nameUP}_INCLUDE_DIR "${__path}/${HFR_ARCH}/include")
      list(FIND Without_LIB_COMPONENTS ${__name} __componentIdx)
      if(__componentIdx LESS 0)        
        set(${__nameUP}_LIBRARY_DIR "${__path}/${HFR_ARCH}/lib")
        file(GLOB subfiles LIST_DIRECTORIES false RELATIVE "${__path}/${HFR_ARCH}/lib" "${__path}/${HFR_ARCH}/lib/*.a" "${__path}/${HFR_ARCH}/lib/*.so")
        foreach(subfile ${subfiles})
          #get_filename_component(subfile "${subfile}" NAME_WLE)
          string(REGEX REPLACE "\.(a|so)$" "" subfile "${subfile}")
          string(REGEX REPLACE "^lib" "" subfile "${subfile}")
          list(APPEND normed_subfiles "${subfile}")
        endforeach()
        set(${__nameUP}_LIBRARIES "${normed_subfiles}")
        unset(normed_subfiles)
        #set(${__nameUP}_LIBRARIES "${__name}")
        #message("    ==> find library: ${__name}, And create variable: ${__nameUP}_INCLUDE_DIR, ${__nameUP}_LIBRARY_DIR, ${__nameUP}_LIBRARIES, ${__nameUP}_FOUND, the library target include: ${${__nameUP}_LIBRARIES}")
        message("    ==> find library: ${__name}, And create variable: ${__nameUP}_DIR , ${__nameUP}_INCLUDE_DIR , ${__nameUP}_LIBRARY_DIR , ${__nameUP}_LIBRARIES ")
      else()
        message("    ==> find library: ${__name}, And create variable: ${__nameUP}_DIR , ${__nameUP}_INCLUDE_DIR , ${__nameUP}_FOUND ")
      endif()
    endforeach()
  endif()
endmacro()


macro(hfr_glob_libraries)
  set(ROOT_DIRS ${ARGN})
  message("")
  message("Start glob library")
  _glob_libraries_single_layer(__main_paths __main_names ${ROOT_DIRS})
  _add_libraries_1(__main_paths __main_names)
  hfr_clear_vars(__main_names  __main_paths)
endmacro()


######################################################################


function(_export_libraries_single_layer out_paths out_names)
  set(PATHS ${ARGN})
  foreach(path ${PATHS})
    list(LENGTH paths before)
    get_filename_component(path "${path}" ABSOLUTE)
    file(GLOB subdirs RELATIVE "${path}" "${path}/*")
    foreach(subdir ${subdirs})
      if(EXISTS "${path}/${subdir}/${HFR_ARCH}" AND EXISTS "${path}/${subdir}/${HFR_ARCH}/lib")
        file(GLOB_RECURSE subfiles LIST_DIRECTORIES false RELATIVE "${path}/${subdir}/${HFR_ARCH}/lib" "${path}/${subdir}/${HFR_ARCH}/lib/*.so*")
        foreach(subfile ${subfiles})
          #get_filename_component(normed_subfile "${subfile}" NAME)
          list(APPEND paths "${path}/${subdir}/${HFR_ARCH}/lib")
          list(APPEND names "${subfile}")
        endforeach()
  #    elseif(EXISTS "${path}/${subdir}/${HFR_ARCH}" AND NOT EXISTS "${path}/${subdir}/${HFR_ARCH}/lib")
  #      file(GLOB_RECURSE subfiles LIST_DIRECTORIES false RELATIVE "${path}/${subdir}/lib" "${path}/${subdir}/lib/*.so")
  #      foreach(subfile ${subfiles})
  #        #get_filename_component(normed_subfile "${subfile}" NAME)
  #        list(APPEND paths "${path}/${subdir}/lib")
  #        list(APPEND names "${subfile}")
  #      endforeach()
      endif()
    endforeach()
    list(LENGTH paths after)
    if(before EQUAL after)
      message(WARNING "No need exported libraries has been found: ${path}")
    endif()
  endforeach()
  set(${out_paths} ${paths} PARENT_SCOPE)
  set(${out_names} ${names} PARENT_SCOPE)
endfunction()


macro(_export_libraries_1 paths names)
  list(LENGTH ${paths} __len)
  if(NOT __len EQUAL 0)
    list(LENGTH ${names} __len_verify)
    if(NOT __len EQUAL __len_verify)
      message(FATAL_ERROR "Bad configuration! ${__len} != ${__len_verify}")
    endif() 
    math(EXPR __len "${__len} - 1")
    foreach(i RANGE ${__len})
      list(GET ${paths} ${i} __path)
      list(GET ${names} ${i} __name)
      file(COPY ${__path}/${__name} DESTINATION ${HPP_LIB_INSTALL_PATH} FOLLOW_SYMLINK_CHAIN)
      get_filename_component(normed_name "${__path}/${__name}" NAME)
      message("    ==> export library: ${HPP_LIB_INSTALL_PATH}/${normed_name}")
    endforeach()
  endif()
endmacro()


macro(hfr_export_preparation)
  if(${HFR_ARCH} STREQUAL "x86_64")
   
    if( $ENV{UBUNTU_VERSION} )
        set(CUR_HOST  $ENV{UBUNTU_VERSION})
    else()
        set(CUR_HOST  "18.04")
    endif()

    if( DEFINED OPENCV_DIR )
       if( EXISTS ${OPENCV_DIR}/host)
          file(READ "${OPENCV_DIR}/host" OPENCV_HOST)
          string(FIND ${OPENCV_HOST} ${CUR_HOST} HOST_MATCH)
          if(${HOST_MATCH} EQUAL -1)    
             message(STATUS "Current working directory: ${CMAKE_CURRENT_SOURCE_DIR}")
             execute_process(COMMAND /bin/sh ${CMAKE_CURRENT_SOURCE_DIR}/cmake/preparation.sh ${OPENCV_DIR}/../ ${CUR_HOST} ) 
          endif()
       endif()
    endif()

  endif()
endmacro()


function(_hfr_strip_all_libraries filePath)
  if(${CMAKE_BUILD_TYPE} STREQUAL "release")
     set(_striptool "_UNUSED_")
     if(${HFR_ARCH} STREQUAL "arm64_a1000b")
        set(_striptool "aarch64-bst-linux-strip")
     endif()

     if(NOT _striptool STREQUAL "_UNUSED_")
        execute_process(COMMAND /bin/bash ${CMAKE_CURRENT_SOURCE_DIR}/cmake/striptool.sh  ${_striptool} ${filePath} )  
     endif()

  endif()
endfunction()


macro(hfr_export_all_libraries)
  set(ROOT_DIRS ${ARGN})
  message("")
  message("Start export all library")
  _export_libraries_single_layer(__main_paths __main_names ${ROOT_DIRS})
  _export_libraries_1(__main_paths __main_names)

  _hfr_strip_all_libraries(${HPP_LIB_INSTALL_PATH})

  hfr_clear_vars(__main_names  __main_paths)
  
endmacro()