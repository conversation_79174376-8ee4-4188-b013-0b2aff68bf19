macro(setHbiVersion)
  set(VALUES_FILE "/tmp/HBI_VERSION_VALUE")
  execute_process(COMMAND /bin/bash /hfr/script/cmake/hbiVersion.sh ${VALUES_FILE})
  file(STRINGS "${VALUES_FILE}" LINES)
  list(GET LINES 0 FIRST_LINE)
  set(HPP_HBI_VERSION ${FIRST_LINE})
endmacro()


if( DEFINED COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR )
  # The variable has already been defined
else()
  cmake_minimum_required(VERSION 3.10)
if(NOT HFR_ARCH)
  set(HFR_ARCH  $ENV{HASCO_ENV})
endif()
  string(FIND "${CMAKE_CURRENT_SOURCE_DIR}" "modules" _subIndex )
  
  if( _subIndex LESS 0)  
   string(FIND "${CMAKE_CURRENT_SOURCE_DIR}" "lib" _subIndex )
  endif()  

  string(SUBSTRING "${CMAKE_CURRENT_SOURCE_DIR}" 0 ${_subIndex} PROJECT_ROOT_PATH) 
  SET(CMAKE_ROOT_SOURCE_DIR ${PROJECT_ROOT_PATH}/script) 
#set install path
  if(HFR_ARCH STREQUAL "arm64_a1000")
    set(HPP_INSTALL_PREFIX "/hfr/build_a1000")
  else()
    set(HPP_INSTALL_PREFIX "/hfr/build")
  endif()
  set(HPP_TARGET_INSTALL_PATH "${HPP_INSTALL_PREFIX}/install/${HFR_ARCH}")
  set(HPP_LIB_INSTALL_PATH "${HPP_INSTALL_PREFIX}/install/${HFR_ARCH}/lib")
  set(HPP_MODULE_INSTALL_PATH "${HPP_INSTALL_PREFIX}/install/${HFR_ARCH}/modules")
  set(HPP_RESOURCE_INSTALL_PATH "${HPP_INSTALL_PREFIX}/install/${HFR_ARCH}/resource")
  set(HPP_DATA_INSTALL_PATH "${HPP_INSTALL_PREFIX}/install/${HFR_ARCH}/data")
#set path
  set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

  set(CMAKE_INSTALL_DEFAULT_DIRECTORY_PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_WRITE GROUP_EXECUTE WORLD_READ WORLD_WRITE WORLD_EXECUTE)
  set(CMAKE_INSTALL_MODE OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_WRITE GROUP_EXECUTE WORLD_READ WORLD_WRITE WORLD_EXECUTE)  

  set(CMAKE_LIB_INSTALL_PATH "${CMAKE_ROOT_SOURCE_DIR}/../lib")
  get_filename_component(CMAKE_LIB_INSTALL_PATH "${CMAKE_LIB_INSTALL_PATH}" ABSOLUTE)
  set(CMAKE_INTERFACE_INSTALL_PATH "${CMAKE_ROOT_SOURCE_DIR}/../interface")
  get_filename_component(CMAKE_INTERFACE_INSTALL_PATH "${CMAKE_INTERFACE_INSTALL_PATH}" ABSOLUTE)  
  
  include("${CMAKE_ROOT_SOURCE_DIR}/cmake/hfrPackageHelper.cmake")
  include("${CMAKE_ROOT_SOURCE_DIR}/cmake/hfrPlatformSettings.cmake")
  include("${CMAKE_ROOT_SOURCE_DIR}/cmake/hfrSearchModule.cmake")
  
  setHbiVersion()
  
#record PROJECT_NAME
  set(__current_project_name ${PROJECT_NAME})

  hfr_glob_cmakes(${CMAKE_ROOT_SOURCE_DIR}/../lib)
  hfr_glob_libraries(${CMAKE_ROOT_SOURCE_DIR}/../3rdparty ${CMAKE_ROOT_SOURCE_DIR}/../lib)

#restroe PROJECT_NAME
  set(PROJECT_NAME ${__current_project_name})  
endif()

function(common_defines)
#add_definitions(-DTARGET_NAME_DEF=\"${PROJECT_NAME}\")
target_compile_definitions(${PROJECT_NAME} PRIVATE TARGET_NAME_DEF=\"${PROJECT_NAME}\" MODULE_INSTALL_PATH=\"${HPP_MODULE_INSTALL_PATH}\")
endfunction()

function(common_defines_UDF)
set(CURRENT_EXE_NAME ${ARGN})
target_compile_definitions(${CURRENT_EXE_NAME} PRIVATE TARGET_NAME_DEF=\"${CURRENT_EXE_NAME}\" MODULE_INSTALL_PATH=\"${HPP_MODULE_INSTALL_PATH}\")
endfunction()

#close vsomeip deprecated
add_definitions(-DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED)

 # message("HPP_INSTALL_PREFIX=${HPP_INSTALL_PREFIX}")
 # message("HPP_TARGET_INSTALL_PATH=${HPP_TARGET_INSTALL_PATH}")
 # message("HPP_LIB_INSTALL_PATH=${HPP_LIB_INSTALL_PATH}")
 # message("HPP_MODULE_INSTALL_PATH=${HPP_MODULE_INSTALL_PATH}") 
 # message("HPP_RESOURCE_INSTALL_PATH=${HPP_RESOURCE_INSTALL_PATH}") 

 # message("HLOG_DIR=${HLOG_DIR}")
 # message("HLOG_INCLUDE_DIR=${HLOG_INCLUDE_DIR}")
 # message("HLOG_LIBRARY_DIR=${HLOG_LIBRARY_DIR}")

#create INCLUDE DIR list
list(APPEND COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR ${BOOST_INCLUDE_DIR})
list(APPEND COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR ${YAML_INCLUDE_DIR})
list(APPEND COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR ${HLOG_INCLUDE_DIR})
list(APPEND COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR ${ICEORYX_INCLUDE_DIR})
list(APPEND COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR ${EXECUTION_MANIFEST_INCLUDE_DIR})
list(APPEND COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR ${VSOMEIP_INCLUDE_DIR})

#create LIBRARY_DIR list
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR ${BOOST_LIBRARY_DIR})
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR ${YAML_LIBRARY_DIR})
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR ${HLOG_LIBRARY_DIR})
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR ${DLT_LIBRARY_DIR})
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR ${ICEORYX_LIBRARY_DIR})
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR ${EXECUTION_MANIFEST_LIBRARY_DIR})
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR ${VSOMEIP_LIBRARY_DIR})

#create LIBRARIES link list
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARIES ${BOOST_LIBRARIES})
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARIES ${YAML_LIBRARIES})
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARIES ${DLT_LIBRARIES})
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARIES ${HLOG_LIBRARIES})
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARIES ${ICEORYX_LIBRARIES})
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARIES ${EXECUTION_MANIFEST_LIBRARIES})
list(APPEND COMPONENTS_AND_DATA_CENTER_LIBRARIES ${VSOMEIP_LIBRARIES})


macro(makeCLeanLib srcName InstPriLib InstallDir)
install(CODE "
execute_process(COMMAND /bin/bash /hfr/script/cmake/cleanlib.sh  ${srcName} ${InstPriLib} )
execute_process(COMMAND /bin/bash /hfr/script/cmake/cleanlib.sh  ${srcName} ${InstallDir} )
")
endmacro()

macro(makeDeliveryBin srcName InstPriLib InstallDir)
message("Install bin ${srcName} To ${InstPriLib} And ${InstallDir} ")
install(TARGETS ${srcName} DESTINATION  ${InstPriLib})
install(TARGETS ${srcName} DESTINATION  ${InstallDir})
endmacro()

macro(makeDeliveryDir srcDir destDir )
message("Install directory ${srcDir} to ${destDir} ")
install(DIRECTORY ${srcDir}  DESTINATION   ${destDir})
endmacro()

macro(makeDeliveryFiles srcDir destDir  filesPat)
message("Install files match ${filesPat} from ${srcDir} to ${destDir} ")
install(DIRECTORY ${srcDir} 
        DESTINATION ${destDir}
        FILES_MATCHING PATTERN ${filesPat}
        )
endmacro()


