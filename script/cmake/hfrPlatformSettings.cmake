set_global(VAR HFR_CXX_STANDARD  VALUE 11)

set_global(VAR HFR_ARCH  VALUE $ENV{HASCO_ENV})

set_global(VAR HFR_C_WARNINGS  VALUE -W -Wall -Wextra -Wuninitialized -Wpedantic -Wstrict-aliasing -Wcast-align -Wconversion)
set_global(VAR HFR_CXX_WARNINGS  VALUE ${HFR_C_WARNINGS} -Wno-noexcept-type)

if(CMAKE_CXX_COMPILER_ID MATCHES "GNU")
    set_global(VAR HFR_CXX_WARNINGS VALUE ${HFR_CXX_WARNINGS} -Wuseless-cast)
endif()

if(BUILD_STRICT)
    set_global(VAR HFR_C_WARNINGS  VALUE ${HFR_C_WARNINGS} -Werror)
    set_global(VAR HFR_CXX_WARNINGS  VALUE ${HFR_CXX_WARNINGS} -Werror)
endif()

if(${HFR_ARCH} STREQUAL "tda4")
  set(TOOLCHAIN "$ENV{PSDKRA_PATH}/gcc-arm-9.2-2019.12-x86_64-aarch64-none-linux-gnu")
  set(CMAKE_C_COMPILER "${TOOLCHAIN}/bin/aarch64-none-linux-gnu-gcc")
  set(CMAKE_CXX_COMPILER "${TOOLCHAIN}/bin/aarch64-none-linux-gnu-g++")
endif()

if(NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE debug)
endif()
string(TOLOWER "${CMAKE_BUILD_TYPE}" lowerStrCmakeBuildType)
if(${lowerStrCmakeBuildType}  STREQUAL "release")
   set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
   set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -s")
endif()