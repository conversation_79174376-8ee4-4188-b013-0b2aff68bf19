#!/usr/bin/env bash

DOCKER_USER="${USER}"

DEV_CONTAINER="hpp_dev_${USER}_a1000"

xhost +local:root 1>/dev/null 2>&1

#docker attach "${DEV_CONTAINER}"
#docker exec -u "${DOCKER_USER}" -e HISTFILE=/hfr/.dev_bash_hist -it "${DEV_CONTAINER}" /bin/bash
autorun_script=
if [ "${1##*.}" = "sh" ] ;
then
    autorun_script="$@"
    echo "ready to compile : ${autorun_script},in docker"
fi

CURR_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd -P)"
curDockerId=$(docker inspect --format="{{.Id}}" ${DEV_CONTAINER})
echo ${curDockerId} > ${CURR_DIR}/../../.dockerInfo

docker exec -u root -e HISTFILE=/hfr/.dev_bash_hist -it "${DEV_CONTAINER}" /bin/bash ${autorun_script}
ret=$?
echo "docker return  : ${ret}"
xhost -local:root 1>/dev/null 2>&1
exit ${ret}
