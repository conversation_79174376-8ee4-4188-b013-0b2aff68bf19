ARG BASE_IMAGE=ubuntu:18.04
FROM $BASE_IMAGE
LABEL dockerfile_maintainer=hui.jiang

# setup timezone
RUN echo 'Etc/UTC' > /etc/timezone && \
    ln -s /usr/share/zoneinfo/Etc/UTC /etc/localtime && \
    apt-get update && \
    apt-get install -q -y --no-install-recommends tzda<PERSON> && \
    apt-get clean && \  
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

#install package
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    lsb-release gnupg2 \
    curl ca-certificates \
    zip gcc \
    python2.7 python3 \
    git make vim nano && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# ARG ORIGIN_PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

# cmake
ADD cmake-3.19.6.tar.gz /usr/local/
ARG CMAKE=/usr/local/cmake-3.19.6/bin
# ENV PATH=${CMAKE}:${ORIGIN_PATH}

# RUN curl -fsSL https://apt.kitware.com/keys/kitware-archive-latest.asc | apt-key add - \
#     && add-apt-repository 'deb https://apt.kitware.com/ubuntu/ focal main' \
#     && apt-get update && apt install cmake

# add cross-compile tool
ADD gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu.tar /opt/

# add opencv
#ADD opencv_3.4.5_arm64.tar /opt/arm3rdLib/
ARG TOOLCHAIN_INSTALL_PATH=/opt/gcc-linaro-6.5.0-2018.12-x86_64_aarch64-linux-gnu
ENV HASCO_ENV=ARM_J3
ENV STRIP=aarch64-linux-gnu-strip
ENV OBJCOPY=aarch64-linux-gnu-objcopy
ENV RANLIB=aarch64-linux-gnu-ranlib
ENV OBJDUMP=aarch64-linux-gnu-objdump
ENV AR=aarch64-linux-gnu-ar
ENV AS=aarch64-linux-gnu-as
ENV CC=aarch64-linux-gnu-gcc
ENV PKG_CONFIG_SYSROOT_DIR=${TOOLCHAIN_INSTALL_PATH}/aarch64-linux-gnu
ENV TARGET_PREFIX=aarch64-linux-gnu-
ENV NM=aarch64-linux-gnu-nm
ENV CXX=aarch64-linux-gnu-g++
ENV OECORE_TARGET_SYSROOT=${TOOLCHAIN_INSTALL_PATH}/aarch64-linux-gnu
ENV CROSS_COMPILE=aarch64-linux-gnu-
ENV CPP=aarch64-linux-gnu-gcc
ENV LD=aarch64-linux-gnu-ld
ENV PATH=${CMAKE}:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:${TOOLCHAIN_INSTALL_PATH}/bin

CMD [ "bash" ]