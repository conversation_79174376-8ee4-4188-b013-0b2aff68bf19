### Dockerfile构建镜像

1. 创建build context
> mkdir ~/hpp_x86_img

2. 准备Dockerfile和安装包
- 拷贝Dockerfile至build context
> cp hfr/script/docker/docker_build/Dockerfile ~/hpp_x86_img
- 从share02 *(\\*********\share02\软件二部共享目录\开发环境容器镜像\hpp_x86_v2.0\docker_build_context)* 拷贝cmake和TensorRT至build context

3. 构建镜像
> cd ~/hpp_x86_img

> docker build -t hpp_x86:v2.0 **.**
- **docker build命令行最后有个点，表示当前目录为build context**

4. 给最新版本镜像打上lastest tag
> docker tag hpp_x86:v2.0 hpp_x86:lastest

5. 保存镜像到本地
> docker save -o ~/{your_path}/hpp_x86_v2.0.tar hpp_x86:v2.0