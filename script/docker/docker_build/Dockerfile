ARG BASE_IMAGE=ubuntu:20.04
FROM $BASE_IMAGE
LABEL dockerfile_maintainer=hui.jiang

# setup timezone
RUN echo 'Etc/UTC' > /etc/timezone && \
    ln -s /usr/share/zoneinfo/Etc/UTC /etc/localtime && \
    apt-get update && \
    apt-get install -q -y --no-install-recommends t<PERSON><PERSON> && \
    apt-get clean && \  
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

#install package
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    lsb-release gnupg2 \
    curl ca-certificates \
    zip gcc \
    python2.7 python3 \
    git make vim nano && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# nvidia cuda
ENV NVARCH=x86_64
ENV NV_CUDA_CUDART_VERSION=11.1.74-1
ENV NV_CUDA_COMPAT_PACKAGE=cuda-compat-11-1
ARG TARGETARCH=amd64
LABEL cuda_maintainer=NVIDIA_CORPORATION_<<EMAIL>>

RUN apt-get update \
    && curl -fsSL https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/${NVARCH}/3bf863cc.pub | apt-key add - \
    && echo "deb https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/${NVARCH} /" > /etc/apt/sources.list.d/cuda.list \
    # && apt-get purge --autoremove -y curl \
    && apt-get clean \  
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

ENV CUDA_VERSION=11.1.1

RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        cuda-cudart-11-1=${NV_CUDA_CUDART_VERSION} ${NV_CUDA_COMPAT_PACKAGE}  \
    && ln -s cuda-11.1 /usr/local/cuda \
    && apt-get clean \  
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*
        
RUN echo "/usr/local/nvidia/lib" >> /etc/ld.so.conf.d/nvidia.conf  \
    && echo "/usr/local/nvidia/lib64" >> /etc/ld.so.conf.d/nvidia.conf

ARG ORIGIN_PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ARG NVIDIA=/usr/local/nvidia/bin:/usr/local/cuda/bin
ENV PATH=${NVIDIA}:${ORIGIN_PATH}
ENV LD_LIBRARY_PATH=/usr/local/cuda/lib64:/usr/local/nvidia/lib:/usr/local/nvidia/lib64

# COPY NGC-DL-CONTAINER-LICENSE /

ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility
ENV NV_CUDA_LIB_VERSION=11.1.1-1
ENV NV_NVTX_VERSION=11.1.74-1
ENV NV_LIBNPP_VERSION=11.1.2.301-1
ENV NV_LIBNPP_PACKAGE=libnpp-11-1=11.1.2.301-1
ENV NV_LIBCUSPARSE_VERSION=11.3.0.10-1
ENV NV_LIBCUBLAS_PACKAGE_NAME=libcublas-11-1
ENV NV_LIBCUBLAS_VERSION=11.3.0.106-1
ENV NV_LIBCUBLAS_PACKAGE=libcublas-11-1=11.3.0.106-1
ENV NV_LIBNCCL_PACKAGE_NAME=libnccl2
ENV NV_LIBNCCL_PACKAGE_VERSION=2.8.4-1
ENV NCCL_VERSION=2.8.4-1
ENV NV_LIBNCCL_PACKAGE=libnccl2=2.8.4-1+cuda11.1

RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        cuda-libraries-11-1=${NV_CUDA_LIB_VERSION} \
        ${NV_LIBNPP_PACKAGE} cuda-nvtx-11-1=${NV_NVTX_VERSION} \
        libcusparse-11-1=${NV_LIBCUSPARSE_VERSION} \
        ${NV_LIBCUBLAS_PACKAGE} ${NV_LIBNCCL_PACKAGE} \
    && apt-get clean \  
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

RUN apt-mark hold ${NV_LIBCUBLAS_PACKAGE_NAME} ${NV_LIBNCCL_PACKAGE_NAME}

ENV NV_CUDA_CUDART_DEV_VERSION=11.1.74-1
ENV NV_NVML_DEV_VERSION=11.1.74-1
ENV NV_LIBCUSPARSE_DEV_VERSION=11.3.0.10-1
ENV NV_LIBNPP_DEV_VERSION=11.1.2.301-1
ENV NV_LIBNPP_DEV_PACKAGE=libnpp-dev-11-1=11.1.2.301-1
ENV NV_LIBCUBLAS_DEV_VERSION=11.3.0.106-1
ENV NV_LIBCUBLAS_DEV_PACKAGE_NAME=libcublas-dev-11-1
ENV NV_LIBCUBLAS_DEV_PACKAGE=libcublas-dev-11-1=11.3.0.106-1
ENV NV_NVPROF_VERSION=11.1.105-1
ENV NV_NVPROF_DEV_PACKAGE=cuda-nvprof-11-1=11.1.105-1
ENV NV_LIBNCCL_DEV_PACKAGE_NAME=libnccl-dev
ENV NV_LIBNCCL_DEV_PACKAGE_VERSION=2.8.4-1
ENV NV_LIBNCCL_DEV_PACKAGE=libnccl-dev=2.8.4-1+cuda11.1
RUN apt-get update  \
    && apt-get install -y --no-install-recommends \
        cuda-cudart-dev-11-1=${NV_CUDA_CUDART_DEV_VERSION} \
        cuda-command-line-tools-11-1=${NV_CUDA_LIB_VERSION} \
        cuda-minimal-build-11-1=${NV_CUDA_LIB_VERSION} \
        cuda-libraries-dev-11-1=${NV_CUDA_LIB_VERSION} \
        cuda-nvml-dev-11-1=${NV_NVML_DEV_VERSION} \
        ${NV_NVPROF_DEV_PACKAGE} ${NV_LIBNPP_DEV_PACKAGE} \
        libcusparse-dev-11-1=${NV_LIBCUSPARSE_DEV_VERSION} \
        ${NV_LIBCUBLAS_DEV_PACKAGE} ${NV_LIBNCCL_DEV_PACKAGE}  \
    && apt-get clean \  
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

RUN apt-mark hold ${NV_LIBCUBLAS_DEV_PACKAGE_NAME} \
    ${NV_LIBNCCL_DEV_PACKAGE_NAME}

ENV LIBRARY_PATH=/usr/local/cuda/lib64/stubs
ENV NV_CUDNN_VERSION=8.0.5.39
ENV NV_CUDNN_PACKAGE_NAME=libcudnn8
ENV NV_CUDNN_PACKAGE=libcudnn8=8.0.5.39-1+cuda11.1
ENV NV_CUDNN_PACKAGE_DEV=libcudnn8-dev=8.0.5.39-1+cuda11.1

LABEL com.nvidia.cudnn.version=8.0.5.39
RUN apt-get update  \
    && apt-get install -y --no-install-recommends \
        ${NV_CUDNN_PACKAGE} ${NV_CUDNN_PACKAGE_DEV} \
    && apt-mark hold ${NV_CUDNN_PACKAGE_NAME} \
    && apt-get clean \  
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# for PCL 
RUN apt-get update \
    && apt-get install -y lz4 \
        libusb-1.0.0-dev \
        libxt-dev \
    && apt-get clean \  
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# cmake
ADD cmake-3.19.6.tar.gz /usr/local/
ARG CMAKE=/usr/local/cmake-3.19.6/bin
ENV PATH=${CMAKE}:${NVIDIA}:${ORIGIN_PATH}
# RUN curl -fsSL https://apt.kitware.com/keys/kitware-archive-latest.asc | apt-key add - \
#     && add-apt-repository 'deb https://apt.kitware.com/ubuntu/ focal main' \
#     && apt-get update && apt install cmake

# set locale
RUN apt-get update && apt-get install locales \
    && apt-get clean \  
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*
RUN locale-gen en_US en_US.UTF-8
RUN update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8

# install ros2
RUN curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key \
        -o /usr/share/keyrings/ros-archive-keyring.gpg
RUN echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] \
        https://mirrors4.tuna.tsinghua.edu.cn/ros2/ubuntu focal main" \
        | tee /etc/apt/sources.list.d/ros2.list > /dev/null
RUN apt-get update && apt-get install -y ros-foxy-desktop --fix-missing \
    && apt-get clean \  
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# install gcc-7 and set default version
ENV HASCO_ENV=x86_64
RUN apt-get update && apt-get install -y gcc-7 g++-7 \
    && update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-9 1 \
        --slave /usr/bin/g++ g++ /usr/bin/g++-9 --slave /usr/bin/gcov gcov /usr/bin/gcov-9 \
    && update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-7 2 \
        --slave /usr/bin/g++ g++ /usr/bin/g++-7 --slave /usr/bin/gcov gcov /usr/bin/gcov-7 \
    && apt-get clean \  
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# install TensorRT
# ARG OS=ubuntu1804
# ARG TAG=cuda11.1-trt7.2.1.6-ga-20201007
ARG TENSORRT=TensorRT-7.2.1.6.Ubuntu-18.04.x86_64-gnu.cuda-11.1.cudnn8.0.tar.gz
ADD ${TENSORRT} /tmp/
RUN cp /tmp/TensorRT-7.2.1.6/include/* /usr/include/x86_64-linux-gnu \
    && cp /tmp/TensorRT-7.2.1.6/lib/lib* /usr/lib/x86_64-linux-gnu \
    && rm -rf /tmp/*
# COPY nv-tensorrt-repo-${OS}-${TAG}_1-1_amd64.deb /tmp/tensorrt
# WORKDIR /tmp/tensorrt

# RUN dpkg -i nv-tensorrt-repo-${OS}-${TAG}_1-1_amd64.deb
# RUN apt-key add /var/nv-tensorrt-repo-${TAG}/7fa2af80.pub
# RUN apt update && apt install -y tensorrt \
#     && apt-get clean \  
#     && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# WORKDIR /home

# opencv
# /usr/bin/ld: warning: libavcodec.so.57, needed by /hfr/3rdparty/opencv/x86_64/lib/libopencv_videoio.so, not found
# 在ubuntu18.04 dockers中编译出的libopencv_videoio.so 需要linklibavcodec.so.57，在当前ubuntu20.04中找不到
# 需要在当前ubuntu20.04 dockers中重新编译opencv，自动link到当前对应版本的linklibavcodec.so
# Add ubuntu version
ENV UBUNTU_VERSION=20.04

RUN apt-get update && apt-get install -y --no-install-recommends \
    iputils-ping && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

CMD [ "bash" ]