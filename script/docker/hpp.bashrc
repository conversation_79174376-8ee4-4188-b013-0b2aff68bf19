#!/usr/bin/env bash

HPP_ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd -P)"

HPP_IN_DOCKER=false 

if [ -f /.dockerenv ]; then
  HPP_IN_DOCKER=true
  HPP_ROOT_DIR="/hfr"
fi

export HPP_ROOT_DIR="${HPP_ROOT_DIR}"
export HPP_IN_DOCKER="${HPP_IN_DOCKER}"
export HPP_CACHE_DIR="${HPP_ROOT_DIR}/.cache"
export HPP_SYSROOT_DIR="/opt/hpp/sysroot"

export TAB="    " 

: ${VERBOSE:=yes}

BOLD='\033[1m'
RED='\033[0;31m'
BLUE='\033[0;34m'
GREEN='\033[32m'
WHITE='\033[34m'
YELLOW='\033[33m'
NO_COLOR='\033[0m'


function info() 
{
  (echo >&2 -e "[${WHITE}${BOLD}INFO${NO_COLOR}] $*")
}

function error() 
{
  (echo >&2 -e "[${RED}ERROR${NO_COLOR}] $*")
}

function warning() 
{
  (echo >&2 -e "${YELLOW}[WARNING] $*${NO_COLOR}")
}

function ok() 
{
  (echo >&2 -e "[${GREEN}${BOLD} OK ${NO_COLOR}] $*")
}

function print_delim() 
{
  echo "=============================================="
}

function success() 
{
  print_delim
  ok "$1"
  print_delim
}

function fail() 
{
  print_delim
  error "$1"
  print_delim
  exit 1
}


function get_now() 
{
  date +%s
}

function time_elapsed_s() 
{
  local start="${1:-$(get_now)}"
  local end="$(get_now)"
  echo "$end - $start" | bc -l
}


function file_ext() 
{
  local filename="$(basename $1)"
  local actual_ext="${filename##*.}"
  if [[ "${actual_ext}" == "${filename}" ]]; then
    actual_ext=""
  fi

  echo "${actual_ext}"
}


function bash_ext() 
{
  local actual_ext
  actual_ext="$(file_ext $1)"

  for ext in "sh" "bash" "bashrc"; do
    if [[ "${ext}" == "${actual_ext}" ]]; then
      return 0
    fi
  done

  return 1
}


function prettier_ext()
{
  local actual_ext
  actual_ext="$(file_ext $1)"

  for ext in "md" "json" "yml"; do
    if [[ "${ext}" == "${actual_ext}" ]]; then
      return 0
    fi
  done

  return 1
}


function optarg_check_for_opt() 
{
  local opt="$1"
  local optarg="$2"
  if [[ -z "${optarg}" || "${optarg}" =~ ^-.* ]]; then
      error "Missing parameter for ${opt}. Exiting..."
      exit 3
  fi
}