#!/usr/bin/env bash

DEV_CONTAINER="hpp_pack"

xhost +local:root 1>/dev/null 2>&1

#docker attach "${DEV_CONTAINER}"
#docker exec -u "${DOCKER_USER}" -e HISTFILE=/hfr/.dev_bash_hist -it "${DEV_CONTAINER}" /bin/bash
autorun_script=
if [ "${1##*.}" = "sh" ] ;
then
    autorun_script="$@"
    echo "ready to compile : ${autorun_script},in docker"
fi
docker exec -u root -e HISTFILE=/hfr/.dev_bash_hist -i "${DEV_CONTAINER}" /bin/bash ${autorun_script}
ret=$?
echo "docker return  : ${ret}"
xhost -local:root 1>/dev/null 2>&1
exit ${ret}