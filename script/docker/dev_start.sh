#!/usr/bin/env bash

CURR_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd -P)"
 
source "${CURR_DIR}/docker_base.sh"

CACHE_ROOT_DIR="${HPP_ROOT_DIR}/.cache"

DOCKER_REPO_LIST=(hpp_x86 hpp_tda4 a1000b-sdk-fad arm_rk3588s hpp_j3) 

DEV_CONTAINER="hpp_dev_${USER}"
DEV_INSIDE="in-dev-docker"

SUPPORTED_ARCHS=(x86_64 tda4 a1000 rk3588s j3)

TARGET_ARCH="$(uname -m)"

VERSION_X86_64="latest"
VERSION_TDA4="latest"
VERSION_A1000="2.3.0.4.1"
VERSION_RK3588S="v1.0"
VERSION_J3="latest"

USER_REPOSITORY_OPT=
USER_VERSION_OPT=

USE_LOCAL_IMAGE=1

USER_SPECIFIED_MAPS=
SHM_SIZE="2G"


function show_usage() 
{
    cat <<EOF
Usage: $0 [options] ...
OPTIONS:
    -h, --help             Display this help and exit.
    -l, --local            Use local docker image.
    -r, --repository       Specify docker image with REPOSITORY to start.
    -t, --tag              Specify docker image with TAG to start.
    --shm-size <bytes>     Size of /dev/shm . Passed directly to "docker run"
    stop                   Stop all running Apollo containers.
EOF
}


function parse_arguments() 
{
    local custom_repository=""
    local custom_version=""
    local shm_size=""

    while [ $# -gt 0 ]; do
        local opt="$1"
        shift
        case "${opt}" in
            -r | --repository)
                if [ -n "${custom_repository}" ]; then
                    warning "Multiple option ${opt} specified, only the last one will take effect."
                fi
                custom_repository="$1"
                shift
                optarg_check_for_opt "${opt}" "${custom_repository}"
                ;;

            -t | --tag)
                if [ -n "${custom_version}" ]; then
                    warning "Multiple option ${opt} specified, only the last one will take effect."
                fi
                custom_version="$1"
                shift
                optarg_check_for_opt "${opt}" "${custom_version}"
                ;;

            -h | --help)
                show_usage
                exit 1
                ;;

            -l | --local)
                USE_LOCAL_IMAGE=1
                shift
                ;;
            
            -a | --arch)
                TARGET_ARCH="$1"
                shift
                ;;

            -s | --shm-size)
                shm_size="$1"
                shift
                optarg_check_for_opt "${opt}" "${shm_size}"
                ;;

            stop)
                info "Now, stop all Hpp containers created by ${USER} ..."
                stop_all_hpp_containers "-f"
                exit 0
                ;;

            *)
                warning "Unknown option: ${opt}"
                exit 2
                ;;
        esac
    done 
    
    [[ -n "${custom_repository}" ]] && USER_REPOSITORY_OPT="${custom_repository}"
    [[ -n "${custom_version}" ]] && USER_VERSION_OPT="${custom_version}"
    [[ -n "${shm_size}" ]] && SHM_SIZE="${shm_size}"
}


function check_host_environment() 
{
    if [[ "${HOST_OS}" != "Linux" ]]; then
        warning "Running Hpp dev container on ${HOST_OS} is UNTESTED, exiting..."
        exit 1
    fi
}


function check_target_arch() 
{   
    local arch="${TARGET_ARCH}"
    for ent in "${SUPPORTED_ARCHS[@]}"; do
        if [[ "${ent}" == "${TARGET_ARCH}" ]]; then
            return 0
        fi
    done
    error "Unsupported target architecture: ${TARGET_ARCH}."
    exit 1
}


function determine_dev_image() 
{
    local repository="$1"
    local version="$2"

    if [[ -z "${repository}" ]]; then
        if [[ "${TARGET_ARCH}" == "x86_64" ]]; then
            repository="hpp_x86"
        elif [[ "${TARGET_ARCH}" == "tda4" ]]; then
            repository="hpp_tda4"
        elif [[ "${TARGET_ARCH}" == "a1000" ]]; then
            repository="a1000b-sdk-fad"
        elif [[ "${TARGET_ARCH}" == "rk3588s" ]]; then
            repository="arm_rk3588s"  
        elif [[ "${TARGET_ARCH}" == "j3" ]]; then
            repository="hpp_j3"      
        else
            error "Logic can't reach here! Please report this issue to manager."
            exit 3
        fi
    fi

    if [[ -z "${version}" ]]; then
        if [[ "${TARGET_ARCH}" == "x86_64" ]]; then
            version="${VERSION_X86_64}"
        elif [[ "${TARGET_ARCH}" == "tda4" ]]; then
            version="${VERSION_TDA4}"
        elif [[ "${TARGET_ARCH}" == "a1000" ]]; then
            version="${VERSION_A1000}"   
        elif [[ "${TARGET_ARCH}" == "rk3588s" ]]; then
            version="${VERSION_RK3588S}"  
        elif [[ "${TARGET_ARCH}" == "j3" ]]; then
            version="${VERSION_J3}"                           
        else
            error "Logic can't reach here! Please report this issue to manager."
            exit 3
        fi
    fi
    
    local flag=1
    for repo_name in "${DOCKER_REPO_LIST[@]}"; do
        if [[ "${repo_name}" == "${repository}" ]]; then
            flag=0
        fi
    done
    if [[ "${flag}" -gt 0 ]]; then
        error "Unsupported docker repository: ${repository}."
        exit 3
    fi
    
    local inside_name_prefix=
    if [[ "${repository}" == "hpp_tda4" ]]; then
        inside_name_prefix="tda4"
    elif [[ "${repository}" == "hpp_x86" ]]; then
        inside_name_prefix="x86"
    elif [[ "${repository}" == "a1000b-sdk-fad" ]]; then
        inside_name_prefix="a1000"   
    elif [[ "${repository}" == "arm_rk3588s" ]]; then
        inside_name_prefix="rk3588s"  
    elif [[ "${repository}" == "hpp_j3" ]]; then
        inside_name_prefix="j3"              
    else
        error "Unsupported docker repository: ${repository}."
        exit 3   
    fi

    #DEV_CONTAINER="${repository}_dev_${USER}"
    DEV_INSIDE="in-${inside_name_prefix}-dev-docker"
    DEV_IMAGE="${repository}:${version}"
}


function docker_pull()
{
    local img="$1"
    if [[ "${USE_LOCAL_IMAGE}" -gt 0 ]]; then
        if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "${img}"; then
            info "Local image ${img} found and will be used."
            return
        fi
        warning "Image ${img} not found locally although local mode enabled. Trying to pull from remote registry."
    fi
    info "Start pulling docker image ${img} ..."
    if ! docker pull "${img}"; then
        error "Failed to pull docker image : ${img}"
        exit 1
    fi
}


function setup_devices_and_mount_local_volumes() 
{
    local __retval="$1"

    #[ -d "${CACHE_ROOT_DIR}" ] || mkdir -p "${CACHE_ROOT_DIR}"

    local volumes="-v $HPP_ROOT_DIR:/hfr"

    volumes="$(tr -s " " <<<"${volumes}")"

    eval "${__retval}='${volumes}'"
}




function main() 
{
    check_host_environment
    check_target_arch
    parse_arguments "$@"

    determine_dev_image "${USER_REPOSITORY_OPT}" "${USER_VERSION_OPT}"

    if [[ "${USE_LOCAL_IMAGE}" -gt 0 ]]; then
        info "Start docker container based on local image : ${DEV_IMAGE}"
    fi

    if ! docker_pull "${DEV_IMAGE}"; then
        error "Failed to pull docker image ${DEV_IMAGE}"
        exit 1
    fi

    info "Remove existing Hpp Development container ..."
    remove_container_if_exists ${DEV_CONTAINER}

    local local_volumes=
    setup_devices_and_mount_local_volumes local_volumes

    info "Starting Docker container \"${DEV_CONTAINER}\" ..."

    local local_host="$(hostname)"
    local display="${DISPLAY:-:0}"
    local user="${USER}"
    local uid="$(id -u)"
    local group="$(id -g -n)"
    local gid="$(id -g)"

    set -x

    ${DOCKER_RUN_CMD} -itd \
        --privileged \
        --name "${DEV_CONTAINER}" \
        --gpus '"device=0"' \
        -e DISPLAY="${display}" \
        -e DOCKER_USER="${user}" \
        -e USER="${user}" \
        -e DOCKER_USER_ID="${uid}" \
        -e DOCKER_GRP="${group}" \
        -e DOCKER_GRP_ID="${gid}" \
        -e DOCKER_IMG="${DEV_IMAGE}" \
        -e NVIDIA_VISIBLE_DEVICES=all \
        -e NVIDIA_DRIVER_CAPABILITIES=compute,video,graphics,utility \
        ${local_volumes} \
        --net host \
        -w /hfr \
        --add-host "${DEV_INSIDE}:127.0.0.1" \
        --add-host "${local_host}:127.0.0.1" \
        --hostname "${DEV_INSIDE}" \
        --shm-size "${SHM_SIZE}" \
        --pid=host \
        "${DEV_IMAGE}" \
        /bin/bash

    if [ $? -ne 0 ]; then
        error "Failed to start docker container \"${DEV_CONTAINER}\" based on image: ${DEV_IMAGE}"
        exit 1
    fi

    set +x

    postrun_start_user "${DEV_CONTAINER}"

    ok "Congratulations! You have successfully finished setting up Hpp Dev Environment."
    ok "To login into the newly created ${DEV_CONTAINER} container, please run the following command:"
    ok "  bash script/docker/dev_into.sh"
    ok "If you have any questions, Please contact me."
    ok "Enjoy!"
}


main "$@"


