# Check for LD_LIBRARY_PATH being set, which can break SDK and generally is a bad practice
# http://tldp.org/HOWTO/Program-Library-HOWTO/shared-libraries.html#AEN80
# http://xahlee.info/UnixResource_dir/_/ldpath.html
# Only disable this check if you are absolutely know what you are doing!
if [ ! -z "$LD_LIBRARY_PATH" ]; then
    echo "Your environment is misconfigured, you probably need to 'unset LD_LIBRARY_PATH'"
    echo "but please check why this was set in the first place and that it's safe to unset."
    echo "The SDK will not operate correctly in most cases when LD_LIBRARY_PATH is set."
    echo "For more references see:"
    echo "  http://tldp.org/HOWTO/Program-Library-HOWTO/shared-libraries.html#AEN80"
    echo "  http://xahlee.info/UnixResource_dir/_/ldpath.html"
    return 1
fi
export SDKTARGETSYSROOT=/opt/bstos/linux-23/sysroots/aarch64-bst-linux
export PATH=/usr/local/src/jdk/jdk1.8.0_261/bin:/opt/bstos/linux-23/cmake-3.19.6/install/bin:/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin:/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/sbin:/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/bin:/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/sbin:/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/../x86_64-bstsdk-linux/bin:/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux:/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux-musl:$PATH
export PKG_CONFIG_SYSROOT_DIR=$SDKTARGETSYSROOT
export PKG_CONFIG_PATH=$SDKTARGETSYSROOT/usr/lib/pkgconfig:$SDKTARGETSYSROOT/usr/share/pkgconfig:$SDKTARGETSYSROOT/usr/lz4/usr/local/lib/pkgconfig
export CONFIG_SITE=/opt/bstos/linux-23/site-config-aarch64-bst-linux
export OECORE_NATIVE_SYSROOT="/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux"
export OECORE_TARGET_SYSROOT="$SDKTARGETSYSROOT"
export OECORE_ACLOCAL_OPTS="-I /opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/share/aclocal"
export OECORE_BASELIB="lib"
export OECORE_TARGET_ARCH="aarch64"
export OECORE_TARGET_OS="linux"
unset command_not_found_handle
alias opkg='/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/opkg -f /opt/bstos/linux-23/sysroots/aarch64-bst-linux/etc/opkg.conf -t /tmp/ -o /opt/bstos/linux-23/sysroots/aarch64-bst-linux' 
alias opkg_bst='/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/opkg -f /opt/bstos/linux-23/sysroots/aarch64-bst-linux/etc/opkg.conf -t /tmp/ -o /opt/bstos/linux-23/sysroots/aarch64-bst-linux' 
alias opkg-native='/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/opkg -f /opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/etc/opkg.conf -t /tmp/ ' 
export CC="aarch64-bst-linux-gcc  --sysroot=$SDKTARGETSYSROOT"
export CXX="aarch64-bst-linux-g++  --sysroot=$SDKTARGETSYSROOT"
export CPP="aarch64-bst-linux-gcc -E  --sysroot=$SDKTARGETSYSROOT"
export AS="aarch64-bst-linux-as "
export LD="aarch64-bst-linux-ld  --sysroot=$SDKTARGETSYSROOT"
export GDB=aarch64-bst-linux-gdb
export STRIP=aarch64-bst-linux-strip
export RANLIB=aarch64-bst-linux-ranlib
export OBJCOPY=aarch64-bst-linux-objcopy
export OBJDUMP=aarch64-bst-linux-objdump
export AR=aarch64-bst-linux-ar
export NM=aarch64-bst-linux-nm
export M4=m4
export TARGET_PREFIX=aarch64-bst-linux-
export CONFIGURE_FLAGS="--target=aarch64-bst-linux --host=aarch64-bst-linux --build=x86_64-linux --with-libtool-sysroot=$SDKTARGETSYSROOT"
export CFLAGS=" -O2 -pipe -g -feliminate-unused-debug-types "
export CXXFLAGS=" -O2 -pipe -g -feliminate-unused-debug-types "
export LDFLAGS="-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed"
export CPPFLAGS=""
export KCFLAGS="--sysroot=$SDKTARGETSYSROOT"
export OECORE_DISTRO_VERSION="linux-23"
export OECORE_SDK_VERSION="linux-23"
export ARCH=arm64
export CROSS_COMPILE=aarch64-bst-linux-
export HASCO_ENV="arm64_a1000b"
export JAVA_HOME=/usr/local/src/jdk/jdk1.8.0_261

# Append environment subscripts
if [ -d "$OECORE_TARGET_SYSROOT/environment-setup.d" ]; then
    for envfile in $OECORE_TARGET_SYSROOT/environment-setup.d/*.sh; do
	    . $envfile
    done
fi
if [ -d "$OECORE_NATIVE_SYSROOT/environment-setup.d" ]; then
    for envfile in $OECORE_NATIVE_SYSROOT/environment-setup.d/*.sh; do
	    . $envfile
    done
fi
