#!/bin/sh

CRT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd -P)"
ROOT_PATH=${CRT_PATH%%/script*}

echo "copy hasco images"
cp $ROOT_PATH/hfr_images/boot.img             /root/src_image/Images/kernel
cp $ROOT_PATH/hfr_images/core-image-bstos.img /root/src_image/Images/rootfs
cp $ROOT_PATH/hfr_images/hasco.img            /root/src_image/Images/hasco
cp $ROOT_PATH/hfr_images/user.img             /root/src_image/Images/user
cp $ROOT_PATH/hfr_images/cfg.img              /root/src_image/Images/cfg
cp $ROOT_PATH/hfr_images/cache.img            /root/src_image/Images/cache
cp $ROOT_PATH/hfr_images/logs.img             /root/src_image/Images/logs
cp $ROOT_PATH/hfr_images/calib.img            /root/src_image/Images/calib

echo "signature hasco images"
cd /root/BSTSignatureTool/bin/linux
./signaturetool-v3.0.1 -cs /root/20240826_pri.key -v "v0.0.0.1" -f /root/src_image/Images/kernel/boot.img
./signaturetool-v3.0.1 -cs /root/20240826_pri.key -v "v0.0.0.1" -f /root/src_image/Images/rootfs/core-image-bstos.img
./signaturetool-v3.0.1 -cs /root/20240826_pri.key -v "v0.0.0.1" -f /root/src_image/Images/hasco/hasco.img
./signaturetool-v3.0.1 -cs /root/20240826_pri.key -v "v0.0.0.1" -f /root/src_image/Images/user/user.img
./signaturetool-v3.0.1 -cs /root/20240826_pri.key -v "v0.0.0.1" -f /root/src_image/Images/cfg/cfg.img
./signaturetool-v3.0.1 -cs /root/20240826_pri.key -v "v0.0.0.1" -f /root/src_image/Images/cache/cache.img
./signaturetool-v3.0.1 -cs /root/20240826_pri.key -v "v0.0.0.1" -f /root/src_image/Images/logs/logs.img
./signaturetool-v3.0.1 -cs /root/20240826_pri.key -v "v0.0.0.1" -f /root/src_image/Images/calib/calib.img

cd $ROOT_PATH
chown -R root:root /root/src_image/Images/kernel/
chown -R root:root /root/src_image/Images/rootfs/
chown -R root:root /root/src_image/Images/hasco/
chown -R root:root /root/src_image/Images/user/
chown -R root:root /root/src_image/Images/cfg/
chown -R root:root /root/src_image/Images/cache/
chown -R root:root /root/src_image/Images/logs/
chown -R root:root /root/src_image/Images/calib/

echo "pack hasco images"
cd /root/src_image
DATE=`date +%Y%m%d_%H%M`
zip -r $ROOT_PATH/hfr_images/IMGS-HASCO-BST-HS-Linux-$DATE.zip .