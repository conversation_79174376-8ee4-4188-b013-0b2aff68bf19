#!/bin/sh

ROOT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && cd ../.. && pwd -P)"

T3RD_SRC_PATH=$1
TARGET_ARCH=$2
INSTALL_PATH="${ROOT_PATH}/$3"

TMP_SRC_PATH="${ROOT_PATH}/${T3RD_SRC_PATH}"

echo " start to complie :[${TMP_SRC_PATH}]"
echo " the install path :[${INSTALL_PATH}]"
cd ${TMP_SRC_PATH}
bash hcompile.sh -t ${TARGET_ARCH} -i ${INSTALL_PATH}
ret=$?
if [ ${ret} -ne 0 ]; then
	echo "Fault : failed to complie :[${TMP_SRC_PATH}], error code : ${ret}"
fi
echo " compile sucess, ret=${ret2}"
chmod 777 -R ${INSTALL_PATH}
exit ${ret2}
