#!/bin/sh

CRT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd -P)"
ROOT_PATH=${CRT_PATH%%/script*}

TARGET_ARCH=$1
PROJECT_NAME=$2
PROJECT_VERSION=$3
PROJECT_PARAM_EX=$4
COMPLIE_TIME=`date +%Y%m%d_%H%M`
export TARGET_ARCH
export HASCO_ENV=${TARGET_ARCH}

INSTALL_PATH="build/install/${TARGET_ARCH}"
TAR_FILE_NAME="${PROJECT_NAME}_${PROJECT_VERSION}.tar.gz"


echo " start to complie [${PROJECT_NAME}_${PROJECT_VERSION}_${COMPLIE_TIME}]:[${ROOT_PATH}]"
echo " the install path :[${INSTALL_PATH}]"

if [ "$TARGET_ARCH" == "arm64_a1000b" ]; then
	source ${ROOT_PATH}/script/docker/environment-setup-aarch64-bst-linux
fi

function aftermath_handling_recovery ()
{
    cd ${ROOT_PATH}
    chmod -R 777 ${ROOT_PATH}/build/
}

cd ${ROOT_PATH}
if [ ! $PROJECT_PARAM_EX ]; then
	cmake -Bbuild -Sscript
else
	cmake -Bbuild -Sscript -D${PROJECT_PARAM_EX}
fi

ret=$?
if [ ${ret} -ne 0 ]; then
	echo "Fault : failed to build :[${ROOT_PATH}], error code : ${ret}"
	aftermath_handling_recovery
	exit ${ret}
fi

cmake --build build --target install -j 4
ret=$?
if [ ${ret} -ne 0 ]; then
	echo "Fault : failed to compile :[${ROOT_PATH}], error code : ${ret}"
	aftermath_handling_recovery
	exit ${ret}
fi

cd ${ROOT_PATH}
echo "tar ${TAR_FILE_NAME} ..."
tar -czf ${TAR_FILE_NAME} ${INSTALL_PATH}/*
if [ ${ret} -ne 0 ]; then
	echo "Fault : failed to tar :[${PROJECT_NAME}], error code : ${ret}"
else
	echo "Success to compile : [${PROJECT_NAME}], output : ${TAR_FILE_NAME}"
fi
# CRC_VALUE=`crc32 ${TAR_FILE_NAME}`
NEW_TAR_FILE_NAME="${PROJECT_NAME}_${PROJECT_VERSION}_${COMPLIE_TIME}.tar.gz"
echo "=== new tar: ${NEW_TAR_FILE_NAME}"
mv ${TAR_FILE_NAME} ${ROOT_PATH}/${NEW_TAR_FILE_NAME}
cd ${ROOT_PATH}
chmod -R 777 ${ROOT_PATH}/build/
chmod 777 ${NEW_TAR_FILE_NAME}

# make images
echo "Make hasco_app images from install_path"
if [ ! -d ${INSTALL_PATH}/../hfr_images ]; then
  mkdir -p ${INSTALL_PATH}/../hfr_images
fi

make_ext4fs -l 3G -s ${INSTALL_PATH}/../hfr_images/hasco.img  ${INSTALL_PATH}
chmod 644 ${INSTALL_PATH}/../hfr_images/*.img

exit ${ret}
