#!/bin/bash
CURRENT_DIR=$(cd $(dirname $0); pwd)
export LD_LIBRARY_PATH=$CURRENT_DIR/lib:$LD_LIBRARY_PATH
export VSOMEIP_CONFIGURATION=$CURRENT_DIR/vsomeip.json

#run iceoryx
./iox-roudi -c roudi_config.toml&

sleep 1
cd $CURRENT_DIR/modules/hlog_service
./run.sh
sleep 1
cd $CURRENT_DIR/modules/config_service
./run.sh
sleep 1
#load modules
cd $CURRENT_DIR/modules/lidar_service
./run.sh
cd $CURRENT_DIR/modules/lidar_service_tool
./run.sh
cd $CURRENT_DIR/modules/data_transfer
./run.sh
sleep 1
cd $CURRENT_DIR/modules/camera_service
./run.sh
sleep 1
cd $CURRENT_DIR/modules/around_view_monitor
./run.sh
sleep 1
cd $CURRENT_DIR/modules/vehicle_service
./run.sh
cd $CURRENT_DIR/modules/dead_reckoning
./run.sh
sleep 1
cd $CURRENT_DIR/modules/pointscloud_compensation
./run.sh
cd $CURRENT_DIR/modules/line_slot_perception
./run.sh
cd $CURRENT_DIR/modules/space_slot_perception
./run.sh
cd $CURRENT_DIR/modules/lidar_obstacle_perception
./run.sh
cd $CURRENT_DIR/modules/fusion_obstacle_perception
./run.sh
cd $CURRENT_DIR/modules/mechanical_slot_perception
./run.sh
cd $CURRENT_DIR/modules/slot_manager
./run.sh
cd $CURRENT_DIR/modules/planning
./run.sh
cd $CURRENT_DIR/modules/hpp_function
./run.sh
cd $CURRENT_DIR/modules/hpp_hmi
./run.sh
cd $CURRENT_DIR/modules/control
./run.sh


#ip link set can0 up type can bitrate 500000 sample-point 0.750 dbitrate 2000000 dsample-point 0.750 fd on
#ip link set can1 up type can bitrate 500000 sample-point 0.750 dbitrate 2000000 dsample-point 0.750 fd on
