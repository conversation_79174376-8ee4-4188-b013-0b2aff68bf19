#!/bin/bash
CURRENT_DIR=$(cd $(dirname $0); pwd)
export LD_LIBRARY_PATH=$CURRENT_DIR/lib:$LD_LIBRARY_PATH
export VSOMEIP_CONFIGURATION=$CURRENT_DIR/vsomeip.json

#run iceoryx
./iox-roudi -c roudi_config.toml&

sleep 1
cd $CURRENT_DIR/modules/hlog_service
./run.sh
sleep 1
cd $CURRENT_DIR/modules/config_service
./run.sh
sleep 1
#load modules
cd $CURRENT_DIR/modules/vehicle_service_autosweeper
./run.sh
sleep 1
cd $CURRENT_DIR/modules/lidar_service_rs16
./run.sh
sleep 1
cd $CURRENT_DIR/modules/lidar_obstacle_perception
./run.sh
sleep 1
cd $CURRENT_DIR/modules/fusion_obstacle_perception
./run.sh
sleep 1
cd $CURRENT_DIR/modules/map
./run.sh
sleep 1
cd $CURRENT_DIR/modules/fusion_localization
./run.sh
sleep 3
cd $CURRENT_DIR/modules/hpp_function
./run.sh
cd $CURRENT_DIR/modules/hpp_hmi
./run.sh
cd $CURRENT_DIR/modules/planning
./run.sh
cd $CURRENT_DIR/modules/control
./run.sh
cd $CURRENT_DIR/modules/key_control
./run.sh


#ip link set can0 up type can bitrate 500000 sample-point 0.750 dbitrate 2000000 dsample-point 0.750 fd on
#ip link set can1 up type can bitrate 500000 sample-point 0.750 dbitrate 2000000 dsample-point 0.750 fd on
