#!/bin/bash

FILE_DOCKERINFO="/hfr/.dockerInfo"
dockerID=""
arrPID=()
if [ -f "${FILE_DOCKERINFO}" ];
then    
    dockerID=$(cat ${FILE_DOCKERINFO})
fi  

ARCH=$(uname -m)
echo "$ARCH"

apps=(em_service config_service data_transfer camera_service time_service  hspi_sync around_view_monitor vehicle_service dead_reckoning line_slot_perception phc2sys ptp4l
  lidar_service mechanical_slot_perception slot_manager map fusion_localization planning hpp_function hpp_hmi control apa_ui fusion_obstacle_perception lidar_obstacle_perception
  space_slot_perception traflight_perception_postprocess hlog_service  dlt  iox-roudi)

# Function to safely stop an application by its name
stopApp()
{ 
  killedpid=$(ps -ef | grep -w "$1" | grep -v 'grep' | grep -v 'tail' | awk '{print $2}')

  if [ -n "$killedpid" ];
  then    
    echo "Stopping application: $1 (PID=$killedpid)"
    kill -2 $killedpid 2>/dev/null
    arrPID[${#arrPID[*]}]=$killedpid
  else
    echo "No active process found for application: $1"
  fi
}

# Function to stop an application by session ID and name
stopAppBySessionID()
{ 
  killedpid=$(ps -ef | grep -w "$1" | grep -v 'grep' | grep -v 'tail' | grep "$2" | awk '{print $2}')

  if [ -n "$killedpid" ];
  then    
    echo "Stopping application: $2 in session $1 (PID=$killedpid)"
    kill -2 $killedpid 2>/dev/null
    arrPID[${#arrPID[*]}]=$killedpid
  else
    echo "No active process found for application: $2 in session $1"
  fi
}

# Stop all apps by name
stopAppsbyAppName()
{
  for app in "${apps[@]}"
  do    
    stopApp "$app"
  done
}

# Stop all apps in a specific session
stopAppsbyAppNameAndSessionID()
{
  echo "Stopping applications in session PID: $1"  
  for app in "${apps[@]}"
  do
    stopAppBySessionID "$1" "$app"
  done
}

# Stop all apps in the current Docker container
stopAppsInCurrentDocker()
{
   dockerPid=$(ps -ef | grep -w "$dockerID" | grep -v 'grep' | grep -v 'tail' | awk '{print $2}')
   if [ -n "$dockerPid" ]; then
     bashListPid=$(ps -ef | grep -w "$dockerPid" | grep -v 'grep' | grep -v 'tail' | awk '{print $2}') 
     bashListArray=(${bashListPid/\n/})
     for bashPid in "${bashListArray[@]}"
     do
        stopAppsbyAppNameAndSessionID "$bashPid"
     done
   else
     echo "No Docker container processes found with ID: $dockerID"
   fi
}

# Stop the heart service for non-x86 architectures
if [[ $ARCH != *"x86"* ]]; then
    echo "Stopping heart service"
    CURRENT_DIR=$(cd $(dirname $0); pwd)
    export LD_LIBRARY_PATH=$CURRENT_DIR/lib:$CURRENT_DIR/modules/3j3/sysd_lib:$LD_LIBRARY_PATH
    cd $CURRENT_DIR/modules/heartControlStop
    ./heartControlStop
fi

# Stop applications based on Docker or system context
if [ -n "$dockerID" ];
then  
   stopAppsInCurrentDocker
else
   stopAppsbyAppName
fi  

# Force kill remaining PIDs if they still exist
echo "Verifying and force-killing remaining processes"
sleep 3
for pid in "${arrPID[@]}"; do   
  if ps -p "$pid" > /dev/null 2>&1; then
    echo "Force-killing process: $pid"
    kill -9 "$pid" 2>/dev/null
  else
    echo "Process $pid no longer exists"
  fi    
done
