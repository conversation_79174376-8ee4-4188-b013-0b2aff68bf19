cmake_minimum_required(VERSION 3.10)

project(hfr VERSION 1.0.0 LANGUAGES CXX)

if(NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE Release)
endif()

option(BUILD_SHARED_LIBS "Build hfr as shared libraries" ON)
option(BUILD_STRICT "Build is performed with '-Werror'" OFF)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

set(CMAKE_INSTALL_DEFAULT_DIRECTORY_PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_WRITE GROUP_EXECUTE WORLD_READ WORLD_WRITE WORLD_EXECUTE)
set(CMAKE_INSTALL_MODE OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_WRITE GROUP_EXECUTE WORLD_READ WORLD_WRITE WORLD_EXECUTE)  

set(CMAKE_LIB_INSTALL_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../lib")
get_filename_component(CMAKE_LIB_INSTALL_PATH "${CMAKE_LIB_INSTALL_PATH}" ABSOLUTE)
set(CMAKE_INTERFACE_INSTALL_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../interface")
get_filename_component(CMAKE_INTERFACE_INSTALL_PATH "${CMAKE_INTERFACE_INSTALL_PATH}" ABSOLUTE)

if(NOT HFR_ARCH)
  set(HFR_ARCH  $ENV{HASCO_ENV})
endif()  

add_compile_definitions(HASCO_HFR_ARCH="${HFR_ARCH}")

set(HPP_INSTALL_PREFIX "${CMAKE_BINARY_DIR}")
set(HPP_TARGET_INSTALL_PATH "${HPP_INSTALL_PREFIX}/install/${HFR_ARCH}")
set(HPP_LIB_INSTALL_PATH "${HPP_TARGET_INSTALL_PATH}/lib")
set(HPP_MODULE_INSTALL_PATH "${HPP_TARGET_INSTALL_PATH}/modules")
set(HPP_RESOURCE_INSTALL_PATH "${HPP_TARGET_INSTALL_PATH}/resource")
set(HPP_DATA_INSTALL_PATH "${HPP_TARGET_INSTALL_PATH}/data")

include("${CMAKE_CURRENT_SOURCE_DIR}/cmake/hfrPackageHelper.cmake")
include("${CMAKE_CURRENT_SOURCE_DIR}/cmake/hfrPlatformSettings.cmake")
include("${CMAKE_CURRENT_SOURCE_DIR}/cmake/hfrSearchModule.cmake")

hfr_glob_cmakes(${CMAKE_CURRENT_SOURCE_DIR}/../lib)
hfr_glob_libraries(${CMAKE_CURRENT_SOURCE_DIR}/../3rdparty ${CMAKE_CURRENT_SOURCE_DIR}/../lib)

include("${CMAKE_CURRENT_SOURCE_DIR}/cmake/hfrLibraryGroup.cmake")
setHbiVersion()

#include_directories(${BOOST_INCLUDE_DIR})
#include_directories(${YAML_INCLUDE_DIR} ${HLOG_INCLUDE_DIR} ${COMPONENTS_AND_DATA_CENTER_INCLUDE_DIR} )
#link_directories( ${YAML_LIBRARY_DIR} ${HLOG_LIBRARY_DIR} ${DLT_LIBRARY_DIR} ${COMPONENTS_AND_DATA_CENTER_LIBRARY_DIR})
#link_libraries(components_and_data_center yaml-cpp hlog)

hfr_glob_modules(${CMAKE_CURRENT_SOURCE_DIR}/../modules)

hfr_export_preparation( )
hfr_export_all_libraries(${CMAKE_CURRENT_SOURCE_DIR}/../3rdparty ${CMAKE_CURRENT_SOURCE_DIR}/../lib)

#install 
install(PROGRAMS run.sh DESTINATION  ${HPP_TARGET_INSTALL_PATH})
install(PROGRAMS simone_run.sh DESTINATION  ${HPP_TARGET_INSTALL_PATH})
install(PROGRAMS autoRun.sh DESTINATION  ${HPP_TARGET_INSTALL_PATH} )
install(PROGRAMS stop.sh DESTINATION  ${HPP_TARGET_INSTALL_PATH} )
install(PROGRAMS run_fctb.sh DESTINATION  ${HPP_TARGET_INSTALL_PATH} )
install(PROGRAMS stop_fctb.sh DESTINATION  ${HPP_TARGET_INSTALL_PATH} )
install(PROGRAMS run_em.sh DESTINATION  ${HPP_TARGET_INSTALL_PATH})
install(PROGRAMS stop_em.sh DESTINATION  ${HPP_TARGET_INSTALL_PATH} )
install(PROGRAMS vsomeip.json DESTINATION  ${HPP_TARGET_INSTALL_PATH} )
install(PROGRAMS cpuSet.sh DESTINATION  ${HPP_TARGET_INSTALL_PATH} )

install(PROGRAMS  ${COMPONENTS_AND_DATA_CENTER_DIR}/tool/cmd DESTINATION  ${HPP_TARGET_INSTALL_PATH} )
install(PROGRAMS  ${ICEORYX_DIR}/bin/iox-roudi DESTINATION  ${HPP_TARGET_INSTALL_PATH} )
install(PROGRAMS  ${ICEORYX_DIR}/bin/roudi_config.toml DESTINATION  ${HPP_TARGET_INSTALL_PATH} )
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../resource/ DESTINATION ${HPP_RESOURCE_INSTALL_PATH})

#install dlt
install(PROGRAMS ${DLT_DIR}/bin/dlt-daemon  DESTINATION ${HPP_MODULE_INSTALL_PATH}/hlog_service/bin )
install(PROGRAMS ${DLT_DIR}/etc/dlt.conf  DESTINATION  ${HPP_TARGET_INSTALL_PATH} )
install(PROGRAMS ${DLT_DIR}/bin/dlt-receive  DESTINATION  ${HPP_MODULE_INSTALL_PATH}/hlog_service/bin )

message("")
message("Project Summary")
message("       PATH Env-variables ")
message("          CMAKE_SOURCE_DIR.....................: " ${CMAKE_SOURCE_DIR})
message("          CMAKE_BINARY_DIR.....................: " ${CMAKE_BINARY_DIR})
message("          CMAKE_CURRENT_SOURCE_DIR.....................: " ${CMAKE_CURRENT_SOURCE_DIR})
message("          CMAKE_CURRENT_BINARY_DIR.....................: " ${CMAKE_CURRENT_BINARY_DIR})
message("          HPP_INSTALL_PREFIX.....................: " ${HPP_INSTALL_PREFIX})
message("          HPP_LIB_INSTALL_PATH.....................: " ${HPP_LIB_INSTALL_PATH})
message("          HPP_MODULE_INSTALL_PATH.....................: " ${HPP_MODULE_INSTALL_PATH})
message("          HPP_RESOURCE_INSTALL_PATH.....................: " ${HPP_RESOURCE_INSTALL_PATH})
message("          HPP_DATA_INSTALL_PATH..................: " ${HPP_DATA_INSTALL_PATH})
message("          CMAKE_LIB_INSTALL_PATH........................: " ${CMAKE_LIB_INSTALL_PATH})
message("          CMAKE_INTERFACE_INSTALL_PATH..................: " ${CMAKE_INTERFACE_INSTALL_PATH})
message("")
message("       CMake Options")
message("          CMAKE_BUILD_TYPE.....................: " ${CMAKE_BUILD_TYPE})
message("          CMAKE_TOOLCHAIN_FILE.................: " ${CMAKE_TOOLCHAIN_FILE})
message("          CMAKE_EXPORT_COMPILE_COMMANDS........: " ${CMAKE_EXPORT_COMPILE_COMMANDS})
message("")
message("       Bool Options")
message("          BUILD_SHARED_LIBS....................: " ${BUILD_SHARED_LIBS})
message("          BUILD_STRICT.........................: " ${BUILD_STRICT})
message("")
message("       Build Properties")
message("          platform..................: " ${HFR_ARCH})
message("          project name..............: " ${CMAKE_PROJECT_NAME})
message("          c++ standard..............: " ${HFR_CXX_STANDARD})
message("          c++ compiler..............: " ${CMAKE_CXX_COMPILER})
message("          c++ warning flags.........: " ${HFR_CXX_WARNINGS})
message("          cmake.....................: " ${CMAKE_VERSION})
message("")
