#!/bin/bash
CURRENT_DIR=$(cd $(dirname $0); pwd)
export LD_LIBRARY_PATH=$CURRENT_DIR/lib:$LD_LIBRARY_PATH

#run iceoryx
./iox-roudi -c roudi_config.toml&
sleep 1
cd $CURRENT_DIR/modules/config_service
./run.sh
sleep 1
#load modules
cd $CURRENT_DIR/modules/data_transfer
./run.sh
sleep 1
cd $CURRENT_DIR/modules/vehicle_service
./run.sh
cd $CURRENT_DIR/modules/dead_reckoning
./run.sh
cd $CURRENT_DIR/modules/lidar_service
./run.sh
sleep 1
cd $CURRENT_DIR/modules/lidar_obstacle_perception
./run.sh
sleep 1
cd $CURRENT_DIR/modules/fusion_obstacle_perception
./run.sh
cd $CURRENT_DIR/modules/CrossTrafficAlertAndBrake
./run.sh
cd $CURRENT_DIR/modules/AutonomousEmergencyBraking
./run.sh
cd $CURRENT_DIR/modules/apa_ui
./run.sh

