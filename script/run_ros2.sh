#!/bin/bash
CURRENT_DIR=$(cd $(dirname $0); pwd)
export LD_LIBRARY_PATH=$CURRENT_DIR/lib:$LD_LIBRARY_PATH
export VSOMEIP_CONFIGURATION=$CURRENT_DIR/vsomeip.json

source /opt/ros/foxy/setup.bash
source $CURRENT_DIR/lib/hfr_msgs/setup.bash

#run iceoryx
./iox-roudi -c roudi_config.toml&

sleep 1
cd $CURRENT_DIR/modules/hlog_service
./run.sh
sleep 1
cd $CURRENT_DIR/modules/config_service
./run.sh
sleep 1
#load modules
cd $CURRENT_DIR/modules/lidar_service_ouster
./run.sh

cd $CURRENT_DIR/modules/ros2dataConversion
./run.sh
#ip link set can0 up type can bitrate 500000 sample-point 0.750 dbitrate 2000000 dsample-point 0.750 fd on
#ip link set can1 up type can bitrate 500000 sample-point 0.750 dbitrate 2000000 dsample-point 0.750 fd on
