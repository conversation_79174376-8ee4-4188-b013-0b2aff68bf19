
## 启动并进入 HPP Docker容器

以下命令假定在项目根目录下运行:

1. 创建容器

    ```bash
    bash script/docker/dev_start.sh 
    # 默认会拉取最新版的 hpp_x86:latest 镜像，如果需要拉取指定版本的 hpp_x86 镜像，请指定选项参数，如:
    # bash script/docker/dev_start.sh -r hpp_x86 -t v1.2
    # 如果需要拉取最新版本的 TDA4镜像，请指定选项参数，如:
    # bash script/docker/dev_start.sh -r hpp_tda4 -t latest
    #拉取黑芝麻A1000 的镜像，请指定参数：
    # bash script/docker/dev_start.sh -r a1000b-sdk-fad -t 23.1  -s 8G
    #或者直接使用创建进入黑芝麻环境的脚本：
    # bash script/docker/dev_a1000.sh
    ```

2. 进入容器

    ```bash
    bash script/docker/dev_into.sh
    ```
 
## 编译 HPP 项目

使用 CMake 构建是首选方式，在 Docker 中运行以下命令以构建 hfr 项目：

1. 生成必要的构建文件

    ```bash
    cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=debug
    ```

2. 编译源代码

    ```bash
    cmake --build build
    # 如果要安装，则执行 cmake --build build --target install。
    ```

## 构建 HPP A1000镜像

```bash
cd /hfr
bash ./script/docker/auto_compile_project.sh arm64_a1000b hpp version
```
镜像生成路径：/hfr/build/install/hfr_images/hasco.img    