#!/bin/bash

setCpu()
{
   pids=$(pgrep -f $1)  
  
   # ����Ƿ��ҵ����κν���  
   if [ -z "$pids" ]; then  
    echo "No $1 processes found."  
   else  
        # ��ÿ�� PID ִ�а�˲���  
        for pid in $pids; do  
          echo "$1  $pid will bind cpu: $2"  
          taskset -pc $2 $pid             
       done  
    fi
}



setCpu iox-roudi               0-5
set<PERSON>pu hlog_service            0-5
setCpu config_service          0-5
setCpu dlt-daemon              0-5
setCpu dlt-receive             0-5
setCpu data_transfer           6,7

