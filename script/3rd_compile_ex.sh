#!/usr/bin/env bash

TARGET_ARCH="$(uname -m)"

CRT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd -P)"
ROOT_PATH=${CRT_PATH%%/script*}

DEV_BRANCH_NAME="dev"

T3RD_SRC_RELATIVE_PATH=""
T3RD_INSTALL_RELATIVE_PATH=""

PROJECT_INFO_FILE_NAME="hproject_info.makefile"

T3RD_LATEST_COMMIT_ID=""


function show_usage() 
{
    cat <<EOF
Usage: $0 -s AAA -i BBB [options] ...
MUST :
    -s, --source           Specify the source relative path to compile. must been under the hfr directory
    -i, --install          Specify the relative path to install, must been under the hfr directory,sibling directory with source path
OPTIONS :
    -h, --hfr              Specify the hfr of path. default is ./..
    -t, --target           Specify the name of project. default is x86_64
    -d, --devbranch        Specify the name of develop branch, default is dev
    -h, --help             Display this help and exit.
Example :
     $0 -s tmp/nana -i tmp/install -t x86_64 -d dev
EOF
}

function parse_arguments() 
{
    while [ $# -gt 0 ]; do
        local opt="$1"
        shift
        case "${opt}" in
            -s | --source)
                if [ -n "${T3RD_SRC_RELATIVE_PATH}" ]; then
                    echo "Multiple option ${opt} specified, only the last one will take effect."
                fi
                T3RD_SRC_RELATIVE_PATH="$1"
                shift
                ;;

            -i | --install)
                if [ -n "${T3RD_INSTALL_RELATIVE_PATH}" ]; then
                    echo "Multiple option ${opt} specified, only the last one will take effect."
                fi
                #check path 
                if [ "`ls -A ${1}`" != "" ]; then
                    echo "Fault : the param is error, the [install]  is not empty : ${1}"
                    exit 1
                fi
                T3RD_INSTALL_RELATIVE_PATH="$1"
                shift
                ;;

            -h | --hfr)
                if [ -n "${ROOT_PATH}" ]; then
                    echo "Multiple option ${opt} specified, only the last one will take effect."
                fi
                ROOT_PATH="$1"
                shift
                ;;

            -t | --target)
                if [ -n "${TARGET_ARCH}" ]; then
                    echo "Multiple option ${opt} specified, only the last one will take effect."
                fi
                TARGET_ARCH="$1"
                shift
                ;;
            -d | --devbranch)
                if [ -n "${DEV_BRANCH_NAME}" ]; then
                    echo "Multiple option ${opt} specified, only the last one will take effect."
                fi
                DEV_BRANCH_NAME="$1"
                shift
                ;;
            -h | --help)
                show_usage
                exit 0
                ;;
            *)
                echo "Unknown option: ${opt}"
                exit 2
                ;;
        esac
    done
}

function check_arguments() 
{
    if [ ! -d ${ROOT_PATH} ]; then
        echo "Fault : error param, the hfr path is not exist, path : ${ROOT_PATH}"
        return 1
    fi

    if [ ! -d ${ROOT_PATH}/script/docker ]; then
        echo "Fault : error param, the hfr path is invalid,not exist [script/docker], path : ${ROOT_PATH}"
        return 1
    fi

    if [ ${#T3RD_SRC_RELATIVE_PATH} -le 1 ]; then
        echo "Fault : error param, the target is invalid, T3RD_SRC_RELATIVE_PATH : ${T3RD_SRC_RELATIVE_PATH}"
        return 1
    fi

    if [ ${#T3RD_INSTALL_RELATIVE_PATH} -le 1 ]; then
        echo "Fault : error param, the target is invalid, T3RD_INSTALL_RELATIVE_PATH : ${T3RD_INSTALL_RELATIVE_PATH}"
        return 1
    fi

    if [ ! -d ${ROOT_PATH}/${T3RD_SRC_RELATIVE_PATH} ]; then
        echo "Fault : error param, the source path is not exist, path : ${T3RD_SRC_RELATIVE_PATH}"
        return 1
    fi

    if [ ! -d ${ROOT_PATH}/${T3RD_INSTALL_RELATIVE_PATH} ]; then
        echo "Fault : error param, the install path is not exist, path : ${T3RD_INSTALL_RELATIVE_PATH}"
        return 1
    fi

    if [ ! -d ${ROOT_PATH}/${T3RD_INSTALL_RELATIVE_PATH} ]; then
        echo "Fault : error param, the install path is not exist, path : ${T3RD_INSTALL_RELATIVE_PATH}"
        return 1
    fi

    if [ ${#TARGET_ARCH} -lt 2 ]; then
        echo "Fault : error param, the target is invalid, TARGET_ARCH : ${TARGET_ARCH}"
        return 1
    fi

    if [ ${#DEV_BRANCH_NAME} -lt 2 ]; then
        echo "Fault : error param, the target is invalid, DEV_BRANCH_NAME : ${DEV_BRANCH_NAME}"
        return 1
    fi

    return 0
}

function checkout_branch()
{
    git branch | grep ${1}
    ret=$?
    if [ ${ret} -eq 0 ]; then
        git checkout ${1}
    else
        git checkout -b ${1} remotes/origin/${1}
    fi
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to checkout ${1}, in : ${ROOT_PATH}/${T3RD_SRC_RELATIVE_PATH},error code : ${ret}"
    fi
    return ${ret}
}

function check_if_need_compile ()
{
    local t3rd_code_path=${ROOT_PATH}/${T3RD_SRC_RELATIVE_PATH}
    cd ${t3rd_code_path}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to into ${t3rd_code_path}"
        return ${ret}
    fi

    checkout_branch release
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to checkout release, in : ${t3rd_code_path},error code : ${ret}"
        return ${ret}
    fi

    local dev_commit_id_in_rel=""
    local version_line=`grep  "PROJECT_COMMIT_ID" ${PROJECT_INFO_FILE_NAME}`
    if [ $? = 0 ]; then
        dev_commit_id_in_rel=${version_line##*=}
    fi

    checkout_branch ${DEV_BRANCH_NAME}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to checkout ${DEV_BRANCH_NAME}, in : ${t3rd_code_path},error code : ${ret}"
        return ${ret}
    fi

    git reset --hard remotes/origin/${DEV_BRANCH_NAME}

    T3RD_LATEST_COMMIT_ID=""
    T3RD_LATEST_COMMIT_ID=`git rev-parse HEAD`
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to get dev latest commit id, in : ${t3rd_code_path},error code : ${ret}"
        return ${ret}
    fi
    if [[ -z  ${T3RD_LATEST_COMMIT_ID} ]] ; then
        echo "Fault : failed to get dev latest commit id, get id is ${T3RD_LATEST_COMMIT_ID}"
        ret=1
    fi
    
    ret=1
    if [ ${dev_commit_id_in_rel} = "\"${T3RD_LATEST_COMMIT_ID}\"" ]; then
        echo "Success : commit id is same, not need to complile & commit."
        ret=0
    else
        echo "Info : commit id not is same, need to complie & commit"
    fi
    echo "old=${dev_commit_id_in_rel},new=${T3RD_LATEST_COMMIT_ID}"
    return ${ret}
}


function 3rd_complie_source ()
{
    cd ${ROOT_PATH}

    bash script/docker/dev_start_ci.sh -r hpp_x86 -t v1.2
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to compile  [ ${T3RD_NAME} ], can't to create docker , error code : ${ret}"
        exit ${ret}
    fi

    # start to compile in docker
    bash script/docker/dev_into_ci.sh /hfr/script/docker/auto_compile_3rd.sh ${T3RD_SRC_RELATIVE_PATH} ${TARGET_ARCH} ${T3RD_INSTALL_RELATIVE_PATH}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to compile  [ ${T3RD_SRC_RELATIVE_PATH} ] in docker, error code : ${ret}"
    fi
    return ${ret}
}

function 3rd_checkout_rel_branch ()
{
    local t3rd_code_path=${ROOT_PATH}/${T3RD_SRC_RELATIVE_PATH}
    cd ${t3rd_code_path}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to checkout release, the dir is not exist : ${t3rd_code_path}"
        return ${ret}
    fi

    git reset --hard remotes/origin/${DEV_BRANCH_NAME}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to reset [remotes/origin/${DEV_BRANCH_NAME}] in : ${t3rd_code_path},error code : ${ret}"
        return ${ret}
    fi
    checkout_branch release
    return $?
}

function overwrite_3rd_from_install ()
{
    local t3rd_code_path=${ROOT_PATH}/${T3RD_SRC_RELATIVE_PATH}
    cd ${t3rd_code_path}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to overwrite, dir is not exist : ${t3rd_code_path}"
        return $ret
    fi

    git status | grep -q "On branch release"
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to overwrite, not in release branch"
        return $ret
    fi

    rm -rf ${t3rd_code_path}/include
    rm -rf ${t3rd_code_path}/lib/${TARGET_ARCH}/*

    local install_path=${ROOT_PATH}/${T3RD_INSTALL_RELATIVE_PATH}
    cp -rf ${install_path}/include ${t3rd_code_path}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to copy [${install_path}/include] to ${t3rd_code_path}"
        return $ret
    fi

    cp -rf ${install_path}/lib/* ${t3rd_code_path}/lib/${TARGET_ARCH}/
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to copy [${install_path}/lib] to ${t3rd_code_path}/lib/${TARGET_ARCH}"
        return $ret
    fi
    echo "export PROJECT_COMMIT_ID=\"${T3RD_LATEST_COMMIT_ID}\"" > ${t3rd_code_path}/${PROJECT_INFO_FILE_NAME}
    return $?
}

function commit_3rd_2_rel ()
{
    local t3rd_code_path=${ROOT_PATH}/${T3RD_SRC_RELATIVE_PATH}
    cd ${t3rd_code_path}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : the directory of  ${t3rd_code_path} is not exist"
        return $ret
    fi

    git status . | grep -q "nothing to commit"
    ret=$?
    if [ ${ret} -eq 0 ]; then
        echo "Info : nothing to commit in ${t3rd_code_path}"
        return 0
    fi

    CRT_TIME=`date +%Y%m%d_%H%M%s`
    git add ./include
    ret=ret+$?
    git add ./lib
    ret=ret+$?
    git add ./${PROJECT_INFO_FILE_NAME}
    ret=ret+$?
    echo "commit by script for test at : ${CRT_TIME}"
    git commit -m "commit by script for test at : ${CRT_TIME}"
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to commit ${t3rd_code_path},ret = ${ret}"
        return $ret
    fi

    git push
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : failed to push ${t3rd_code_path},ret = ${ret}"
    fi
    return $ret
}

function aftermath_handling_recovery()
{
    local t3rd_install_path=${ROOT_PATH}/${T3RD_INSTALL_RELATIVE_PATH}
    rm -rf ${t3rd_install_path}/*

    local t3rd_code_path=${ROOT_PATH}/${T3RD_SRC_RELATIVE_PATH}
    cd ${t3rd_code_path}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : the directory of  ${t3rd_code_path} is not exist"
        return $ret
    fi
    checkout_branch ${DEV_BRANCH_NAME}
    git reset --hard remotes/origin/${DEV_BRANCH_NAME}
    cd ${ROOT_PATH}
}

function exit_if_return_failure()
{
    ret=$1
    if [ ${ret} -eq 0 ]; then
        echo "Success to call ${2}"
        return
    fi
    echo "Fault : in [$2], ret = ${ret}"
    aftermath_handling_recovery
    exit ${ret}
}

function main() 
{
    parse_arguments "$@"

    check_arguments
    if [ $? -ne 0 ]; then
        show_usage
        exit 0
    fi
    echo "============zhl ===================="
    echo " = BRANCH_NAME = ${BRANCH_NAME} ===="
    echo " = GIT_COMMIT = ${GIT_COMMIT} ===="
    echo " = GIT_PREVIOUS_COMMIT = ${GIT_PREVIOUS_COMMIT} ===="
    echo " = GIT_LOCAL_BRANCH = ${GIT_LOCAL_BRANCH} ===="
    echo " = TAG_NAME = ${TAG_NAME} ===="
    echo "============zhl ===================="

    check_if_need_compile ${DEV_BRANCH_NAME}
    if [ $? -eq 0 ]; then
        aftermath_handling_recovery
        exit 0
    fi

    3rd_complie_source
    exit_if_return_failure $? "3rd_complie_source"

    3rd_checkout_rel_branch
    exit_if_return_failure $? "3rd_checkout_rel_branch"
 
    overwrite_3rd_from_install
    exit_if_return_failure $? "overwrite_3rd_from_install"

    commit_3rd_2_rel
    exit_if_return_failure $? "commit_3rd_2_rel"

    aftermath_handling_recovery
    exit 0
}

main "$@"