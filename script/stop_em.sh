#!/bin/bash

FILE_DOCKERINFO="/hfr/.dockerInfo"
dockerID=""
arrPID=()
if [ -f "${FILE_DOCKERINFO}" ];
then    
    dockerID=$(cat ${FILE_DOCKERINFO})
fi  

ARCH=$(uname -m)
echo "$ARCH"

apps=(em_service config_service data_transfer camera_service time_service  hspi_sync around_view_monitor vehicle_service dead_reckoning line_slot_perception phc2sys ptp4l lidar_service mechanical_slot_perception slot_manager map fusion_localization planning hpp_function hpp_hmi control apa_ui fusion_obstacle_perception lidar_obstacle_perception road_boundary_perception_track space_slot_perception hlog_service  dlt  iox-roudi)

#not stop ptp now
#stop p2p only for x86 ptf  
#if [[ $ARCH == *"x86"* ]]; then
#apps+=(ptp4l phc2sys)
#fi

stopApp()
{ 
  killedpid=$(ps -ef | grep $1 |grep -v 'grep'|grep -v 'tail'|awk '{print $2}')

  if [ -n "$killedpid" ];
  then    
    ps -ef | grep $1 |grep -v 'grep'|grep -v 'tail'
    echo "kill $1,pid=[$killedpid]"
    kill -2 $killedpid
    arrPID[${#arrPID[*]}]=$killedpid
  fi

}

#$1=sessionPid, $2=appname
stopAppBySessionID()
{ 
  killedpid=$(ps -ef |grep -w $1 |grep -v 'grep'|grep -v 'tail' | grep $2 |awk '{print $2}')

  if [ -n "$killedpid" ];
  then    
    ps -ef |grep -w $1 |grep -v 'grep'|grep -v 'tail' | grep $2
    echo "kill $2,pid=[$killedpid]"
    kill -2 $killedpid
    arrPID[${#arrPID[*]}]=$killedpid
  fi
}

stopAppsbyAppName()
{
  for app in "${apps[@]}"
  do    
    stopApp $app
  done
}

stopAppsbyAppNameAndSessionID()
{
  echo "Stop Apps In Session PID: $1"  
  for app in "${apps[@]}"
  do
    #echo "===$app===="
    stopAppBySessionID $1 $app
  done
}

stopAppsInCurrentDocker()
{
   dockerPid=$(ps -ef |grep -w $dockerID |grep -v 'grep'|grep -v 'tail'|awk '{print $2}' )
   bashListPid=$(ps -ef |grep -w $dockerPid |grep -v 'grep'|grep -v 'tail'|awk '{print $2}' ) 
   
   #string to Array
   bashListArray=(${bashListPid/\n/})
   for bashPid in "${bashListArray[@]}"
   do
      stopAppsbyAppNameAndSessionID $bashPid
   done
}


if [[ $ARCH != *"x86"* ]]; then
    echo "stop heart first"
    CURRENT_DIR=$(cd $(dirname $0); pwd)
    export LD_LIBRARY_PATH=$CURRENT_DIR/lib:$CURRENT_DIR/modules/3j3/sysd_lib:$LD_LIBRARY_PATH
    cd $CURRENT_DIR/modules/heartControlStop
    ./heartControlStop
fi

if [ -n "$dockerID" ];
then  
   stopAppsInCurrentDocker
else
   stopAppsbyAppName
fi  


#force to kill not exited pid
#echo arrPID value:"${arrPID[*]}"
sleep 3
for(( i=0;i<${#arrPID[@]};i++)) do   
  #echo ${arrPID[i]}
  PID_EXIST=$(ps aux | awk '{print $2}'| grep -w ${arrPID[i]})
  
  if [ $PID_EXIST ];then
    echo the process ${arrPID[i]} exist
    kill -9 ${arrPID[i]}
  fi    
done


