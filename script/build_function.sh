#!/bin/bash

#cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=debug
function cmd_env()
{
  #check makefile   
  if [[ ! -f "script/CMakeLists.txt" ]]; then
     echo "script/CMakeLists.txt not exist"
    exit
  fi

  if [[ -d "build" ]]; then
     echo "run: rm -rf build, please waiting..."
     rm -rf build
  fi

  if [[ "$1" =~ "rel" ]]; then
     echo "run: cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=release"
     cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=release
  else
     echo "run: cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=debug"
     cmake -Bbuild -Sscript -DCMAKE_BUILD_TYPE=debug   
  fi    
}

function cmd_exec()
{
  if [[ ! -d "build" ]]; then
     echo "env is not build, build first, dev"
     cmd_env "dev"
  fi

   echo "run: cmake --build build --target install -j32"
   cmake --build build --target install -j32
}

function tar_build_install()
{
  if [[ -d "build/install" ]]; then
      cd build
      tar czvf install_$1.tar.gz install
      mv install_$1.tar.gz ../
      cd -
      ls -lh /hfr/install_$1.tar.gz
  else
      echo "install dir is not exist"
      exit
  fi
}
function tar_script()
{
  if [[ -d "script" ]]; then
      tar czvf script_$1.tar.gz script
      ls -lh /hfr/script_$1.tar.gz
  else
      echo "script dir is not exist"
      exit
  fi
}
function cmd_pack()
{
   randName=`date +%m%d%H%M`
case $1 in
   build)     
     tar_build_install $randName
     ;;
   script)
     tar_script $randName
     ;;
   full)
     tar_build_install $randName
     tar_script $randName
     ;;
esac
}
