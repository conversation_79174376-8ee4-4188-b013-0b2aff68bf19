#!/bin/bash
set -e

# ----------------------------
# 配置部分：各目录路径设置
# ----------------------------
# lib目录路径，包含库文件和软链接
LIB_DIR="./lib/road_boundary_perception_track/arm64_a1000b/lib"
# 模块目录路径
MODULE_DIR="./build_a1000/install/arm64_a1000b/modules/road_boundary_perception_track"

# ----------------------------
# 提取版本号
# ----------------------------
# 假设库文件命名格式为：xxx.so.版本号，如 xxx.so.1.21.1.3
lib_file=$(find "$LIB_DIR" -maxdepth 1 -type f -name "*.so.*" | head -n 1)
if [ -z "$lib_file" ]; then
  echo "未找到符合要求的库文件 (*.so.*)！"
  exit 1
fi

# 提取版本号：去除路径和前缀，只保留 .so. 后的部分
# 此处假设库文件名中只出现一次 .so.
version=$(basename "$lib_file" | sed 's/.*\.so\.//')
echo "检测到版本号：$version"

# ----------------------------
# 获取当前日期时间，格式为mmddhhmm
# ----------------------------
current_datetime=$(date +"%m%d%H%M")
echo "当前日期时间：$current_datetime"

# 目标目录名
target_dir="rbt_a1000_bin_v${version}_${current_datetime}"

# ----------------------------
# 准备临时目录
# ----------------------------
tmp_dir=$(mktemp -d)
echo "创建临时目录：$tmp_dir"

# 复制 lib 目录（保持软链接不被破坏）
mkdir -p "$tmp_dir/$target_dir"
cp -a "$LIB_DIR" "$tmp_dir/$target_dir/lib"

# 复制 road_boundary_perception_track 模块目录
cp -a "$MODULE_DIR" "$tmp_dir/$target_dir/road_boundary_perception_track"

# ----------------------------
# 打包成 tar.gz 压缩包
# ----------------------------
pkg_name="rbt_a1000_bin_v${version}_${current_datetime}.tar.gz"
pkg_path="$tmp_dir/$pkg_name"
echo "正在打包为：$pkg_name"
# -C 指定切换到临时目录进行打包，保证压缩包内目录结构正确
tar -czf "$pkg_path" -C "$tmp_dir" "$target_dir"

# 将打包文件移回当前工作目录
mv "$pkg_path" .

# 清理临时目录
rm -rf "$tmp_dir"
echo "临时目录已删除。"

echo "打包完成，生成压缩包：$pkg_name"


# JFrog 配置
JFROG_URL="http://***********:8082/artifactory/P_ADAS_HPP/hfr/wz3200/haf_rbt"
API_KEY="cmVmdGtuOjAxOjAwMDAwMDAwMDA6ZG96YzFtZ1o4SGlYSTI4VWpJMDAyRElQdWFH"

# 上传 tar.gz 包到 JFrog
UPLOAD_URL="$JFROG_URL/$pkg_name"
echo "开始上传到 JFrog"
curl --noproxy "*" -H "X-JFrog-Art-Api:$API_KEY" -T "$pkg_name" "$UPLOAD_URL"

# 检查上传状态
if [[ $? -eq 0 ]]; then
    echo -e "\n上传成功...下载地址：$UPLOAD_URL"
else
    echo -e "\n上传失败...请检查配置"
    exit 1
fi
