#!/bin/bash
CURRENT_DIR=$(cd $(dirname $0); pwd)

export LD_LIBRARY_PATH=$CURRENT_DIR/lib:$CURRENT_DIR/modules/3j3/sysd_lib:$LD_LIBRARY_PATH
export VSOMEIP_CONFIGURATION=$CURRENT_DIR/vsomeip.json


ARCH=$(uname -m)
echo "$ARCH"

#only x86 need to start ptp
#if [[ $ARCH == *"x86"* ]]; then
#start ptp first
#if [[ -d "$CURRENT_DIR/modules/lidar_service_tool" ]]; then
#    echo "match"
#    cd $CURRENT_DIR/modules/lidar_service_tool
#    ./run.sh &
#fi
#fi  

selected_car_name=$(python3 -c "import json; print(json.load(open('${CURRENT_DIR}/resource/car_instance.json'))['selected_car_name'])")
echo $selected_car_name
config_name=${CURRENT_DIR}/resource/${selected_car_name}/em_start_config.json
echo $config_name

if [ -e "${config_name}" ]; then
    cd $CURRENT_DIR/modules/em_service/bin
    ./em_service $config_name&
else
    echo "$config_name file not exist"
fi





