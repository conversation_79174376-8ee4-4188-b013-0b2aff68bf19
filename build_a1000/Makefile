# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.19

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/bstos/linux-23/cmake-3.19.6/install/bin/cmake

# The command to remove a file.
RM = /opt/bstos/linux-23/cmake-3.19.6/install/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /hfr/script

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /hfr/build_a1000

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/opt/bstos/linux-23/cmake-3.19.6/install/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/opt/bstos/linux-23/cmake-3.19.6/install/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/opt/bstos/linux-23/cmake-3.19.6/install/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/opt/bstos/linux-23/cmake-3.19.6/install/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/opt/bstos/linux-23/cmake-3.19.6/install/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/opt/bstos/linux-23/cmake-3.19.6/install/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/opt/bstos/linux-23/cmake-3.19.6/install/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/opt/bstos/linux-23/cmake-3.19.6/install/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /hfr/build_a1000/CMakeFiles /hfr/build_a1000//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /hfr/build_a1000/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named lidar_curb_perception_segment

# Build rule for target.
lidar_curb_perception_segment: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lidar_curb_perception_segment
.PHONY : lidar_curb_perception_segment

# fast build rule for target.
lidar_curb_perception_segment/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/lidar_curb_perception_segment/CMakeFiles/lidar_curb_perception_segment.dir/build.make .cache/Release/lidar_curb_perception_segment/CMakeFiles/lidar_curb_perception_segment.dir/build
.PHONY : lidar_curb_perception_segment/fast

#=============================================================================
# Target rules for targets named lidar_obstacle_perception_segment

# Build rule for target.
lidar_obstacle_perception_segment: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lidar_obstacle_perception_segment
.PHONY : lidar_obstacle_perception_segment

# fast build rule for target.
lidar_obstacle_perception_segment/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/lidar_obstacle_perception_segment/CMakeFiles/lidar_obstacle_perception_segment.dir/build.make .cache/Release/lidar_obstacle_perception_segment/CMakeFiles/lidar_obstacle_perception_segment.dir/build
.PHONY : lidar_obstacle_perception_segment/fast

#=============================================================================
# Target rules for targets named road_boundary_perception_track

# Build rule for target.
road_boundary_perception_track: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 road_boundary_perception_track
.PHONY : road_boundary_perception_track

# fast build rule for target.
road_boundary_perception_track/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/road_boundary_perception_track/CMakeFiles/road_boundary_perception_track.dir/build.make .cache/Release/road_boundary_perception_track/CMakeFiles/road_boundary_perception_track.dir/build
.PHONY : road_boundary_perception_track/fast

#=============================================================================
# Target rules for targets named hpp_calib_function

# Build rule for target.
hpp_calib_function: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 hpp_calib_function
.PHONY : hpp_calib_function

# fast build rule for target.
hpp_calib_function/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/hpp_calib_function/CMakeFiles/hpp_calib_function.dir/build.make .cache/Release/hpp_calib_function/CMakeFiles/hpp_calib_function.dir/build
.PHONY : hpp_calib_function/fast

#=============================================================================
# Target rules for targets named hpp_function

# Build rule for target.
hpp_function: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 hpp_function
.PHONY : hpp_function

# fast build rule for target.
hpp_function/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/hpp_function/CMakeFiles/hpp_function.dir/build.make .cache/Release/hpp_function/CMakeFiles/hpp_function.dir/build
.PHONY : hpp_function/fast

#=============================================================================
# Target rules for targets named hpp_hmi

# Build rule for target.
hpp_hmi: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 hpp_hmi
.PHONY : hpp_hmi

# fast build rule for target.
hpp_hmi/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/hpp_hmi/CMakeFiles/hpp_hmi.dir/build.make .cache/Release/hpp_hmi/CMakeFiles/hpp_hmi.dir/build
.PHONY : hpp_hmi/fast

#=============================================================================
# Target rules for targets named config_service

# Build rule for target.
config_service: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 config_service
.PHONY : config_service

# fast build rule for target.
config_service/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/config_service/CMakeFiles/config_service.dir/build.make .cache/Release/config_service/CMakeFiles/config_service.dir/build
.PHONY : config_service/fast

#=============================================================================
# Target rules for targets named data_transfer

# Build rule for target.
data_transfer: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 data_transfer
.PHONY : data_transfer

# fast build rule for target.
data_transfer/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/data_transfer/CMakeFiles/data_transfer.dir/build.make .cache/Release/data_transfer/CMakeFiles/data_transfer.dir/build
.PHONY : data_transfer/fast

#=============================================================================
# Target rules for targets named em_service

# Build rule for target.
em_service: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 em_service
.PHONY : em_service

# fast build rule for target.
em_service/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/execution_manifest/em_service/CMakeFiles/em_service.dir/build.make .cache/Release/execution_manifest/em_service/CMakeFiles/em_service.dir/build
.PHONY : em_service/fast

#=============================================================================
# Target rules for targets named mainboard

# Build rule for target.
mainboard: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mainboard
.PHONY : mainboard

# fast build rule for target.
mainboard/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/execution_manifest/mainboard/CMakeFiles/mainboard.dir/build.make .cache/Release/execution_manifest/mainboard/CMakeFiles/mainboard.dir/build
.PHONY : mainboard/fast

#=============================================================================
# Target rules for targets named heartControlStop

# Build rule for target.
heartControlStop: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 heartControlStop
.PHONY : heartControlStop

# fast build rule for target.
heartControlStop/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/execution_manifest/heart_control/CMakeFiles/heartControlStop.dir/build.make .cache/Release/execution_manifest/heart_control/CMakeFiles/heartControlStop.dir/build
.PHONY : heartControlStop/fast

#=============================================================================
# Target rules for targets named hlog_service

# Build rule for target.
hlog_service: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 hlog_service
.PHONY : hlog_service

# fast build rule for target.
hlog_service/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/hlog_service/CMakeFiles/hlog_service.dir/build.make .cache/Release/hlog_service/CMakeFiles/hlog_service.dir/build
.PHONY : hlog_service/fast

#=============================================================================
# Target rules for targets named time_service

# Build rule for target.
time_service: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 time_service
.PHONY : time_service

# fast build rule for target.
time_service/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/time_service/CMakeFiles/time_service.dir/build.make .cache/Release/time_service/CMakeFiles/time_service.dir/build
.PHONY : time_service/fast

#=============================================================================
# Target rules for targets named mqtt_service

# Build rule for target.
mqtt_service: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mqtt_service
.PHONY : mqtt_service

# fast build rule for target.
mqtt_service/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/MQTT_service/CMakeFiles/mqtt_service.dir/build.make .cache/Release/MQTT_service/CMakeFiles/mqtt_service.dir/build
.PHONY : mqtt_service/fast

#=============================================================================
# Target rules for targets named lidar_service_hasco

# Build rule for target.
lidar_service_hasco: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lidar_service_hasco
.PHONY : lidar_service_hasco

# fast build rule for target.
lidar_service_hasco/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/lidar_service_hasco/CMakeFiles/lidar_service_hasco.dir/build.make .cache/Release/lidar_service_hasco/CMakeFiles/lidar_service_hasco.dir/build
.PHONY : lidar_service_hasco/fast

#=============================================================================
# Target rules for targets named vehicle_service_soc

# Build rule for target.
vehicle_service_soc: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 vehicle_service_soc
.PHONY : vehicle_service_soc

# fast build rule for target.
vehicle_service_soc/fast:
	$(MAKE) $(MAKESILENT) -f .cache/Release/vehicle_service_auto_soc/CMakeFiles/vehicle_service_soc.dir/build.make .cache/Release/vehicle_service_auto_soc/CMakeFiles/vehicle_service_soc.dir/build
.PHONY : vehicle_service_soc/fast

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... config_service"
	@echo "... data_transfer"
	@echo "... em_service"
	@echo "... heartControlStop"
	@echo "... hlog_service"
	@echo "... hpp_calib_function"
	@echo "... hpp_function"
	@echo "... hpp_hmi"
	@echo "... lidar_curb_perception_segment"
	@echo "... lidar_obstacle_perception_segment"
	@echo "... lidar_service_hasco"
	@echo "... mainboard"
	@echo "... mqtt_service"
	@echo "... road_boundary_perception_track"
	@echo "... time_service"
	@echo "... vehicle_service_soc"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

