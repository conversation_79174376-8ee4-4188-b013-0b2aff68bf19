[{"directory": "/hfr/build_a1000/.cache/Release/lidar_curb_perception_segment", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_curb_perception_segment\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_curb_perception_segment_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/3rdparty/eigen/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/lib/lidar_curb_perception_segment/arm64_a1000b/include -I/hfr/lib/lidar_curb_perception_segment/arm64_a1000b/include/lcs -I/hfr/modules/HAF/lidar_curb_perception_segment/include -O2 -pipe -g -feliminate-unused-debug-types -fopenmp -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/lidar_curb_perception_segment.dir/main.cpp.o -c /hfr/modules/HAF/lidar_curb_perception_segment/main.cpp", "file": "/hfr/modules/HAF/lidar_curb_perception_segment/main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_curb_perception_segment", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_curb_perception_segment\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_curb_perception_segment_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/3rdparty/eigen/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/lib/lidar_curb_perception_segment/arm64_a1000b/include -I/hfr/lib/lidar_curb_perception_segment/arm64_a1000b/include/lcs -I/hfr/modules/HAF/lidar_curb_perception_segment/include -O2 -pipe -g -feliminate-unused-debug-types -fopenmp -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/lidar_curb_perception_segment.dir/src/segment/components_lidar_curb_perception_segment.cpp.o -c /hfr/modules/HAF/lidar_curb_perception_segment/src/segment/components_lidar_curb_perception_segment.cpp", "file": "/hfr/modules/HAF/lidar_curb_perception_segment/src/segment/components_lidar_curb_perception_segment.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_obstacle_perception_segment", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_obstacle_perception_segment\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_obstacle_perception_segment_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/3rdparty/eigen/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/lib/lidar_obstacle_perception_segment/arm64_a1000b/include -I/hfr/lib/lidar_obstacle_perception_segment/arm64_a1000b/include/los -I/hfr/modules/HAF/lidar_obstacle_perception_segment/include -O2 -pipe -g -feliminate-unused-debug-types -fopenmp -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/lidar_obstacle_perception_segment.dir/main.cpp.o -c /hfr/modules/HAF/lidar_obstacle_perception_segment/main.cpp", "file": "/hfr/modules/HAF/lidar_obstacle_perception_segment/main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_obstacle_perception_segment", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_obstacle_perception_segment\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_obstacle_perception_segment_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/3rdparty/eigen/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/lib/lidar_obstacle_perception_segment/arm64_a1000b/include -I/hfr/lib/lidar_obstacle_perception_segment/arm64_a1000b/include/los -I/hfr/modules/HAF/lidar_obstacle_perception_segment/include -O2 -pipe -g -feliminate-unused-debug-types -fopenmp -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/lidar_obstacle_perception_segment.dir/src/segment/components_lidar_obstacle_perception_segment.cpp.o -c /hfr/modules/HAF/lidar_obstacle_perception_segment/src/segment/components_lidar_obstacle_perception_segment.cpp", "file": "/hfr/modules/HAF/lidar_obstacle_perception_segment/src/segment/components_lidar_obstacle_perception_segment.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/road_boundary_perception_track", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"road_boundary_perception_track\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Droad_boundary_perception_track_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/3rdparty/eigen/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/lib/road_boundary_perception_track/arm64_a1000b/include -I/hfr/lib/road_boundary_perception_track/arm64_a1000b/include/common -I/hfr/lib/road_boundary_perception_track/arm64_a1000b/include/RBT -I/hfr/modules/HAF/road_boundary_perception_track/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/road_boundary_perception_track.dir/main.cpp.o -c /hfr/modules/HAF/road_boundary_perception_track/main.cpp", "file": "/hfr/modules/HAF/road_boundary_perception_track/main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/road_boundary_perception_track", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"road_boundary_perception_track\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Droad_boundary_perception_track_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/3rdparty/eigen/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/lib/road_boundary_perception_track/arm64_a1000b/include -I/hfr/lib/road_boundary_perception_track/arm64_a1000b/include/common -I/hfr/lib/road_boundary_perception_track/arm64_a1000b/include/RBT -I/hfr/modules/HAF/road_boundary_perception_track/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/road_boundary_perception_track.dir/src/components_road_boundary_perception_track.cpp.o -c /hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp", "file": "/hfr/modules/HAF/road_boundary_perception_track/src/components_road_boundary_perception_track.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/road_boundary_perception_track", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"road_boundary_perception_track\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Droad_boundary_perception_track_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/3rdparty/eigen/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/lib/road_boundary_perception_track/arm64_a1000b/include -I/hfr/lib/road_boundary_perception_track/arm64_a1000b/include/common -I/hfr/lib/road_boundary_perception_track/arm64_a1000b/include/RBT -I/hfr/modules/HAF/road_boundary_perception_track/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/road_boundary_perception_track.dir/src/test_road_boundary_perception_track.cpp.o -c /hfr/modules/HAF/road_boundary_perception_track/src/test_road_boundary_perception_track.cpp", "file": "/hfr/modules/HAF/road_boundary_perception_track/src/test_road_boundary_perception_track.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/hpp_calib_function", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"hpp_calib_function\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dhpp_calib_function_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/hpp_calib_function.dir/main.cpp.o -c /hfr/modules/HFF/hpp_calib_function/main.cpp", "file": "/hfr/modules/HFF/hpp_calib_function/main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/hpp_calib_function", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"hpp_calib_function\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dhpp_calib_function_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/hpp_calib_function.dir/src/components_hpp_calib_function.cpp.o -c /hfr/modules/HFF/hpp_calib_function/src/components_hpp_calib_function.cpp", "file": "/hfr/modules/HFF/hpp_calib_function/src/components_hpp_calib_function.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/hpp_function", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"hpp_function\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dhpp_function_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/hpp_function.dir/main.cpp.o -c /hfr/modules/HFF/hpp_function/main.cpp", "file": "/hfr/modules/HFF/hpp_function/main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/hpp_function", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"hpp_function\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dhpp_function_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/hpp_function.dir/src/components_hpp_function.cpp.o -c /hfr/modules/HFF/hpp_function/src/components_hpp_function.cpp", "file": "/hfr/modules/HFF/hpp_function/src/components_hpp_function.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/hpp_function", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"hpp_function\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dhpp_function_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/hpp_function.dir/src/components_hpp_function_lsv.cpp.o -c /hfr/modules/HFF/hpp_function/src/components_hpp_function_lsv.cpp", "file": "/hfr/modules/HFF/hpp_function/src/components_hpp_function_lsv.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/hpp_hmi", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"hpp_hmi\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dhpp_hmi_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/hpp_hmi.dir/main.cpp.o -c /hfr/modules/HFF/hpp_hmi/main.cpp", "file": "/hfr/modules/HFF/hpp_hmi/main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/hpp_hmi", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"hpp_hmi\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dhpp_hmi_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/hpp_hmi.dir/src/components_hpp_hmi.cpp.o -c /hfr/modules/HFF/hpp_hmi/src/components_hpp_hmi.cpp", "file": "/hfr/modules/HFF/hpp_hmi/src/components_hpp_hmi.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/hpp_hmi", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"hpp_hmi\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dhpp_hmi_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/hpp_hmi.dir/src/components_hpp_hmi_lsv.cpp.o -c /hfr/modules/HFF/hpp_hmi/src/components_hpp_hmi_lsv.cpp", "file": "/hfr/modules/HFF/hpp_hmi/src/components_hpp_hmi_lsv.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/ConfigService.cpp.o -c /hfr/modules/HFR/config_service/src/ConfigService.cpp", "file": "/hfr/modules/HFR/config_service/src/ConfigService.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/config/ApplicationConfig.cpp.o -c /hfr/modules/HFR/config_service/src/config/ApplicationConfig.cpp", "file": "/hfr/modules/HFR/config_service/src/config/ApplicationConfig.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/config/ApplicationContext.cpp.o -c /hfr/modules/HFR/config_service/src/config/ApplicationContext.cpp", "file": "/hfr/modules/HFR/config_service/src/config/ApplicationContext.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/controller/AbstractController.cpp.o -c /hfr/modules/HFR/config_service/src/controller/AbstractController.cpp", "file": "/hfr/modules/HFR/config_service/src/controller/AbstractController.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/controller/AbstractTcpSocketController.cpp.o -c /hfr/modules/HFR/config_service/src/controller/AbstractTcpSocketController.cpp", "file": "/hfr/modules/HFR/config_service/src/controller/AbstractTcpSocketController.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/controller/ClusterSynchronizeSocketController.cpp.o -c /hfr/modules/HFR/config_service/src/controller/ClusterSynchronizeSocketController.cpp", "file": "/hfr/modules/HFR/config_service/src/controller/ClusterSynchronizeSocketController.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/controller/ParameterClientSocketController.cpp.o -c /hfr/modules/HFR/config_service/src/controller/ParameterClientSocketController.cpp", "file": "/hfr/modules/HFR/config_service/src/controller/ParameterClientSocketController.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/controller/ParameterController.cpp.o -c /hfr/modules/HFR/config_service/src/controller/ParameterController.cpp", "file": "/hfr/modules/HFR/config_service/src/controller/ParameterController.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/data/sender/ClusterBroadcaster.cpp.o -c /hfr/modules/HFR/config_service/src/data/sender/ClusterBroadcaster.cpp", "file": "/hfr/modules/HFR/config_service/src/data/sender/ClusterBroadcaster.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/data/sender/ParameterClientSender.cpp.o -c /hfr/modules/HFR/config_service/src/data/sender/ParameterClientSender.cpp", "file": "/hfr/modules/HFR/config_service/src/data/sender/ParameterClientSender.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/data/watcher/FileWatcher.cpp.o -c /hfr/modules/HFR/config_service/src/data/watcher/FileWatcher.cpp", "file": "/hfr/modules/HFR/config_service/src/data/watcher/FileWatcher.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/data/watcher/FileWatcherManager.cpp.o -c /hfr/modules/HFR/config_service/src/data/watcher/FileWatcherManager.cpp", "file": "/hfr/modules/HFR/config_service/src/data/watcher/FileWatcherManager.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/framework/ApplicationDelegate.cpp.o -c /hfr/modules/HFR/config_service/src/framework/ApplicationDelegate.cpp", "file": "/hfr/modules/HFR/config_service/src/framework/ApplicationDelegate.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/framework/InitializingObject.cpp.o -c /hfr/modules/HFR/config_service/src/framework/InitializingObject.cpp", "file": "/hfr/modules/HFR/config_service/src/framework/InitializingObject.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/framework/Object.cpp.o -c /hfr/modules/HFR/config_service/src/framework/Object.cpp", "file": "/hfr/modules/HFR/config_service/src/framework/Object.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/framework/ObjectFactory.cpp.o -c /hfr/modules/HFR/config_service/src/framework/ObjectFactory.cpp", "file": "/hfr/modules/HFR/config_service/src/framework/ObjectFactory.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/framework/TaskPool.cpp.o -c /hfr/modules/HFR/config_service/src/framework/TaskPool.cpp", "file": "/hfr/modules/HFR/config_service/src/framework/TaskPool.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/framework/ThreadPool.cpp.o -c /hfr/modules/HFR/config_service/src/framework/ThreadPool.cpp", "file": "/hfr/modules/HFR/config_service/src/framework/ThreadPool.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/model/SynchronizationFile.cpp.o -c /hfr/modules/HFR/config_service/src/model/SynchronizationFile.cpp", "file": "/hfr/modules/HFR/config_service/src/model/SynchronizationFile.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/service/ParameterService.cpp.o -c /hfr/modules/HFR/config_service/src/service/ParameterService.cpp", "file": "/hfr/modules/HFR/config_service/src/service/ParameterService.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/service/impl/ClusterSynchronizeSocketService.cpp.o -c /hfr/modules/HFR/config_service/src/service/impl/ClusterSynchronizeSocketService.cpp", "file": "/hfr/modules/HFR/config_service/src/service/impl/ClusterSynchronizeSocketService.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/service/impl/FileSynchronizationEventBuilder.cpp.o -c /hfr/modules/HFR/config_service/src/service/impl/FileSynchronizationEventBuilder.cpp", "file": "/hfr/modules/HFR/config_service/src/service/impl/FileSynchronizationEventBuilder.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/service/impl/FileSynchronizationEventPublisher.cpp.o -c /hfr/modules/HFR/config_service/src/service/impl/FileSynchronizationEventPublisher.cpp", "file": "/hfr/modules/HFR/config_service/src/service/impl/FileSynchronizationEventPublisher.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/service/impl/FileSynchronizationPostbackFilter.cpp.o -c /hfr/modules/HFR/config_service/src/service/impl/FileSynchronizationPostbackFilter.cpp", "file": "/hfr/modules/HFR/config_service/src/service/impl/FileSynchronizationPostbackFilter.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/service/impl/ParameterClientSocketService.cpp.o -c /hfr/modules/HFR/config_service/src/service/impl/ParameterClientSocketService.cpp", "file": "/hfr/modules/HFR/config_service/src/service/impl/ParameterClientSocketService.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/service/task/FileWatcherTask.cpp.o -c /hfr/modules/HFR/config_service/src/service/task/FileWatcherTask.cpp", "file": "/hfr/modules/HFR/config_service/src/service/task/FileWatcherTask.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/service/timer/FileSynchronizeTimer.cpp.o -c /hfr/modules/HFR/config_service/src/service/timer/FileSynchronizeTimer.cpp", "file": "/hfr/modules/HFR/config_service/src/service/timer/FileSynchronizeTimer.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/util/Base64.cpp.o -c /hfr/modules/HFR/config_service/src/util/Base64.cpp", "file": "/hfr/modules/HFR/config_service/src/util/Base64.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/util/FileUtil.cpp.o -c /hfr/modules/HFR/config_service/src/util/FileUtil.cpp", "file": "/hfr/modules/HFR/config_service/src/util/FileUtil.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/util/HttpResponseUtil.cpp.o -c /hfr/modules/HFR/config_service/src/util/HttpResponseUtil.cpp", "file": "/hfr/modules/HFR/config_service/src/util/HttpResponseUtil.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/util/IpUtil.cpp.o -c /hfr/modules/HFR/config_service/src/util/IpUtil.cpp", "file": "/hfr/modules/HFR/config_service/src/util/IpUtil.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/util/Md5.cpp.o -c /hfr/modules/HFR/config_service/src/util/Md5.cpp", "file": "/hfr/modules/HFR/config_service/src/util/Md5.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/util/TcpSocketUtil.cpp.o -c /hfr/modules/HFR/config_service/src/util/TcpSocketUtil.cpp", "file": "/hfr/modules/HFR/config_service/src/util/TcpSocketUtil.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/config_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"config_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/config_service/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -fno-strict-aliasing -Wvla -Wall -Werror -o CMakeFiles/config_service.dir/src/util/UrlUtil.cpp.o -c /hfr/modules/HFR/config_service/src/util/UrlUtil.cpp", "file": "/hfr/modules/HFR/config_service/src/util/UrlUtil.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/data_transfer", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"data_transfer\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Ddata_transfer_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/data_transfer.dir/main.cpp.o -c /hfr/modules/HFR/data_transfer/main.cpp", "file": "/hfr/modules/HFR/data_transfer/main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/data_transfer", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"data_transfer\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Ddata_transfer_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/data_transfer.dir/src/base_server.cpp.o -c /hfr/modules/HFR/data_transfer/src/base_server.cpp", "file": "/hfr/modules/HFR/data_transfer/src/base_server.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/data_transfer", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"data_transfer\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Ddata_transfer_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/data_transfer.dir/src/remote_control.cpp.o -c /hfr/modules/HFR/data_transfer/src/remote_control.cpp", "file": "/hfr/modules/HFR/data_transfer/src/remote_control.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/data_transfer", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"data_transfer\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Ddata_transfer_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/data_transfer.dir/src/remote_server.cpp.o -c /hfr/modules/HFR/data_transfer/src/remote_server.cpp", "file": "/hfr/modules/HFR/data_transfer/src/remote_server.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/data_transfer", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"data_transfer\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Ddata_transfer_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/data_transfer.dir/src/remote_server_component.cpp.o -c /hfr/modules/HFR/data_transfer/src/remote_server_component.cpp", "file": "/hfr/modules/HFR/data_transfer/src/remote_server_component.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/em_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"em_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/em_service/parameter -I/hfr/modules/HFR/execution_manifest/em_service/common -I/hfr/modules/HFR/execution_manifest/em_service/process -I/hfr/modules/HFR/execution_manifest/em_service/monitor -I/hfr/modules/HFR/execution_manifest/em_service/component -I/hfr/modules/HFR/execution_manifest/em_service/file -I/hfr/modules/HFR/execution_manifest/em_service/mcucontrol -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/em_service.dir/common/em_utils.cpp.o -c /hfr/modules/HFR/execution_manifest/em_service/common/em_utils.cpp", "file": "/hfr/modules/HFR/execution_manifest/em_service/common/em_utils.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/em_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"em_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/em_service/parameter -I/hfr/modules/HFR/execution_manifest/em_service/common -I/hfr/modules/HFR/execution_manifest/em_service/process -I/hfr/modules/HFR/execution_manifest/em_service/monitor -I/hfr/modules/HFR/execution_manifest/em_service/component -I/hfr/modules/HFR/execution_manifest/em_service/file -I/hfr/modules/HFR/execution_manifest/em_service/mcucontrol -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/em_service.dir/main/em_main.cpp.o -c /hfr/modules/HFR/execution_manifest/em_service/main/em_main.cpp", "file": "/hfr/modules/HFR/execution_manifest/em_service/main/em_main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/em_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"em_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/em_service/parameter -I/hfr/modules/HFR/execution_manifest/em_service/common -I/hfr/modules/HFR/execution_manifest/em_service/process -I/hfr/modules/HFR/execution_manifest/em_service/monitor -I/hfr/modules/HFR/execution_manifest/em_service/component -I/hfr/modules/HFR/execution_manifest/em_service/file -I/hfr/modules/HFR/execution_manifest/em_service/mcucontrol -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/em_service.dir/parameter/parameter.cpp.o -c /hfr/modules/HFR/execution_manifest/em_service/parameter/parameter.cpp", "file": "/hfr/modules/HFR/execution_manifest/em_service/parameter/parameter.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/em_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"em_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/em_service/parameter -I/hfr/modules/HFR/execution_manifest/em_service/common -I/hfr/modules/HFR/execution_manifest/em_service/process -I/hfr/modules/HFR/execution_manifest/em_service/monitor -I/hfr/modules/HFR/execution_manifest/em_service/component -I/hfr/modules/HFR/execution_manifest/em_service/file -I/hfr/modules/HFR/execution_manifest/em_service/mcucontrol -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/em_service.dir/process/process_base.cpp.o -c /hfr/modules/HFR/execution_manifest/em_service/process/process_base.cpp", "file": "/hfr/modules/HFR/execution_manifest/em_service/process/process_base.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/em_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"em_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/em_service/parameter -I/hfr/modules/HFR/execution_manifest/em_service/common -I/hfr/modules/HFR/execution_manifest/em_service/process -I/hfr/modules/HFR/execution_manifest/em_service/monitor -I/hfr/modules/HFR/execution_manifest/em_service/component -I/hfr/modules/HFR/execution_manifest/em_service/file -I/hfr/modules/HFR/execution_manifest/em_service/mcucontrol -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/em_service.dir/process/process_manager.cpp.o -c /hfr/modules/HFR/execution_manifest/em_service/process/process_manager.cpp", "file": "/hfr/modules/HFR/execution_manifest/em_service/process/process_manager.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/em_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"em_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/em_service/parameter -I/hfr/modules/HFR/execution_manifest/em_service/common -I/hfr/modules/HFR/execution_manifest/em_service/process -I/hfr/modules/HFR/execution_manifest/em_service/monitor -I/hfr/modules/HFR/execution_manifest/em_service/component -I/hfr/modules/HFR/execution_manifest/em_service/file -I/hfr/modules/HFR/execution_manifest/em_service/mcucontrol -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/em_service.dir/monitor/cpu_monitor.cpp.o -c /hfr/modules/HFR/execution_manifest/em_service/monitor/cpu_monitor.cpp", "file": "/hfr/modules/HFR/execution_manifest/em_service/monitor/cpu_monitor.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/em_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"em_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/em_service/parameter -I/hfr/modules/HFR/execution_manifest/em_service/common -I/hfr/modules/HFR/execution_manifest/em_service/process -I/hfr/modules/HFR/execution_manifest/em_service/monitor -I/hfr/modules/HFR/execution_manifest/em_service/component -I/hfr/modules/HFR/execution_manifest/em_service/file -I/hfr/modules/HFR/execution_manifest/em_service/mcucontrol -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/em_service.dir/monitor/disk_monitor.cpp.o -c /hfr/modules/HFR/execution_manifest/em_service/monitor/disk_monitor.cpp", "file": "/hfr/modules/HFR/execution_manifest/em_service/monitor/disk_monitor.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/em_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"em_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/em_service/parameter -I/hfr/modules/HFR/execution_manifest/em_service/common -I/hfr/modules/HFR/execution_manifest/em_service/process -I/hfr/modules/HFR/execution_manifest/em_service/monitor -I/hfr/modules/HFR/execution_manifest/em_service/component -I/hfr/modules/HFR/execution_manifest/em_service/file -I/hfr/modules/HFR/execution_manifest/em_service/mcucontrol -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/em_service.dir/monitor/heart_monitor.cpp.o -c /hfr/modules/HFR/execution_manifest/em_service/monitor/heart_monitor.cpp", "file": "/hfr/modules/HFR/execution_manifest/em_service/monitor/heart_monitor.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/em_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"em_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/em_service/parameter -I/hfr/modules/HFR/execution_manifest/em_service/common -I/hfr/modules/HFR/execution_manifest/em_service/process -I/hfr/modules/HFR/execution_manifest/em_service/monitor -I/hfr/modules/HFR/execution_manifest/em_service/component -I/hfr/modules/HFR/execution_manifest/em_service/file -I/hfr/modules/HFR/execution_manifest/em_service/mcucontrol -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/em_service.dir/monitor/memory_monitor.cpp.o -c /hfr/modules/HFR/execution_manifest/em_service/monitor/memory_monitor.cpp", "file": "/hfr/modules/HFR/execution_manifest/em_service/monitor/memory_monitor.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/em_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"em_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/em_service/parameter -I/hfr/modules/HFR/execution_manifest/em_service/common -I/hfr/modules/HFR/execution_manifest/em_service/process -I/hfr/modules/HFR/execution_manifest/em_service/monitor -I/hfr/modules/HFR/execution_manifest/em_service/component -I/hfr/modules/HFR/execution_manifest/em_service/file -I/hfr/modules/HFR/execution_manifest/em_service/mcucontrol -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/em_service.dir/file/em_file.cpp.o -c /hfr/modules/HFR/execution_manifest/em_service/file/em_file.cpp", "file": "/hfr/modules/HFR/execution_manifest/em_service/file/em_file.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/em_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"em_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/em_service/parameter -I/hfr/modules/HFR/execution_manifest/em_service/common -I/hfr/modules/HFR/execution_manifest/em_service/process -I/hfr/modules/HFR/execution_manifest/em_service/monitor -I/hfr/modules/HFR/execution_manifest/em_service/component -I/hfr/modules/HFR/execution_manifest/em_service/file -I/hfr/modules/HFR/execution_manifest/em_service/mcucontrol -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/em_service.dir/mcucontrol/mcu_control.cpp.o -c /hfr/modules/HFR/execution_manifest/em_service/mcucontrol/mcu_control.cpp", "file": "/hfr/modules/HFR/execution_manifest/em_service/mcucontrol/mcu_control.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/mainboard", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/mainboard/shared_library -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/mainboard.dir/main/mainboard.cpp.o -c /hfr/modules/HFR/execution_manifest/mainboard/main/mainboard.cpp", "file": "/hfr/modules/HFR/execution_manifest/mainboard/main/mainboard.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/mainboard", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/modules/HFR/execution_manifest/mainboard/shared_library -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/mainboard.dir/shared_library/shared_library.cpp.o -c /hfr/modules/HFR/execution_manifest/mainboard/shared_library/shared_library.cpp", "file": "/hfr/modules/HFR/execution_manifest/mainboard/shared_library/shared_library.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/execution_manifest/heart_control", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMCU_KEEPALIVE=1 -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"heartControlStop\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -pthread -o CMakeFiles/heartControlStop.dir/heart_control.cpp.o -c /hfr/modules/HFR/execution_manifest/heart_control/heart_control.cpp", "file": "/hfr/modules/HFR/execution_manifest/heart_control/heart_control.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/hlog_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"hlog_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/hlog_service/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -std=gnu++14 -o CMakeFiles/hlog_service.dir/src/glog_service.cpp.o -c /hfr/modules/HFR/hlog_service/src/glog_service.cpp", "file": "/hfr/modules/HFR/hlog_service/src/glog_service.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/hlog_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"hlog_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/hlog_service/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -std=gnu++14 -o CMakeFiles/hlog_service.dir/src/dlt_service.cpp.o -c /hfr/modules/HFR/hlog_service/src/dlt_service.cpp", "file": "/hfr/modules/HFR/hlog_service/src/dlt_service.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/hlog_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"hlog_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/hlog_service/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -std=gnu++14 -o CMakeFiles/hlog_service.dir/src/main.cpp.o -c /hfr/modules/HFR/hlog_service/src/main.cpp", "file": "/hfr/modules/HFR/hlog_service/src/main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/hlog_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"hlog_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -I/hfr/modules/HFR/hlog_service/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -std=c++14 -std=gnu++14 -o CMakeFiles/hlog_service.dir/src/log_factory.cpp.o -c /hfr/modules/HFR/hlog_service/src/log_factory.cpp", "file": "/hfr/modules/HFR/hlog_service/src/log_factory.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/time_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"time_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dtime_service_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/time_service.dir/main.cpp.o -c /hfr/modules/HFR/time_service/main.cpp", "file": "/hfr/modules/HFR/time_service/main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/time_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"time_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dtime_service_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/time_service.dir/src/component_time_sync.cpp.o -c /hfr/modules/HFR/time_service/src/component_time_sync.cpp", "file": "/hfr/modules/HFR/time_service/src/component_time_sync.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/time_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"time_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dtime_service_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -std=c++14 -pthread -o CMakeFiles/time_service.dir/src/time_sync.cpp.o -c /hfr/modules/HFR/time_service/src/time_sync.cpp", "file": "/hfr/modules/HFR/time_service/src/time_sync.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/MQTT_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"mqtt_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dmqtt_service_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/mosquitto/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -I/hfr/3rdparty/cjson/arm64_a1000b/include -I/hfr/modules/HSF/MQTT_service/src/include -I/hfr/modules/HSF/MQTT_service/src/src -I/hfr/modules/HSF/MQTT_service/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/mqtt_service.dir/main.cpp.o -c /hfr/modules/HSF/MQTT_service/main.cpp", "file": "/hfr/modules/HSF/MQTT_service/main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/MQTT_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"mqtt_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dmqtt_service_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/mosquitto/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -I/hfr/3rdparty/cjson/arm64_a1000b/include -I/hfr/modules/HSF/MQTT_service/src/include -I/hfr/modules/HSF/MQTT_service/src/src -I/hfr/modules/HSF/MQTT_service/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/mqtt_service.dir/src/src/MQTTSubService.cpp.o -c /hfr/modules/HSF/MQTT_service/src/src/MQTTSubService.cpp", "file": "/hfr/modules/HSF/MQTT_service/src/src/MQTTSubService.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/MQTT_service", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"mqtt_service\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dmqtt_service_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/mosquitto/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -I/hfr/3rdparty/cjson/arm64_a1000b/include -I/hfr/modules/HSF/MQTT_service/src/include -I/hfr/modules/HSF/MQTT_service/src/src -I/hfr/modules/HSF/MQTT_service/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/mqtt_service.dir/src/src/MQTTdataProc.cpp.o -c /hfr/modules/HSF/MQTT_service/src/src/MQTTdataProc.cpp", "file": "/hfr/modules/HSF/MQTT_service/src/src/MQTTdataProc.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/main.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/main.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/anti_bloom.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/anti_bloom.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/anti_bloom.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/components_func_packets_service.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/components_func_packets_service.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/components_func_packets_service.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/components_lidar_merged_service.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/components_lidar_merged_service.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/components_lidar_merged_service.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/components_lidar_service1.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/components_lidar_service1.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/components_lidar_service1.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/components_lidar_service2.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/components_lidar_service2.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/components_lidar_service2.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/components_lidar_service3.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/components_lidar_service3.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/components_lidar_service3.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/components_lidar_service4.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/components_lidar_service4.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/components_lidar_service4.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/components_lidar_service5.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/components_lidar_service5.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/components_lidar_service5.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/components_packets_service1.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/components_packets_service1.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/components_packets_service1.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/components_packets_service2.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/components_packets_service2.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/components_packets_service2.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/components_packets_service3.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/components_packets_service3.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/components_packets_service3.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/components_packets_service4.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/components_packets_service4.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/components_packets_service4.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/components_packets_service5.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/components_packets_service5.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/components_packets_service5.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/lidar_service_hasco", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"lidar_service_hasco\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dlidar_service_hasco_EXPORTS -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/eigen/arm64_a1000b/include -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/lidar_service_hasco.dir/src/hasco_lidar.cpp.o -c /hfr/modules/HSF/lidar_service_hasco/src/hasco_lidar.cpp", "file": "/hfr/modules/HSF/lidar_service_hasco/src/hasco_lidar.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/vehicle_service_auto_soc", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"vehicle_service_soc\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dvehicle_service_soc_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/QXWZ/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -I/hfr/modules/HSF/vehicle_service_auto_soc/src/include -I/hfr/modules/HSF/vehicle_service_auto_soc/src/include/ge930 -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/vehicle_service_soc.dir/main.cpp.o -c /hfr/modules/HSF/vehicle_service_auto_soc/main.cpp", "file": "/hfr/modules/HSF/vehicle_service_auto_soc/main.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/vehicle_service_auto_soc", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"vehicle_service_soc\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dvehicle_service_soc_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/QXWZ/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -I/hfr/modules/HSF/vehicle_service_auto_soc/src/include -I/hfr/modules/HSF/vehicle_service_auto_soc/src/include/ge930 -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/vehicle_service_soc.dir/src/src/PubUDPService.cpp.o -c /hfr/modules/HSF/vehicle_service_auto_soc/src/src/PubUDPService.cpp", "file": "/hfr/modules/HSF/vehicle_service_auto_soc/src/src/PubUDPService.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/vehicle_service_auto_soc", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"vehicle_service_soc\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dvehicle_service_soc_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/QXWZ/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -I/hfr/modules/HSF/vehicle_service_auto_soc/src/include -I/hfr/modules/HSF/vehicle_service_auto_soc/src/include/ge930 -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/vehicle_service_soc.dir/src/src/SubUDPService.cpp.o -c /hfr/modules/HSF/vehicle_service_auto_soc/src/src/SubUDPService.cpp", "file": "/hfr/modules/HSF/vehicle_service_auto_soc/src/src/SubUDPService.cpp"}, {"directory": "/hfr/build_a1000/.cache/Release/vehicle_service_auto_soc", "command": "/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++   --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux -DHASCO_HFR_ARCH=\\\"arm64_a1000b\\\" -DMODULE_INSTALL_PATH=\\\"/hfr/build_a1000/install/arm64_a1000b/modules\\\" -DTARGET_NAME_DEF=\\\"vehicle_service_soc\\\" -DVSOMEIP_INTERNAL_SUPPRESS_DEPRECATED -Dvehicle_service_soc_EXPORTS -I/hfr/3rdparty/opencv/arm64_a1000b/include -I/hfr/lib/components_and_data_center/arm64_a1000b/include -I/hfr/3rdparty/boost/arm64_a1000b/include -I/hfr/3rdparty/yaml/arm64_a1000b/include -I/hfr/lib/hlog/arm64_a1000b/include -I/hfr/lib/iceoryx/arm64_a1000b/include -I/hfr/lib/execution_manifest/arm64_a1000b/include -I/hfr/lib/vsomeip/arm64_a1000b/include -I/hfr/interface -I/hfr/3rdparty/QXWZ/arm64_a1000b/include -I/hfr/3rdparty/openssl/arm64_a1000b/include -I/hfr/modules/HSF/vehicle_service_auto_soc/src/include -I/hfr/modules/HSF/vehicle_service_auto_soc/src/include/ge930 -O2 -pipe -g -feliminate-unused-debug-types -O3 -DNDEBUG -s -fPIC -o CMakeFiles/vehicle_service_soc.dir/src/src/UDPInterface.cpp.o -c /hfr/modules/HSF/vehicle_service_auto_soc/src/src/UDPInterface.cpp", "file": "/hfr/modules/HSF/vehicle_service_auto_soc/src/src/UDPInterface.cpp"}]