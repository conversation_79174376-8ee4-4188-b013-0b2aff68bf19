# This is the CMakeCache file.
# For build in directory: /hfr/build_a1000
# It was generated by CMake: /opt/bstos/linux-23/cmake-3.19.6/install/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Build hfr as shared libraries
BUILD_SHARED_LIBS:BOOL=ON

//Build is performed with '-Werror'
BUILD_STRICT:BOOL=OFF

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Release

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-g++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-gcc-ar

//Arguments to CXX compiler
CMAKE_CXX_COMPILER_ARG1:STRING=  --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-gcc-ranlib

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=-O2 -pipe -g -feliminate-unused-debug-types

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-gcc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-gcc-ar

//Arguments to C compiler
CMAKE_C_COMPILER_ARG1:STRING=  --sysroot=/opt/bstos/linux-23/sysroots/aarch64-bst-linux

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-gcc-ranlib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=-O2 -pipe -g -feliminate-unused-debug-types

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=hfr

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.0.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=-Wl,-O1 -Wl,--hash-style=gnu -Wl,--as-needed

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/usr/bin/aarch64-bst-linux/aarch64-bst-linux-strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//CXX compiler flags for OpenMP parallelization
OpenMP_CXX_FLAGS:STRING=-fopenmp

//CXX compiler libraries for OpenMP parallelization
OpenMP_CXX_LIB_NAMES:STRING=gomp;pthread

//C compiler flags for OpenMP parallelization
OpenMP_C_FLAGS:STRING=-fopenmp

//C compiler libraries for OpenMP parallelization
OpenMP_C_LIB_NAMES:STRING=gomp;pthread

//Path to the gomp library for OpenMP
OpenMP_gomp_LIBRARY:FILEPATH=/opt/bstos/linux-23/sysroots/aarch64-bst-linux/usr/lib/libgomp.so

//Path to the pthread library for OpenMP
OpenMP_pthread_LIBRARY:FILEPATH=/opt/bstos/linux-23/sysroots/aarch64-bst-linux/usr/lib/libpthread.so

//Value Computed by CMake
components_and_data_center_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/map_resource

//Value Computed by CMake
components_and_data_center_SOURCE_DIR:STATIC=/hfr/modules/HCR/map_resource

//Value Computed by CMake
config_service_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/config_service

//Value Computed by CMake
config_service_SOURCE_DIR:STATIC=/hfr/modules/HFR/config_service

//Value Computed by CMake
data_transfer_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/data_transfer

//Dependencies for the target
data_transfer_LIB_DEPENDS:STATIC=general;components_and_data_center;general;boost_atomic;general;boost_atomic;general;boost_chrono;general;boost_chrono;general;boost_container;general;boost_container;general;boost_context;general;boost_context;general;boost_coroutine;general;boost_coroutine;general;boost_date_time;general;boost_date_time;general;boost_exception;general;boost_fiber;general;boost_fiber;general;boost_filesystem;general;boost_filesystem;general;boost_graph;general;boost_graph;general;boost_iostreams;general;boost_iostreams;general;boost_locale;general;boost_locale;general;boost_log;general;boost_log;general;boost_log_setup;general;boost_log_setup;general;boost_math_c99;general;boost_math_c99;general;boost_math_c99f;general;boost_math_c99f;general;boost_math_c99l;general;boost_math_c99l;general;boost_math_tr1;general;boost_math_tr1;general;boost_math_tr1f;general;boost_math_tr1f;general;boost_math_tr1l;general;boost_math_tr1l;general;boost_prg_exec_monitor;general;boost_prg_exec_monitor;general;boost_program_options;general;boost_program_options;general;boost_random;general;boost_random;general;boost_regex;general;boost_regex;general;boost_serialization;general;boost_serialization;general;boost_signals;general;boost_signals;general;boost_stacktrace_basic;general;boost_stacktrace_basic;general;boost_stacktrace_noop;general;boost_stacktrace_noop;general;boost_system;general;boost_system;general;boost_test_exec_monitor;general;boost_thread;general;boost_thread;general;boost_timer;general;boost_timer;general;boost_type_erasure;general;boost_type_erasure;general;boost_unit_test_framework;general;boost_unit_test_framework;general;boost_wave;general;boost_wave;general;boost_wserialization;general;boost_wserialization;general;yaml-cpp;general;dlt;general;hlog;general;iceoryx_binding_c;general;iceoryx_hoofs;general;iceoryx_platform;general;iceoryx_posh;general;iceoryx_posh_config;general;iceoryx_posh_gateway;general;iceoryx_posh_roudi;general;em_interface;general;vsomeip3-cfg;general;vsomeip3-e2e;general;vsomeip3-sd;general;vsomeip3;general;-lpthread;

//Value Computed by CMake
data_transfer_SOURCE_DIR:STATIC=/hfr/modules/HFR/data_transfer

//Value Computed by CMake
em_service_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/execution_manifest/em_service

//Value Computed by CMake
em_service_SOURCE_DIR:STATIC=/hfr/modules/HFR/execution_manifest/em_service

//Value Computed by CMake
execution_manifest_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/execution_manifest

//Value Computed by CMake
execution_manifest_SOURCE_DIR:STATIC=/hfr/modules/HFR/execution_manifest

//Value Computed by CMake
heartControlStop_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/execution_manifest/heart_control

//Value Computed by CMake
heartControlStop_SOURCE_DIR:STATIC=/hfr/modules/HFR/execution_manifest/heart_control

//Value Computed by CMake
hfr_BINARY_DIR:STATIC=/hfr/build_a1000

//Value Computed by CMake
hfr_SOURCE_DIR:STATIC=/hfr/script

//Value Computed by CMake
hlog_service_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/hlog_service

//Value Computed by CMake
hlog_service_SOURCE_DIR:STATIC=/hfr/modules/HFR/hlog_service

//Value Computed by CMake
hpp_calib_function_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/hpp_calib_function

//Dependencies for the target
hpp_calib_function_LIB_DEPENDS:STATIC=general;components_and_data_center;general;boost_atomic;general;boost_atomic;general;boost_chrono;general;boost_chrono;general;boost_container;general;boost_container;general;boost_context;general;boost_context;general;boost_coroutine;general;boost_coroutine;general;boost_date_time;general;boost_date_time;general;boost_exception;general;boost_fiber;general;boost_fiber;general;boost_filesystem;general;boost_filesystem;general;boost_graph;general;boost_graph;general;boost_iostreams;general;boost_iostreams;general;boost_locale;general;boost_locale;general;boost_log;general;boost_log;general;boost_log_setup;general;boost_log_setup;general;boost_math_c99;general;boost_math_c99;general;boost_math_c99f;general;boost_math_c99f;general;boost_math_c99l;general;boost_math_c99l;general;boost_math_tr1;general;boost_math_tr1;general;boost_math_tr1f;general;boost_math_tr1f;general;boost_math_tr1l;general;boost_math_tr1l;general;boost_prg_exec_monitor;general;boost_prg_exec_monitor;general;boost_program_options;general;boost_program_options;general;boost_random;general;boost_random;general;boost_regex;general;boost_regex;general;boost_serialization;general;boost_serialization;general;boost_signals;general;boost_signals;general;boost_stacktrace_basic;general;boost_stacktrace_basic;general;boost_stacktrace_noop;general;boost_stacktrace_noop;general;boost_system;general;boost_system;general;boost_test_exec_monitor;general;boost_thread;general;boost_thread;general;boost_timer;general;boost_timer;general;boost_type_erasure;general;boost_type_erasure;general;boost_unit_test_framework;general;boost_unit_test_framework;general;boost_wave;general;boost_wave;general;boost_wserialization;general;boost_wserialization;general;yaml-cpp;general;dlt;general;hlog;general;iceoryx_binding_c;general;iceoryx_hoofs;general;iceoryx_platform;general;iceoryx_posh;general;iceoryx_posh_config;general;iceoryx_posh_gateway;general;iceoryx_posh_roudi;general;em_interface;general;vsomeip3-cfg;general;vsomeip3-e2e;general;vsomeip3-sd;general;vsomeip3;

//Value Computed by CMake
hpp_calib_function_SOURCE_DIR:STATIC=/hfr/modules/HFF/hpp_calib_function

//Value Computed by CMake
hpp_function_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/hpp_function

//Dependencies for the target
hpp_function_LIB_DEPENDS:STATIC=general;components_and_data_center;general;boost_atomic;general;boost_atomic;general;boost_chrono;general;boost_chrono;general;boost_container;general;boost_container;general;boost_context;general;boost_context;general;boost_coroutine;general;boost_coroutine;general;boost_date_time;general;boost_date_time;general;boost_exception;general;boost_fiber;general;boost_fiber;general;boost_filesystem;general;boost_filesystem;general;boost_graph;general;boost_graph;general;boost_iostreams;general;boost_iostreams;general;boost_locale;general;boost_locale;general;boost_log;general;boost_log;general;boost_log_setup;general;boost_log_setup;general;boost_math_c99;general;boost_math_c99;general;boost_math_c99f;general;boost_math_c99f;general;boost_math_c99l;general;boost_math_c99l;general;boost_math_tr1;general;boost_math_tr1;general;boost_math_tr1f;general;boost_math_tr1f;general;boost_math_tr1l;general;boost_math_tr1l;general;boost_prg_exec_monitor;general;boost_prg_exec_monitor;general;boost_program_options;general;boost_program_options;general;boost_random;general;boost_random;general;boost_regex;general;boost_regex;general;boost_serialization;general;boost_serialization;general;boost_signals;general;boost_signals;general;boost_stacktrace_basic;general;boost_stacktrace_basic;general;boost_stacktrace_noop;general;boost_stacktrace_noop;general;boost_system;general;boost_system;general;boost_test_exec_monitor;general;boost_thread;general;boost_thread;general;boost_timer;general;boost_timer;general;boost_type_erasure;general;boost_type_erasure;general;boost_unit_test_framework;general;boost_unit_test_framework;general;boost_wave;general;boost_wave;general;boost_wserialization;general;boost_wserialization;general;yaml-cpp;general;dlt;general;hlog;general;iceoryx_binding_c;general;iceoryx_hoofs;general;iceoryx_platform;general;iceoryx_posh;general;iceoryx_posh_config;general;iceoryx_posh_gateway;general;iceoryx_posh_roudi;general;em_interface;general;vsomeip3-cfg;general;vsomeip3-e2e;general;vsomeip3-sd;general;vsomeip3;

//Value Computed by CMake
hpp_function_SOURCE_DIR:STATIC=/hfr/modules/HFF/hpp_function

//Value Computed by CMake
hpp_hmi_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/hpp_hmi

//Dependencies for the target
hpp_hmi_LIB_DEPENDS:STATIC=general;components_and_data_center;general;boost_atomic;general;boost_atomic;general;boost_chrono;general;boost_chrono;general;boost_container;general;boost_container;general;boost_context;general;boost_context;general;boost_coroutine;general;boost_coroutine;general;boost_date_time;general;boost_date_time;general;boost_exception;general;boost_fiber;general;boost_fiber;general;boost_filesystem;general;boost_filesystem;general;boost_graph;general;boost_graph;general;boost_iostreams;general;boost_iostreams;general;boost_locale;general;boost_locale;general;boost_log;general;boost_log;general;boost_log_setup;general;boost_log_setup;general;boost_math_c99;general;boost_math_c99;general;boost_math_c99f;general;boost_math_c99f;general;boost_math_c99l;general;boost_math_c99l;general;boost_math_tr1;general;boost_math_tr1;general;boost_math_tr1f;general;boost_math_tr1f;general;boost_math_tr1l;general;boost_math_tr1l;general;boost_prg_exec_monitor;general;boost_prg_exec_monitor;general;boost_program_options;general;boost_program_options;general;boost_random;general;boost_random;general;boost_regex;general;boost_regex;general;boost_serialization;general;boost_serialization;general;boost_signals;general;boost_signals;general;boost_stacktrace_basic;general;boost_stacktrace_basic;general;boost_stacktrace_noop;general;boost_stacktrace_noop;general;boost_system;general;boost_system;general;boost_test_exec_monitor;general;boost_thread;general;boost_thread;general;boost_timer;general;boost_timer;general;boost_type_erasure;general;boost_type_erasure;general;boost_unit_test_framework;general;boost_unit_test_framework;general;boost_wave;general;boost_wave;general;boost_wserialization;general;boost_wserialization;general;yaml-cpp;general;dlt;general;hlog;general;iceoryx_binding_c;general;iceoryx_hoofs;general;iceoryx_platform;general;iceoryx_posh;general;iceoryx_posh_config;general;iceoryx_posh_gateway;general;iceoryx_posh_roudi;general;em_interface;general;vsomeip3-cfg;general;vsomeip3-e2e;general;vsomeip3-sd;general;vsomeip3;

//Value Computed by CMake
hpp_hmi_SOURCE_DIR:STATIC=/hfr/modules/HFF/hpp_hmi

//Value Computed by CMake
lidar_curb_perception_segment_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/lidar_curb_perception_segment

//Value Computed by CMake
lidar_curb_perception_segment_SOURCE_DIR:STATIC=/hfr/modules/HAF/lidar_curb_perception_segment

//Value Computed by CMake
lidar_obstacle_perception_segment_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/lidar_obstacle_perception_segment

//Value Computed by CMake
lidar_obstacle_perception_segment_SOURCE_DIR:STATIC=/hfr/modules/HAF/lidar_obstacle_perception_segment

//Value Computed by CMake
lidar_service_hasco_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/lidar_service_hasco

//Dependencies for the target
lidar_service_hasco_LIB_DEPENDS:STATIC=general;components_and_data_center;general;boost_atomic;general;boost_atomic;general;boost_chrono;general;boost_chrono;general;boost_container;general;boost_container;general;boost_context;general;boost_context;general;boost_coroutine;general;boost_coroutine;general;boost_date_time;general;boost_date_time;general;boost_exception;general;boost_fiber;general;boost_fiber;general;boost_filesystem;general;boost_filesystem;general;boost_graph;general;boost_graph;general;boost_iostreams;general;boost_iostreams;general;boost_locale;general;boost_locale;general;boost_log;general;boost_log;general;boost_log_setup;general;boost_log_setup;general;boost_math_c99;general;boost_math_c99;general;boost_math_c99f;general;boost_math_c99f;general;boost_math_c99l;general;boost_math_c99l;general;boost_math_tr1;general;boost_math_tr1;general;boost_math_tr1f;general;boost_math_tr1f;general;boost_math_tr1l;general;boost_math_tr1l;general;boost_prg_exec_monitor;general;boost_prg_exec_monitor;general;boost_program_options;general;boost_program_options;general;boost_random;general;boost_random;general;boost_regex;general;boost_regex;general;boost_serialization;general;boost_serialization;general;boost_signals;general;boost_signals;general;boost_stacktrace_basic;general;boost_stacktrace_basic;general;boost_stacktrace_noop;general;boost_stacktrace_noop;general;boost_system;general;boost_system;general;boost_test_exec_monitor;general;boost_thread;general;boost_thread;general;boost_timer;general;boost_timer;general;boost_type_erasure;general;boost_type_erasure;general;boost_unit_test_framework;general;boost_unit_test_framework;general;boost_wave;general;boost_wave;general;boost_wserialization;general;boost_wserialization;general;yaml-cpp;general;dlt;general;hlog;general;iceoryx_binding_c;general;iceoryx_hoofs;general;iceoryx_platform;general;iceoryx_posh;general;iceoryx_posh_config;general;iceoryx_posh_gateway;general;iceoryx_posh_roudi;general;em_interface;general;vsomeip3-cfg;general;vsomeip3-e2e;general;vsomeip3-sd;general;vsomeip3;

//Value Computed by CMake
lidar_service_hasco_SOURCE_DIR:STATIC=/hfr/modules/HSF/lidar_service_hasco

//Value Computed by CMake
mainboard_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/execution_manifest/mainboard

//Value Computed by CMake
mainboard_SOURCE_DIR:STATIC=/hfr/modules/HFR/execution_manifest/mainboard

//Value Computed by CMake
mqtt_service_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/MQTT_service

//Dependencies for the target
mqtt_service_LIB_DEPENDS:STATIC=general;components_and_data_center;general;boost_atomic;general;boost_atomic;general;boost_chrono;general;boost_chrono;general;boost_container;general;boost_container;general;boost_context;general;boost_context;general;boost_coroutine;general;boost_coroutine;general;boost_date_time;general;boost_date_time;general;boost_exception;general;boost_fiber;general;boost_fiber;general;boost_filesystem;general;boost_filesystem;general;boost_graph;general;boost_graph;general;boost_iostreams;general;boost_iostreams;general;boost_locale;general;boost_locale;general;boost_log;general;boost_log;general;boost_log_setup;general;boost_log_setup;general;boost_math_c99;general;boost_math_c99;general;boost_math_c99f;general;boost_math_c99f;general;boost_math_c99l;general;boost_math_c99l;general;boost_math_tr1;general;boost_math_tr1;general;boost_math_tr1f;general;boost_math_tr1f;general;boost_math_tr1l;general;boost_math_tr1l;general;boost_prg_exec_monitor;general;boost_prg_exec_monitor;general;boost_program_options;general;boost_program_options;general;boost_random;general;boost_random;general;boost_regex;general;boost_regex;general;boost_serialization;general;boost_serialization;general;boost_signals;general;boost_signals;general;boost_stacktrace_basic;general;boost_stacktrace_basic;general;boost_stacktrace_noop;general;boost_stacktrace_noop;general;boost_system;general;boost_system;general;boost_test_exec_monitor;general;boost_thread;general;boost_thread;general;boost_timer;general;boost_timer;general;boost_type_erasure;general;boost_type_erasure;general;boost_unit_test_framework;general;boost_unit_test_framework;general;boost_wave;general;boost_wave;general;boost_wserialization;general;boost_wserialization;general;yaml-cpp;general;dlt;general;hlog;general;iceoryx_binding_c;general;iceoryx_hoofs;general;iceoryx_platform;general;iceoryx_posh;general;iceoryx_posh_config;general;iceoryx_posh_gateway;general;iceoryx_posh_roudi;general;em_interface;general;vsomeip3-cfg;general;vsomeip3-e2e;general;vsomeip3-sd;general;vsomeip3;general;mosquitto;general;mosquittopp;general;crypto;general;crypto;general;ssl;general;ssl;general;cjson;general;pthread;

//Value Computed by CMake
mqtt_service_SOURCE_DIR:STATIC=/hfr/modules/HSF/MQTT_service

//Value Computed by CMake
road_boundary_perception_track_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/road_boundary_perception_track

//Value Computed by CMake
road_boundary_perception_track_SOURCE_DIR:STATIC=/hfr/modules/HAF/road_boundary_perception_track

//Value Computed by CMake
time_service_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/time_service

//Dependencies for the target
time_service_LIB_DEPENDS:STATIC=general;components_and_data_center;general;boost_atomic;general;boost_atomic;general;boost_chrono;general;boost_chrono;general;boost_container;general;boost_container;general;boost_context;general;boost_context;general;boost_coroutine;general;boost_coroutine;general;boost_date_time;general;boost_date_time;general;boost_exception;general;boost_fiber;general;boost_fiber;general;boost_filesystem;general;boost_filesystem;general;boost_graph;general;boost_graph;general;boost_iostreams;general;boost_iostreams;general;boost_locale;general;boost_locale;general;boost_log;general;boost_log;general;boost_log_setup;general;boost_log_setup;general;boost_math_c99;general;boost_math_c99;general;boost_math_c99f;general;boost_math_c99f;general;boost_math_c99l;general;boost_math_c99l;general;boost_math_tr1;general;boost_math_tr1;general;boost_math_tr1f;general;boost_math_tr1f;general;boost_math_tr1l;general;boost_math_tr1l;general;boost_prg_exec_monitor;general;boost_prg_exec_monitor;general;boost_program_options;general;boost_program_options;general;boost_random;general;boost_random;general;boost_regex;general;boost_regex;general;boost_serialization;general;boost_serialization;general;boost_signals;general;boost_signals;general;boost_stacktrace_basic;general;boost_stacktrace_basic;general;boost_stacktrace_noop;general;boost_stacktrace_noop;general;boost_system;general;boost_system;general;boost_test_exec_monitor;general;boost_thread;general;boost_thread;general;boost_timer;general;boost_timer;general;boost_type_erasure;general;boost_type_erasure;general;boost_unit_test_framework;general;boost_unit_test_framework;general;boost_wave;general;boost_wave;general;boost_wserialization;general;boost_wserialization;general;yaml-cpp;general;dlt;general;hlog;general;iceoryx_binding_c;general;iceoryx_hoofs;general;iceoryx_platform;general;iceoryx_posh;general;iceoryx_posh_config;general;iceoryx_posh_gateway;general;iceoryx_posh_roudi;general;em_interface;general;vsomeip3-cfg;general;vsomeip3-e2e;general;vsomeip3-sd;general;vsomeip3;general;-lpthread;general;-lm;

//Value Computed by CMake
time_service_SOURCE_DIR:STATIC=/hfr/modules/HFR/time_service

//Value Computed by CMake
vehicle_service_soc_BINARY_DIR:STATIC=/hfr/build_a1000/.cache/Release/vehicle_service_auto_soc

//Dependencies for the target
vehicle_service_soc_LIB_DEPENDS:STATIC=general;components_and_data_center;general;boost_atomic;general;boost_atomic;general;boost_chrono;general;boost_chrono;general;boost_container;general;boost_container;general;boost_context;general;boost_context;general;boost_coroutine;general;boost_coroutine;general;boost_date_time;general;boost_date_time;general;boost_exception;general;boost_fiber;general;boost_fiber;general;boost_filesystem;general;boost_filesystem;general;boost_graph;general;boost_graph;general;boost_iostreams;general;boost_iostreams;general;boost_locale;general;boost_locale;general;boost_log;general;boost_log;general;boost_log_setup;general;boost_log_setup;general;boost_math_c99;general;boost_math_c99;general;boost_math_c99f;general;boost_math_c99f;general;boost_math_c99l;general;boost_math_c99l;general;boost_math_tr1;general;boost_math_tr1;general;boost_math_tr1f;general;boost_math_tr1f;general;boost_math_tr1l;general;boost_math_tr1l;general;boost_prg_exec_monitor;general;boost_prg_exec_monitor;general;boost_program_options;general;boost_program_options;general;boost_random;general;boost_random;general;boost_regex;general;boost_regex;general;boost_serialization;general;boost_serialization;general;boost_signals;general;boost_signals;general;boost_stacktrace_basic;general;boost_stacktrace_basic;general;boost_stacktrace_noop;general;boost_stacktrace_noop;general;boost_system;general;boost_system;general;boost_test_exec_monitor;general;boost_thread;general;boost_thread;general;boost_timer;general;boost_timer;general;boost_type_erasure;general;boost_type_erasure;general;boost_unit_test_framework;general;boost_unit_test_framework;general;boost_wave;general;boost_wave;general;boost_wserialization;general;boost_wserialization;general;yaml-cpp;general;dlt;general;hlog;general;iceoryx_binding_c;general;iceoryx_hoofs;general;iceoryx_platform;general;iceoryx_posh;general;iceoryx_posh_config;general;iceoryx_posh_gateway;general;iceoryx_posh_roudi;general;em_interface;general;vsomeip3-cfg;general;vsomeip3-e2e;general;vsomeip3-sd;general;vsomeip3;general;qxinertial;general;qxinertial;general;crypto;general;crypto;general;ssl;general;ssl;general;pthread;

//Value Computed by CMake
vehicle_service_soc_SOURCE_DIR:STATIC=/hfr/modules/HSF/vehicle_service_auto_soc


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/hfr/build_a1000
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=19
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=6
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/opt/bstos/linux-23/cmake-3.19.6/install/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/opt/bstos/linux-23/cmake-3.19.6/install/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/opt/bstos/linux-23/cmake-3.19.6/install/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/hfr/script
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=19
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/opt/bstos/linux-23/cmake-3.19.6/install/share/cmake-3.19
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/opt/bstos/linux-23/sysroots/x86_64-bstsdk-linux/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding OpenMP
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP:INTERNAL=[TRUE][TRUE][c ][v4.5()]
//Details about finding OpenMP_C
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_C:INTERNAL=[-fopenmp][/opt/bstos/linux-23/sysroots/aarch64-bst-linux/usr/lib/libgomp.so][/opt/bstos/linux-23/sysroots/aarch64-bst-linux/usr/lib/libpthread.so][v4.5()]
//Details about finding OpenMP_CXX
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_CXX:INTERNAL=[-fopenmp][/opt/bstos/linux-23/sysroots/aarch64-bst-linux/usr/lib/libgomp.so][/opt/bstos/linux-23/sysroots/aarch64-bst-linux/usr/lib/libpthread.so][v4.5()]
//HFR_ARCH
HFR_ARCH:INTERNAL=arm64_a1000b
//HFR_CXX_STANDARD
HFR_CXX_STANDARD:INTERNAL=11
//HFR_CXX_WARNINGS
HFR_CXX_WARNINGS:INTERNAL=-W;-Wall;-Wextra;-Wuninitialized;-Wpedantic;-Wstrict-aliasing;-Wcast-align;-Wconversion;-Wno-noexcept-type;-Wuseless-cast
//HFR_C_WARNINGS
HFR_C_WARNINGS:INTERNAL=-W;-Wall;-Wextra;-Wuninitialized;-Wpedantic;-Wstrict-aliasing;-Wcast-align;-Wconversion
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_CXX_fopenmp:INTERNAL=TRUE
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_C_fopenmp:INTERNAL=TRUE
//ADVANCED property for variable: OpenMP_CXX_FLAGS
OpenMP_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_CXX_LIB_NAMES
OpenMP_CXX_LIB_NAMES-ADVANCED:INTERNAL=1
//CXX compiler's OpenMP specification date
OpenMP_CXX_SPEC_DATE:INTERNAL=201511
//ADVANCED property for variable: OpenMP_C_FLAGS
OpenMP_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_LIB_NAMES
OpenMP_C_LIB_NAMES-ADVANCED:INTERNAL=1
//C compiler's OpenMP specification date
OpenMP_C_SPEC_DATE:INTERNAL=201511
//Result of TRY_COMPILE
OpenMP_SPECTEST_CXX_:INTERNAL=TRUE
//Result of TRY_COMPILE
OpenMP_SPECTEST_C_:INTERNAL=TRUE
//ADVANCED property for variable: OpenMP_gomp_LIBRARY
OpenMP_gomp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_pthread_LIBRARY
OpenMP_pthread_LIBRARY-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/usr/local

