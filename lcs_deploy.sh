#!/bin/bash
set -e

# ----------------------------
# 脚本使用说明
# ----------------------------
# 功能：部署 lidar_curb_perception_segment 模块到A1000域控板
# 
# 用法：./deploy_lcs.sh <tar_package> <install_dir>
# 
# 参数说明：
#   tar_package    待部署的tar.gz压缩包文件路径
#   install_dir    域控端软件部署目录名（通常为install_VX.XX格式）
#                  例如：install_V1.21、install_V2.00 等
# 
# 示例：
#   ./deploy_lcs.sh lcs_a1000_bin_v1.21.1.3_12081430.tar.gz install_V1.21
#   ./deploy_lcs.sh lcs_a1000_bin_v2.00.0.1_12081500.tar.gz install_V2.00
# 
# 注意：
#   - tar包需要在前置步骤中已经完成打包
#   - 域控端IP：************，用户：root，密码：hasco123
#   - 部署路径：/userdata/hasco/{install_dir}/arm64_a1000b
#   - 如果系统未安装sshpass，将提示用户手动输入密码
# ----------------------------

# 检查参数数量
if [ $# -lt 1 ]; then
    echo "错误：缺少必要参数！"
    echo ""
    echo "用法: $0 <tar_package> [install_dir]"
    echo ""
    echo "参数说明："
    echo "  tar_package    待部署的tar.gz压缩包文件路径"
    echo "  install_dir    域控端软件部署目录名（可选，默认为install）"
    echo "                 通常为install_VX.XX格式，例如：install_V1.21、install_V2.00 等"
    echo ""
    echo "示例："
    echo "  $0 lcs_a1000_bin_v1.21.1.3_12081430.tar.gz"
    echo "  $0 lcs_a1000_bin_v1.21.1.3_12081430.tar.gz install_V1.21"
    echo "  $0 lcs_a1000_bin_v2.00.0.1_12081500.tar.gz install_V2.00"
    exit 1
fi

# ----------------------------
# 获取参数
# ----------------------------
# 获取传入的tar包路径和安装目录参数
pkg_name="$1"
# 如果没有提供install_dir参数，则使用默认值"install"
install_dir="${2:-install}"

# 检查tar包文件是否存在
if [ ! -f "$pkg_name" ]; then
    echo "错误：指定的tar包文件不存在: $pkg_name"
    exit 1
fi

echo "使用tar包: $pkg_name"
echo "安装目录: $install_dir"

# 从tar包文件名中提取目标目录名
# 假设tar包格式为: lcs_a1000_bin_v{版本号}_{日期时间}.tar.gz
# 提取不含扩展名的文件名作为目标目录名
target_dir=$(basename "$pkg_name" .tar.gz)
echo "目标目录名: $target_dir"


# ----------------------------
# A1000 域控板配置
# ----------------------------
deploy_root_dir="/userdata/hfr"
domain_controller_ip="************"
domain_controller_user="root"
domain_controller_password=" "

# 组成完整安装目录
full_install_dir="${deploy_root_dir}/${install_dir}/arm64_a1000b"
echo "完整安装目录为: $full_install_dir"

# ----------------------------
# 检查sshpass是否可用
# ----------------------------
use_sshpass=false
if command -v sshpass &> /dev/null; then
    use_sshpass=true
    echo "检测到sshpass工具，将使用自动密码认证"
else
    echo "未检测到sshpass工具，将使用交互式密码认证"
    echo "提示：您可以通过以下命令安装sshpass以实现自动认证："
    echo "  Ubuntu/Debian: sudo apt-get install sshpass"
    echo "  CentOS/RHEL:   sudo yum install sshpass"
    echo ""
fi

# ----------------------------
# 上传并解压文件到域控端
# ----------------------------
echo "正在上传文件到域控端..."
echo "目标地址: ${domain_controller_user}@${domain_controller_ip}:${full_install_dir}"

# 根据sshpass可用性选择上传方式
if [ "$use_sshpass" = true ]; then
    # 使用sshpass自动认证
    echo "使用自动密码认证上传文件..."
    sshpass -p "$domain_controller_password" scp "$pkg_name" "${domain_controller_user}@${domain_controller_ip}:${full_install_dir}/"
    upload_result=$?
else
    # 使用交互式密码认证
    echo "请输入域控端密码进行认证（密码：hasco123）："
    scp "$pkg_name" "${domain_controller_user}@${domain_controller_ip}:${full_install_dir}/"
    upload_result=$?
fi

# 检查上传结果
if [ $upload_result -eq 0 ]; then
    echo "文件上传成功！"
    echo "上传的文件: $pkg_name"
    echo "上传到: ${domain_controller_user}@${domain_controller_ip}:${full_install_dir}/"
    
    # 在域控端自动解压文件
    echo "正在域控端解压文件..."
    
    if [ "$use_sshpass" = true ]; then
        # 使用sshpass自动认证
        sshpass -p "$domain_controller_password" ssh "${domain_controller_user}@${domain_controller_ip}" \
            "cd ${full_install_dir} && tar -xzf $pkg_name && echo '解压完成: $pkg_name'"
        extract_result=$?
    else
        # 使用交互式密码认证
        echo "请再次输入域控端密码进行解压操作："
        ssh "${domain_controller_user}@${domain_controller_ip}" \
            "cd ${full_install_dir} && tar -xzf $pkg_name && echo '解压完成: $pkg_name'"
        extract_result=$?
    fi
    
    if [ $extract_result -eq 0 ]; then
        echo "文件解压成功！"
        
        # ----------------------------
        # 执行文件部署操作
        # ----------------------------
        echo "正在执行文件部署操作..."
        
        # 构建部署命令
        deploy_commands="
        # 进入解压后的目录
        cd ${full_install_dir}/${target_dir}
        
        echo '========================================='
        echo '开始执行文件部署操作'
        echo '========================================='
        
        # 1. 复制lib目录下的库文件到系统lib目录
        echo '正在复制库文件到系统lib目录...'
        if [ -d 'lib' ]; then
            echo '源lib目录内容:'
            ls -la lib/
            
            # 检查目标lib目录是否存在，不存在则创建
            if [ ! -d '${full_install_dir}/lib' ]; then
                echo '创建目标lib目录...'
                mkdir -p ${full_install_dir}/lib
            fi
            
            # 先删除目标目录中的同名前缀文件（避免版本冲突）
            echo '清理目标lib目录中的旧版本文件...'
            for src_file in lib/*.so*; do
                if [ -f \"\$src_file\" ]; then
                    # 提取文件名前缀（去掉版本号）
                    filename=\$(basename \"\$src_file\")
                    prefix=\$(echo \"\$filename\" | sed 's/\\.so.*/.so*/')
                    echo \"  清理 \$prefix 相关文件\"
                    rm -f ${full_install_dir}/lib/\$prefix
                fi
            done
            
            # 复制所有库文件和软链接（保持软链接关系）
            echo '复制新版本库文件...'
            cp -af lib/* ${full_install_dir}/lib/
            
            echo '库文件复制完成，目标lib目录内容:'
            ls -la ${full_install_dir}/lib/liblcs.so* 2>/dev/null || echo '未找到liblcs.so相关文件'
            
            # 验证软链接指向
            echo '验证软链接指向:'
            for solink in \$(find lib/ -name \"*.so\" -type l 2>/dev/null); do
                linkname=\$(basename \"\$solink\")
                if [ -L \"${full_install_dir}/lib/\$linkname\" ]; then
                    echo \"  \$linkname -> \$(readlink ${full_install_dir}/lib/\$linkname)\"
                else
                    echo \"  警告：\$linkname 软链接未找到\"
                fi
            done
        else
            echo '警告：未找到lib目录'
        fi
        
        echo ''
        echo '========================================='
        
        # 2. 替换modules目录下的lidar_curb_perception_segment模块
        echo '正在替换lidar_curb_perception_segment模块...'
        if [ -d 'lidar_curb_perception_segment' ]; then
            # 检查目标modules目录是否存在，不存在则创建
            if [ ! -d '${full_install_dir}/modules' ]; then
                echo '创建目标modules目录...'
                mkdir -p ${full_install_dir}/modules
            fi
            
            # 在复制前，验证源模块目录下bin子目录内的so库文件和软链接信息
            if [ -d 'lidar_curb_perception_segment/bin' ]; then
                echo '源模块 bin 目录内容:'
                ls -la lidar_curb_perception_segment/bin/
                
                echo '源模块 bin 目录下的so库文件:'
                find lidar_curb_perception_segment/bin/ -name \"*.so*\" -exec ls -l {} \;
                
                echo '源模块 bin 目录下的软链接:'
                find lidar_curb_perception_segment/bin/ -type l -exec ls -l {} \;
                
                # 统计so库文件数量
                src_so_count=$(find lidar_curb_perception_segment/bin/ -name \"*.so*\" | wc -l)
                echo \"  共找到 \$src_so_count 个so相关文件\"
            else
                echo '警告：源模块未包含 bin 目录'
            fi

            # 如果目标模块目录存在，先删除
            if [ -d '${full_install_dir}/modules/lidar_curb_perception_segment' ]; then
                echo '删除旧的lidar_curb_perception_segment模块...'
                rm -rf ${full_install_dir}/modules/lidar_curb_perception_segment
            fi
            
            # 复制新的模块目录
            echo '复制新版本模块目录...'
            cp -af lidar_curb_perception_segment ${full_install_dir}/modules/
            
            # 验证复制结果
            if [ -d '${full_install_dir}/modules/lidar_curb_perception_segment' ]; then
                echo '模块替换成功！'
                
                # 检查bin目录下的so库及软链接
                echo '检查bin目录下的so库及软链接:'
                if [ -d \"${full_install_dir}/modules/lidar_curb_perception_segment/bin\" ]; then
                    echo '  bin目录内容:'
                    ls -la \"${full_install_dir}/modules/lidar_curb_perception_segment/bin/\"
                    
                    # 验证so库的软链接指向
                    echo '  验证so库软链接指向:'
                    for sofile in \$(find \"${full_install_dir}/modules/lidar_curb_perception_segment/bin/\" -name \"*.so\" -type l 2>/dev/null); do
                        echo \"    \$(basename \$sofile) -> \$(readlink \$sofile)\"
                    done
                    
                    # 统计so库文件数量
                    so_count=\$(find \"${full_install_dir}/modules/lidar_curb_perception_segment/bin/\" -name \"*.so*\" | wc -l)
                    echo \"  共找到 \$so_count 个so相关文件\"
                else
                    echo '  警告：bin/ 目录不存在'
                fi    
            else
                echo '错误：模块替换失败'
                exit 1
            fi
        else
            echo '警告：未找到lidar_curb_perception_segment目录'
        fi
        
        echo ''
        echo '========================================='
        
        # 3. 清理解压后的临时目录
        echo '清理临时文件...'
        rm -rf ${full_install_dir}/${target_dir}
        rm -f ${full_install_dir}/${pkg_name}
        echo '临时文件清理完成'
        
        echo ''
        echo '========================================='
        echo '文件部署操作完成！'
        echo '========================================='
        
        # 最终验证
        echo '最终部署验证:'
        echo '1. 库文件验证:'
        if [ -f '${full_install_dir}/lib/liblcs.so' ]; then
            echo '   ✓ liblcs.so 部署成功'
        else
            echo '   ✗ liblcs.so 部署失败'
        fi
        
        echo '2. 模块验证:'
        if [ -d '${full_install_dir}/modules/lidar_curb_perception_segment' ]; then
            echo '   ✓ lidar_curb_perception_segment 模块部署成功'
            echo \"   模块大小: \$(du -sh ${full_install_dir}/modules/lidar_curb_perception_segment | cut -f1)\"
        else
            echo '   ✗ lidar_curb_perception_segment 模块部署失败'
        fi 
        "
        
        # 执行部署命令
        if [ "$use_sshpass" = true ]; then
            # 使用sshpass自动认证
            sshpass -p "$domain_controller_password" ssh "${domain_controller_user}@${domain_controller_ip}" "$deploy_commands"
            deploy_result=$?
        else
            # 使用交互式密码认证
            echo "请输入域控端密码进行文件部署操作："
            ssh "${domain_controller_user}@${domain_controller_ip}" "$deploy_commands"
            deploy_result=$?
        fi
        
        if [ $deploy_result -eq 0 ]; then
            echo "文件部署成功！"
            echo "部署完成！"
            echo ""
            echo "部署摘要："
            echo "- 压缩包: $pkg_name"
            echo "- 目标位置: ${domain_controller_user}@${domain_controller_ip}:${full_install_dir}/"
            echo "- 认证方式: $([ "$use_sshpass" = true ] && echo "自动密码认证" || echo "交互式密码认证")"
            echo "- 库文件已更新至: ${full_install_dir}/lib/"
            echo "- 模块已替换至: ${full_install_dir}/modules/lidar_curb_perception_segment/"
        else
            echo "错误：文件部署失败！"
            echo "请手动登录域控端检查部署状态或执行以下操作："
            echo "1. 检查解压后的目录结构"
            echo "2. 手动复制库文件到 ${full_install_dir}/lib/"
            echo "3. 手动替换模块到 ${full_install_dir}/modules/lidar_curb_perception_segment/"
            exit 1
        fi
    else
        echo "警告：文件解压失败，请手动检查！"
        echo "您可以手动登录域控端执行以下命令："
        echo "  ssh ${domain_controller_user}@${domain_controller_ip}"
        echo "  cd ${full_install_dir}"
        echo "  tar -xzf $pkg_name"
        exit 1
    fi
else
    echo "错误：文件上传失败！"
    echo "请检查："
    echo "1. 网络连接是否正常"
    echo "2. 域控端IP地址是否正确: ${domain_controller_ip}"
    echo "3. 用户名和密码是否正确"
    echo "4. 目标目录是否存在: ${full_install_dir}"
    exit 1
fi
