#!/usr/bin/env bash

CURR_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd -P)"
X86=0
A1000=0
DEV_CONTAINER="hpp_dev_${USER}"

function show_usage() 
{
    cat <<EOF
Usage: $0 [options] ...
OPTIONS:
	-u, --update           Delete current container, add create new container.
    -h, --help             Display this help and exit.
EOF
}

function get_container_info()
{
	docker ps -a --filter name=${1} --format "{{.${2}}} {{.Names}}" | grep "${1}" | cut -d" " -f1
}

function is_container_existed()
{
	local names=`get_container_info ${1} Names`
	if [ -n "${names}" ]; then
		echo yes;
	else
		echo no;
	fi
}

function start_container()
{
	local existed=`is_container_existed ${1}`
	if [ "${existed}" = "yes" ]; then
		local status=`get_container_info ${1} Status | grep Up`
		if [ -n "${status}" ]; then
			return
		fi
		docker start ${1}
	fi
}

function if_container_not_exist_need_update() 
{
	container_exist=0
    if docker ps -a --format '{{.Names}}' | grep -q "$@"; then
		container_exist=1
    fi
	if [ ${container_exist} = 0 ]; then
		X86=1
	fi
}

function remove_container_if_exists() 
{
    local container="$1"
    if docker ps -a --format '{{.Names}}' | grep -q "${container}"; then
        info "Removing existing Hpp container: ${container}"
        docker stop "${container}" >/dev/null
        docker rm -v -f "${container}" 2>/dev/null
    fi
}

function parse_arguments() 
{
    while [ $# -gt 0 ]; do
        local opt="$1"
        shift
        case "${opt}" in
            -x | --x86)
                X86=1
                shift
                ;;

            -a | --a1000)
                A1000=1
                shift
                ;;

            -h | --help)
                show_usage
                exit 1
                ;;
        esac
    done 
}

function delete_and_create_container()
{
	if [ "${1}" = "-x" ] ; then
		shift
	fi
	bash script/docker/dev_start.sh "$@"
}

function dev_into_container()
{
	if [ "${1}" = "-u" ] ; then
		shift
	fi
	bash script/docker/dev_into.sh "$@"
}

function make_and_install() 
{
    cd ${CURR_DIR}/build/makefile
    make DPARAMS=-DTARGET_ARCH=${TARGET_ARCH}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : compile nana, error code : ${ret}"
        make clean;
        exit ${ret}
    fi

    make install INSTALL_PREFIX=${INSTALL_PATH}
    ret=$?
    if [ ${ret} -ne 0 ]; then
        echo "Fault : install nana, error code : ${ret}"
        make clean;
        exit ${ret}
    fi
}

function main() 
{
    parse_arguments "$@"
	if_container_not_exist_need_update "${DEV_CONTAINER}"

	if [ ${X86} = 1 ]; then
		delete_and_create_container "$@"
    elif [ ${A1000} = 1 ]; then
        bash script/docker/dev_a1000.sh "$@"
        exit 0
	fi

	start_container "${DEV_CONTAINER}"
    dev_into_container "$@"
    exit 0
}


main "$@"